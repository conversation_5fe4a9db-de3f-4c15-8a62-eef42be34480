{"name": "roots/bedrock", "type": "project", "license": "MIT", "description": "WordPress boilerplate with Composer, easier configuration, and an improved folder structure", "homepage": "https://roots.io/bedrock/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/swalkinshaw"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/retlehs"}], "keywords": ["bedrock", "composer", "roots", "wordpress", "wp", "wp-config"], "support": {"issues": "https://github.com/roots/bedrock/issues", "forum": "https://discourse.roots.io/category/bedrock"}, "repositories": [{"type": "composer", "url": "https://wpackagist.org", "only": ["wpackagist-plugin/*", "wpackagist-theme/*"]}], "require": {"php": ">=8.1", "composer/installers": "^2.2", "vlucas/phpdotenv": "^5.5", "oscarotero/env": "^2.1", "roots/bedrock-autoloader": "^1.0", "roots/bedrock-disallow-indexing": "^2.0", "roots/wordpress": "6.8.2", "roots/wp-config": "1.0.0", "wpackagist-theme/twentytwentyfive": "^1.0", "wpackagist-plugin/query-monitor": "^3", "wpackagist-plugin/health-check": "^1", "wpackagist-plugin/wp-mail-logging": "^1", "wpackagist-plugin/two-factor": "^0.8", "wpackagist-plugin/the-events-calendar": "^6", "wpackagist-plugin/bbpress": "^2.6", "wpackagist-plugin/advanced-ads": "^1", "wpackagist-plugin/wpforo": "^2.4"}, "require-dev": {"roave/security-advisories": "dev-latest", "laravel/pint": "^1.18"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "allow-plugins": {"composer/installers": true, "roots/wordpress-core-installer": true}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"installer-paths": {"web/app/mu-plugins/{$name}/": ["type:wordpress-muplugin"], "web/app/plugins/{$name}/": ["type:wordpress-plugin"], "web/app/themes/{$name}/": ["type:wordpress-theme"]}, "wordpress-install-dir": "web/wp"}, "scripts": {"lint": "pint --test", "lint:fix": "pint"}}