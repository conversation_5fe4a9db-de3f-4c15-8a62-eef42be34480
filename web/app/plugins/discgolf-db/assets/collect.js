(function(){
  const btn = document.querySelector('.dgdb-collect-btn');
  if (!btn) return;

  const status = document.querySelector('.dgdb-collect-status');
  const discId = parseInt(btn.getAttribute('data-disc'),10) || 0;

  const ajaxUrl =
    (window.DGDB && DGDB.ajax) ||
    (window.ajaxurl) ||
    (document.querySelector('script[data-ajaxurl]')?.getAttribute('data-ajaxurl')) ||
    '/wp-admin/admin-ajax.php';

  async function toggle() {
    if (!(window.DGDB && DGDB.loggedIn)) { alert('Please log in.'); return; }
    if (!discId) return;

    btn.disabled = true;
    if (status) status.textContent = 'Saving…';

    try {
      const fd = new FormData();
      fd.append('action','dgdb_collection_toggle');
      fd.append('nonce', (window.DGDB && DGDB.nonce) ? DGDB.nonce : '');
      fd.append('disc_id', String(discId));

      const res = await fetch(ajaxUrl, { method:'POST', body: fd, credentials:'same-origin' });
      if (!res.ok) throw new Error('HTTP '+res.status);

      const data = await res.json();

      if (data && data.success) {
        // Support both `state` and legacy `status`
        const state = (data.data && (data.data.state || data.data.status)) || '';
        const added = state === 'added';
        btn.setAttribute('data-state', added ? 'in' : 'out');
        btn.textContent = added ? 'Remove from my collection' : 'Add to my collection';
        if (status) status.textContent = added ? 'Added ✓' : 'Removed';
      } else {
        if (status) status.textContent = (data && data.data && data.data.message) ? data.data.message : 'Error';
      }
    } catch(e){
      if (status) status.textContent = 'AJAX failed';
      console.warn('DGDB collect error:', e, 'url:', ajaxUrl);
    } finally {
      btn.disabled = false;
    }
  }

  btn.addEventListener('click', (e)=>{ e.preventDefault(); toggle(); });
})();
