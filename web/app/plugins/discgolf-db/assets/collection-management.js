/**
 * Collection Management JavaScript
 * Handles personal disc information modal, collection controls, and AJAX operations
 */

(function() {
    'use strict';

    // ---- one-time init guard (avoid double binding) ----
    if (window.__DGDB_COLLECTION_INIT__) return;
    window.__DGDB_COLLECTION_INIT__ = true;

    // Global state
    let currentCollectionId = null;
    let currentDiscData = null;
    // Normalize favorites to strings to avoid "123" vs 123 mismatches
    let favorites = (() => {
        try {
            const raw = JSON.parse(localStorage.getItem('dgdb_favorites') || '[]');
            return Array.isArray(raw) ? raw.map(String) : [];
        } catch (_) { return []; }
    })();
    let draggedElement = null;

    // DOM refs (filled on init)
    let modal = null;
    let modalTitle = null;
    let viewMode = null;
    let editMode = null;
    let editForm = null;

    // Init now if DOM is ready; else wait
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCollectionManagement);
    } else {
        initializeCollectionManagement();
    }

    function initializeCollectionManagement() {
        // Grab modal pieces if present
        modal      = document.getElementById('dgdb-personal-disc-modal');
        modalTitle = document.getElementById('dgdb-modal-title');
        viewMode   = document.getElementById('dgdb-personal-view');
        editMode   = document.getElementById('dgdb-personal-edit');
        editForm   = document.getElementById('dgdb-personal-form');

        // Always bind these (don’t depend on modal existence)
        bindCollectionControls();
        bindCollectionFilters();
        initializeDragAndDrop();
        initializeFavorites();

        // Bind modal-related controls if modal exists
        if (modal) {
            bindModalDelegatedHandlers();
            bindFormHandlers();
        }
    }

    /* --------------------------------
     * Collection controls (cards)
     * -------------------------------- */
    function bindCollectionControls() {
        // Open modal when clicking card (but not its control buttons)
        document.addEventListener('click', function(e) {
            const card = e.target.closest('.dgdb-collection-card');
            if (!card) return;
            // ignore clicks on control buttons within a card
            if (e.target.closest('.dgdb-collection-controls')) return;

            // Only try to open if modal exists
            if (!modal) return;
            e.preventDefault();
            openPersonalDiscModal(card);
        });

        // Delete
        document.addEventListener('click', function(e) {
            const btn = e.target.closest('.dgdb-delete-btn');
            if (!btn) return;
            e.preventDefault();
            e.stopPropagation();
            const collectionId = toId(btn.dataset.collectionId);
            if (collectionId && confirm('Are you sure you want to delete this disc from your collection?')) {
                deleteCollectionItem(collectionId);
            }
        });

        // Duplicate
        document.addEventListener('click', function(e) {
            const btn = e.target.closest('.dgdb-duplicate-btn');
            if (!btn) return;
            e.preventDefault();
            e.stopPropagation();
            const collectionId = toId(btn.dataset.collectionId);
            if (collectionId) duplicateCollectionItem(collectionId);
        });

        // Favorite
        document.addEventListener('click', function(e) {
            const btn = e.target.closest('.dgdb-favorite-btn');
            if (!btn) return;
            e.preventDefault();
            e.stopPropagation();
            const collectionId = toId(btn.dataset.collectionId);
            if (collectionId) toggleFavorite(collectionId);
        });
    }

    /* --------------------------------
     * Modal event delegation
     * -------------------------------- */
    function bindModalDelegatedHandlers() {
        // One delegated click handler for all modal buttons (survives re-renders)
        modal.addEventListener('click', function(e) {
            // Close (X or backdrop)
            if (e.target.closest('.dgdb-modal-close') || e.target.classList.contains('dgdb-modal-backdrop')) {
                e.preventDefault();
                closeModal();
                return;
            }

            // Switch to edit (button rendered in view mode markup)
            if (e.target.closest('#dgdb-edit-personal-btn')) {
                e.preventDefault();
                switchToEditMode();
                return;
            }

            // Cancel edit
            if (e.target.closest('#dgdb-cancel-edit-btn')) {
                e.preventDefault();
                switchToViewMode();
                return;
            }

            // Save
            if (e.target.closest('#dgdb-save-personal-btn')) {
                e.preventDefault();
                savePersonalInfo();
                return;
            }
        });

        // ESC to close
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal && modal.style.display !== 'none') {
                closeModal();
            }
        });
    }

    function bindFormHandlers() {
        // Handle Enter key submit in the form (prevents page reload)
        if (editForm) {
            editForm.addEventListener('submit', function(e) {
                e.preventDefault();
                savePersonalInfo();
            });
        }

        // Image upload
        const imageUpload = document.getElementById('dgdb-image-upload');
        if (imageUpload) {
            imageUpload.addEventListener('change', function(e) {
                if (e.target.files && e.target.files[0]) {
                    uploadPersonalImage(e.target.files[0]);
                }
            });
        }

        // Remove image
        document.getElementById('dgdb-remove-image')?.addEventListener('click', function(e) {
            e.preventDefault();
            removePersonalImage();
        });

        // Add custom field
        document.getElementById('dgdb-add-custom-field')?.addEventListener('click', function(e) {
            e.preventDefault();
            addCustomFieldRow();
        });

        // Dynamic remove custom field (delegated to modal)
        modal.addEventListener('click', function(e) {
            const rm = e.target.closest('.dgdb-remove-field-btn');
            if (!rm) return;
            e.preventDefault();
            const row = rm.closest('.dgdb-custom-field-row');
            if (row) row.remove();

            const container = document.getElementById('dgdb-custom-fields-container');
            if (container && container.querySelectorAll('.dgdb-custom-field-row').length === 0) {
                showCustomFieldsEmptyState();
            }
        });
    }

    /* --------------------------------
     * Modal flow
     * -------------------------------- */
    function openPersonalDiscModal(card) {
        const discData = JSON.parse(card.dataset.disc || '{}');
        currentCollectionId = toId(discData.collection_id);
        currentDiscData = discData;

        if (!currentCollectionId) {
            console.error('No collection ID found');
            return;
        }

        // Set modal title
        modalTitle.textContent = `${discData.name} - ${discData.manufacturer}`;

        // Load personal information
        loadPersonalInfo();

        // Show modal
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }

    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = '';
        currentCollectionId = null;
        currentDiscData = null;
    }

    function switchToViewMode() {
        if (!viewMode || !editMode) return;
        viewMode.style.display = 'block';
        editMode.style.display = 'none';
    }

    function switchToEditMode() {
        if (!viewMode || !editMode) return;
        viewMode.style.display = 'none';
        editMode.style.display = 'block';
        populateEditForm();
    }

    function loadPersonalInfo() {
        if (!viewMode) return;
        // Show loading state
        viewMode.innerHTML = '<div class="dgdb-loading">Loading...</div>';

        const formData = new FormData();
        formData.append('action', 'dgdb_personal_disc_get');
        formData.append('nonce', window.dgdb_nonce || '');
        formData.append('collection_id', currentCollectionId);

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                displayPersonalInfo(data.data.personal_info);
            } else {
                console.error('Failed to load personal info:', data?.data);
                displayPersonalInfo(null); // Show empty state
            }
        })
        .catch(error => {
            console.error('Error loading personal info:', error);
            displayPersonalInfo(null);
        });
    }

    function displayPersonalInfo(personalInfo) {
        const hasData = !!(personalInfo && (
            personalInfo.custom_image_id ||
            personalInfo.plastic_type ||
            personalInfo.weight ||
            personalInfo.personal_notes ||
            (personalInfo.custom_fields && personalInfo.custom_fields.length)
        ));

        let html = '';
        if (hasData) {
            html = `
                <div class="dgdb-personal-display">
                    <div class="dgdb-personal-image-display">
                        ${personalInfo.custom_image_url
                            ? `<img id="dgdb-display-image" src="${personalInfo.custom_image_url}" alt="Personal disc image">`
                            : '<div id="dgdb-display-placeholder" class="dgdb-image-placeholder">No custom image</div>'}
                    </div>
                    <div class="dgdb-personal-info-display">
                        <div class="dgdb-info-row"><strong>Plastic Type:</strong> <span id="dgdb-display-plastic">${personalInfo.plastic_type || '-'}</span></div>
                        <div class="dgdb-info-row"><strong>Weight:</strong> <span id="dgdb-display-weight">${personalInfo.weight ? personalInfo.weight + 'g' : '-'}</span></div>
                        <div class="dgdb-info-row"><strong>Notes:</strong> <div id="dgdb-display-notes">${personalInfo.personal_notes || '-'}</div></div>
                        ${personalInfo.custom_fields && personalInfo.custom_fields.length
                            ? personalInfo.custom_fields.map(field => `
                                <div class="dgdb-info-row">
                                    <strong>${field.name}:</strong> <span>${field.value || '-'}</span>
                                </div>`).join('')
                            : ''}
                    </div>
                </div>
                <div class="dgdb-modal-actions">
                    <button type="button" id="dgdb-edit-personal-btn" class="dgdb-btn dgdb-btn-primary">Edit Information</button>
                </div>`;
        } else {
            html = `
                <div class="dgdb-personal-display">
                    <div class="dgdb-personal-image-display">
                        <div class="dgdb-image-placeholder">No custom image</div>
                    </div>
                    <div class="dgdb-personal-info-display">
                        <p>No personal information added yet.</p>
                        <p class="dgdb-subtle">Add custom details to personalize this disc in your collection.</p>
                    </div>
                </div>
                <div class="dgdb-modal-actions">
                    <button type="button" id="dgdb-edit-personal-btn" class="dgdb-btn dgdb-btn-primary">Add Information</button>
                </div>`;
        }

        viewMode.innerHTML = html;
        // No need to bind here; we use delegated clicks on the modal.
        // Stash for edit mode
        window.currentPersonalInfo = personalInfo;
    }

    function populateEditForm() {
        const personalInfo = window.currentPersonalInfo || {};

        document.getElementById('dgdb-form-collection-id').value = currentCollectionId || '';
        document.getElementById('dgdb-plastic-type').value = personalInfo.plastic_type || '';
        document.getElementById('dgdb-weight').value = personalInfo.weight || '';
        document.getElementById('dgdb-personal-notes').value = personalInfo.personal_notes || '';
        document.getElementById('dgdb-custom-image-id').value = personalInfo.custom_image_id || '';

        // Show current image if exists
        const currentImageDiv = document.getElementById('dgdb-current-image');
        const imagePreview = document.getElementById('dgdb-form-image-preview');

        if (personalInfo.custom_image_url) {
            imagePreview.src = personalInfo.custom_image_url;
            currentImageDiv.style.display = 'block';
        } else {
            currentImageDiv.style.display = 'none';
        }

        // Populate custom fields
        const container = document.getElementById('dgdb-custom-fields-container');
        container.innerHTML = '';

        if (personalInfo.custom_fields && personalInfo.custom_fields.length > 0) {
            personalInfo.custom_fields.forEach(field => {
                addCustomFieldRow(field.name, field.value, field.type || 'text');
            });
        } else {
            showCustomFieldsEmptyState();
        }
    }

    /* --------------------------------
     * Save / Upload / Remove (AJAX)
     * -------------------------------- */
    function savePersonalInfo() {
        const formData = new FormData();
        formData.append('action', 'dgdb_personal_disc_save');
        formData.append('nonce', window.dgdb_nonce || '');
        formData.append('collection_id', currentCollectionId);

        // Basic fields
        formData.append('custom_image_id', document.getElementById('dgdb-custom-image-id').value);
        formData.append('plastic_type', document.getElementById('dgdb-plastic-type').value);
        formData.append('weight', document.getElementById('dgdb-weight').value);
        formData.append('personal_notes', document.getElementById('dgdb-personal-notes').value);

        // Collect custom fields
        const customFields = [];
        const fieldRows = document.querySelectorAll('.dgdb-custom-field-row');
        fieldRows.forEach(row => {
            const name = row.querySelector('.dgdb-field-name').value.trim();
            const value = row.querySelector('.dgdb-field-value').value.trim();
            const type = row.querySelector('.dgdb-field-type').value;
            if (name && value) customFields.push({ name, value, type });
        });
        formData.append('custom_fields', JSON.stringify(customFields));

        // Show saving state
        const saveBtn = document.getElementById('dgdb-save-personal-btn');
        const originalText = saveBtn ? saveBtn.textContent : '';
        if (saveBtn) {
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
        }

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                // Reload personal info and switch to view mode
                loadPersonalInfo();
                switchToViewMode();
                // Refresh the collection card to show updated info
                refreshCollectionCard();
                showNotification('Personal disc information saved successfully!', 'success');
            } else {
                console.error('Failed to save personal info:', data?.data);
                showNotification('Failed to save information. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving personal info:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            if (saveBtn) {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
            }
        });
    }

    function uploadPersonalImage(file) {
        const formData = new FormData();
        formData.append('action', 'dgdb_personal_disc_upload');
        formData.append('nonce', window.dgdb_nonce || '');
        formData.append('collection_id', currentCollectionId);
        formData.append('file', file);

        // Show uploading state
        const uploadInput = document.getElementById('dgdb-image-upload');
        if (uploadInput) uploadInput.disabled = true;

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', { method: 'POST', body: formData })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                // Update form with new image
                document.getElementById('dgdb-custom-image-id').value = data.data.attachment_id;
                document.getElementById('dgdb-form-image-preview').src = data.data.url;
                document.getElementById('dgdb-current-image').style.display = 'block';
                showNotification('Image uploaded successfully!', 'success');
            } else {
                console.error('Failed to upload image:', data?.data);
                showNotification('Failed to upload image. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error uploading image:', error);
            showNotification('An error occurred while uploading. Please try again.', 'error');
        })
        .finally(() => {
            if (uploadInput) {
                uploadInput.disabled = false;
                uploadInput.value = ''; // Reset file input
            }
        });
    }

    function removePersonalImage() {
        document.getElementById('dgdb-custom-image-id').value = '';
        const currentImageDiv = document.getElementById('dgdb-current-image');
        if (currentImageDiv) currentImageDiv.style.display = 'none';
        showNotification('Image removed. Save to apply changes.', 'info');
    }

    function deleteCollectionItem(collectionId) {
        const formData = new FormData();
        formData.append('action', 'dgdb_collection_delete');
        formData.append('nonce', window.dgdb_nonce || '');
        formData.append('collection_id', collectionId);

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', { method: 'POST', body: formData })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                // Remove the card from DOM
                const card = document.querySelector(`[data-collection-id="${cssEsc(collectionId)}"]`);
                if (card) card.remove();
                showNotification('Disc removed from collection!', 'success');

                // Close modal if it's open for this item
                if (currentCollectionId == collectionId && modal) closeModal();
            } else {
                console.error('Failed to delete item:', data?.data);
                showNotification('Failed to delete disc. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting item:', error);
            showNotification('An error occurred. Please try again.', 'error');
        });
    }

    function duplicateCollectionItem(collectionId) {
        const formData = new FormData();
        formData.append('action', 'dgdb_collection_duplicate');
        formData.append('nonce', window.dgdb_nonce || '');
        formData.append('collection_id', collectionId);

        fetch(window.ajaxurl || '/wp-admin/admin-ajax.php', { method: 'POST', body: formData })
        .then(response => response.json())
        .then(data => {
            if (data && data.success) {
                showNotification('Disc duplicated successfully!', 'success');
                window.location.reload();
            } else {
                console.error('Failed to duplicate item:', data?.data);
                showNotification('Failed to duplicate disc. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error duplicating item:', error);
            showNotification('An error occurred. Please try again.', 'error');
        });
    }

    function refreshCollectionCard() {
        const card = document.querySelector(`[data-collection-id="${cssEsc(currentCollectionId)}"]`);
        if (!card) return;
        if (!card.classList.contains('dgdb-card--personalized')) {
            card.classList.add('dgdb-card--personalized');
            const avatar = card.querySelector('.dgdb-card-avatar');
            if (avatar && !avatar.querySelector('.dgdb-personalized-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'dgdb-personalized-indicator';
                indicator.title = 'Personalized disc';
                indicator.textContent = '★';
                avatar.appendChild(indicator);
            }
        }
    }

    /* --------------------------------
     * Favorites
     * -------------------------------- */
    function initializeFavorites() {
        favorites.forEach(id => {
            const card = document.querySelector(`[data-collection-id="${cssEsc(id)}"]`);
            const btn = card?.querySelector('.dgdb-favorite-btn');
            const indicator = card?.querySelector('.dgdb-favorite-indicator');
            if (card && btn) {
                card.classList.add('favorited');
                btn.classList.add('favorited');
                btn.title = 'Remove from favorites';
                if (indicator) indicator.style.display = 'flex';
            }
        });
    }

    function toggleFavorite(collectionId) {
        collectionId = toId(collectionId);
        const card = document.querySelector(`[data-collection-id="${cssEsc(collectionId)}"]`);
        const btn = card?.querySelector('.dgdb-favorite-btn');
        const indicator = card?.querySelector('.dgdb-favorite-indicator');
        if (!card || !btn) return;

        const idx = favorites.indexOf(collectionId);
        const isFavorited = idx !== -1;

        if (isFavorited) {
            favorites.splice(idx, 1);
            card.classList.remove('favorited');
            btn.classList.remove('favorited');
            btn.title = 'Add to favorites';
            if (indicator) indicator.style.display = 'none';
            showNotification('Removed from favorites', 'info');
        } else {
            favorites.push(collectionId);
            card.classList.add('favorited');
            btn.classList.add('favorited');
            btn.title = 'Remove from favorites';
            if (indicator) indicator.style.display = 'flex';
            showNotification('Added to favorites', 'success');
        }

        localStorage.setItem('dgdb_favorites', JSON.stringify(favorites));

        if (document.getElementById('dgdb-toggle-favorites')?.classList.contains('active')) {
            filterCollection();
        }
    }

    /* --------------------------------
     * Filters / sorting
     * -------------------------------- */
    function bindCollectionFilters() {
        const searchInput = document.getElementById('dgdb-collection-search');
        const manufacturerSelect = document.getElementById('dgdb-collection-manufacturer');
        const sortSelect = document.getElementById('dgdb-collection-sort');
        const favoritesBtn = document.getElementById('dgdb-toggle-favorites');

        if (searchInput) searchInput.addEventListener('input', debounce(filterCollection, 300));
        if (manufacturerSelect) manufacturerSelect.addEventListener('change', filterCollection);
        if (sortSelect) sortSelect.addEventListener('change', sortCollection);
        if (favoritesBtn) {
            favoritesBtn.addEventListener('click', function() {
                this.classList.toggle('active');
                filterCollection();
            });
        }
    }

    function filterCollection() {
        const searchTerm = (document.getElementById('dgdb-collection-search')?.value || '').toLowerCase();
        const selectedManufacturer = document.getElementById('dgdb-collection-manufacturer')?.value || '';
        const showOnlyFavorites = document.getElementById('dgdb-toggle-favorites')?.classList.contains('active') || false;

        const cards = document.querySelectorAll('.dgdb-collection-card');
        let visibleCount = 0;

        cards.forEach(card => {
            const discData = JSON.parse(card.dataset.disc || '{}');
            const collectionId = toId(card.dataset.collectionId);

            const matchesSearch = !searchTerm ||
                (discData.name || '').toLowerCase().includes(searchTerm) ||
                (discData.manufacturer || '').toLowerCase().includes(searchTerm);

            const matchesManufacturer = !selectedManufacturer ||
                discData.manufacturer === selectedManufacturer;

            const matchesFavorites = !showOnlyFavorites ||
                favorites.includes(collectionId);

            const isVisible = matchesSearch && matchesManufacturer && matchesFavorites;

            card.style.display = isVisible ? 'block' : 'none';
            if (isVisible) visibleCount++;
        });

        const countElement = document.querySelector('.dgdb-collection-count');
        if (countElement) {
            const totalCount = cards.length;
            countElement.textContent = visibleCount === totalCount
                ? `(${totalCount} discs)`
                : `(${visibleCount} of ${totalCount} discs)`;
        }
    }

    function sortCollection() {
        const sortValue = document.getElementById('dgdb-collection-sort')?.value || 'newest';
        const grid = document.querySelector('.dgdb-collection-grid');
        if (!grid) return;

        const cards = Array.from(grid.querySelectorAll('.dgdb-collection-card'));

        cards.sort((a, b) => {
            const dataA = JSON.parse(a.dataset.disc || '{}');
            const dataB = JSON.parse(b.dataset.disc || '{}');

            switch (sortValue) {
                case 'oldest':
                    return new Date(dataA.created_at || 0) - new Date(dataB.created_at || 0);
                case 'az':
                    return (dataA.name || '').localeCompare(dataB.name || '');
                case 'za':
                    return (dataB.name || '').localeCompare(dataA.name || '');
                case 'manufacturer':
                    return (dataA.manufacturer || '').localeCompare(dataB.manufacturer || '') ||
                           (dataA.name || '').localeCompare(dataB.name || '');
                case 'favorites': {
                    const aFav = favorites.includes(toId(a.dataset.collectionId));
                    const bFav = favorites.includes(toId(b.dataset.collectionId));
                    if (aFav && !bFav) return -1;
                    if (!aFav && bFav) return 1;
                    return (dataA.name || '').localeCompare(dataB.name || '');
                }
                case 'newest':
                default:
                    return new Date(dataB.created_at || 0) - new Date(dataA.created_at || 0);
            }
        });

        cards.forEach(card => grid.appendChild(card));
    }

    /* --------------------------------
     * Drag & drop (cards)
     * -------------------------------- */
    function initializeDragAndDrop() {
        const cards = document.querySelectorAll('.dgdb-collection-card');

        cards.forEach(card => {
            card.draggable = true;

            card.addEventListener('dragstart', function(e) {
                draggedElement = this;
                this.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/plain', toId(this.dataset.collectionId));
            });

            card.addEventListener('dragend', function() {
                this.classList.remove('dragging');
                draggedElement = null;

                // Remove all drag-over classes
                document.querySelectorAll('.dgdb-collection-card').forEach(c => c.classList.remove('drag-over'));
            });

            card.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                if (this !== draggedElement) this.classList.add('drag-over');
            });

            card.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });

            card.addEventListener('drop', function(e) {
                e.preventDefault();

                if (this !== draggedElement && draggedElement) {
                    const grid = this.parentNode;
                    const allCards = Array.from(grid.children);
                    const draggedIndex = allCards.indexOf(draggedElement);
                    const targetIndex  = allCards.indexOf(this);

                    if (draggedIndex < targetIndex) {
                        grid.insertBefore(draggedElement, this.nextSibling);
                    } else {
                        grid.insertBefore(draggedElement, this);
                    }

                    showNotification('Collection order updated', 'success');
                }

                this.classList.remove('drag-over');
            });
        });
    }

    /* --------------------------------
     * Utilities and public API
     * -------------------------------- */
    function debounce(fn, wait) {
        let t;
        return (...args) => {
            clearTimeout(t);
            t = setTimeout(() => fn(...args), wait);
        };
    }

    function toId(v) {
        if (v == null) return '';
        return String(v);
    }

    // minimal escape for attribute selector
    function cssEsc(s) {
        return String(s).replace(/"/g, '\\"');
    }

    function addCustomFieldRow(name = '', value = '', type = 'text') {
        const container = document.getElementById('dgdb-custom-fields-container');

        // Remove empty state if present
        const emptyState = container?.querySelector('.dgdb-custom-fields-empty');
        if (emptyState) emptyState.remove();

        const fieldRow = document.createElement('div');
        fieldRow.className = 'dgdb-custom-field-row';
        fieldRow.innerHTML = `
            <div class="dgdb-field-group">
                <label>Field Name</label>
                <input type="text" class="dgdb-field-name" value="${name}" placeholder="e.g., Weight, Condition, Date">
            </div>
            <div class="dgdb-field-group">
                <label>Value</label>
                <input type="text" class="dgdb-field-value" value="${value}" placeholder="e.g., 175g, 9/10, 2024-01-15">
            </div>
            <div class="dgdb-field-group dgdb-field-type-group">
                <label>Type</label>
                <select class="dgdb-field-type">
                    <option value="text" ${type === 'text' ? 'selected' : ''}>Text</option>
                    <option value="number" ${type === 'number' ? 'selected' : ''}>Number</option>
                </select>
            </div>
            <button type="button" class="dgdb-remove-field-btn" title="Remove field">×</button>
        `;
        container.appendChild(fieldRow);

        // Focus the name input for new fields
        if (!name) fieldRow.querySelector('.dgdb-field-name').focus();
    }

    function showCustomFieldsEmptyState() {
        const container = document.getElementById('dgdb-custom-fields-container');
        if (container && container.children.length === 0) {
            container.innerHTML = `
                <div class="dgdb-custom-fields-empty">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" opacity="0.5">
                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                        <line x1="12" y1="14" x2="12" y2="18"></line>
                        <line x1="10" y1="16" x2="14" y2="16"></line>
                    </svg>
                    <span>No custom fields yet</span>
                    <small>Click the + button to add personalized information</small>
                </div>
            `;
        }
    }

    // Export functions for external use (unchanged API)
    window.DGDB_CollectionManagement = {
        openPersonalDiscModal,
        closeModal,
        deleteCollectionItem,
        duplicateCollectionItem,
        showNotification,
        toggleFavorite,
        filterCollection,
        sortCollection
    };

    // Notifications (kept as-is)
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `dgdb-notification dgdb-notification--${type}`;
        notification.textContent = message;

        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            maxWidth: '300px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        });

        const colors = { success: '#28a745', error: '#dc3545', info: '#17a2b8' };
        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);
        setTimeout(() => { notification.remove(); }, 4000);
    }

})();
