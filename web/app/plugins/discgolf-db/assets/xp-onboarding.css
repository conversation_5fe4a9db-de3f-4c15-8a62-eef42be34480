/* =====================================================================
   Disc Codex – XP Onboarding (theme-aware)
   Uses site variables:
   --wp--preset--color--bg, --surface, --ink, --muted, --accent, --dg-border
   ===================================================================== */

/* Shortcuts derived from your theme variables */
:root{
  --dg-bg: var(--wp--preset--color--bg);
  --dg-surface: var(--wp--preset--color--surface);
  --dg-ink: var(--wp--preset--color--ink);
  --dg-muted: var(--wp--preset--color--muted);
  --dg-accent: var(--wp--preset--color--accent);
  /* overlay: a bit darker in dark mode (overridden below) */
  --dg-overlay: rgba(10, 14, 22, .55);
  --dg-chip-bg: color-mix(in srgb, var(--dg-accent) 14%, transparent);
  --dg-chip-border: color-mix(in srgb, var(--dg-accent) 36%, #0000);
}

:root[data-theme="dark"]{
  --dg-overlay: rgba(0, 0, 0, .7);
  --dg-chip-bg: color-mix(in srgb, var(--dg-accent) 18%, transparent);
  --dg-chip-border: color-mix(in srgb, var(--dg-accent) 42%, #0000);
}

/* Enable UA adjustments for forms/scrollbars in both modes */
html { color-scheme: light dark; }

/* -------------------------------------------------------
   Overlay shell
   ------------------------------------------------------- */
.dgdb-onb-root[hidden] { display: none !important; }

.dgdb-onb-overlay {
  position: fixed; inset: 0; z-index: 9999;
  background: var(--dg-overlay);
  display: grid; place-items: center;
  backdrop-filter: blur(2px);
}

.dgdb-onb {
  width: min(960px, 92vw);
  min-height: 420px;
  background: var(--dg-surface);
  color: var(--dg-ink);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0,0,0,.18);
  border: 1px solid var(--dg-border);
  overflow: hidden;
  display: grid;
  grid-template-rows: auto 1fr auto;
  transform: translateY(14px);
  animation: onb-pop .26s ease-out forwards;
}
@keyframes onb-pop { to { transform: translateY(0) } }

/* -------------------------------------------------------
   Header
   ------------------------------------------------------- */
.dgdb-onb-head {
  display: flex; align-items: center; justify-content: space-between;
  padding: 14px 16px;
  border-bottom: 1px solid var(--dg-border);
  background: var(--dg-bg);
  backdrop-filter: saturate(180%) blur(8px);
}
.dgdb-onb-title { margin: 0; font-size: 1.1rem; font-weight: 700; }
.dgdb-onb-close {
  border: 1px solid var(--dg-border);
  background: var(--dg-bg);
  color: var(--dg-ink);
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  width: 36px; height: 32px; border-radius: 10px;
}

/* -------------------------------------------------------
   Body (sidebar + content)
   ------------------------------------------------------- */
.dgdb-onb-body {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 16px;
  padding: 12px 16px 16px;
}
@media (max-width: 780px) {
  .dgdb-onb-body { grid-template-columns: 1fr; }
}

/* Sidebar steps */
.dgdb-onb-steps { list-style: none; padding: 0; margin: 0; }
.dgdb-onb-step {
  display: flex; align-items: center; gap: 10px;
  padding: 10px 10px; border-radius: 10px; cursor: pointer;
  color: var(--dg-ink);
  border: 1px solid transparent;
}
.dgdb-onb-step:hover {
  background: var(--dg-surface);
  border-color: var(--dg-border);
}
.dgdb-onb-step[aria-current="step"] {
  background: color-mix(in srgb, var(--dg-accent) 10%, var(--dg-surface));
  border-color: color-mix(in srgb, var(--dg-accent) 35%, var(--dg-border));
  box-shadow: inset 0 0 0 1px color-mix(in srgb, var(--dg-accent) 35%, transparent);
}
.dgdb-onb-step .idx {
  width: 26px; height: 26px; display: grid; place-items: center;
  border-radius: 50%;
  border: 1px solid var(--dg-border);
  background: var(--dg-bg);
  color: var(--dg-ink);
  font-size: .9rem;
}
.dgdb-onb-step.done .idx {
  background: var(--dg-chip-bg);
  border-color: var(--dg-chip-border);
}
.dgdb-onb-step .meta { display: flex; flex-direction: column; line-height: 1.1; }
.dgdb-onb-step .meta .ttl { font-weight: 600; }
.dgdb-onb-step .meta .xp  { font-size: .9rem; color: var(--dg-muted); }

/* Content area */
.dgdb-onb-content { padding: 8px 2px; }
.dgdb-onb-card {
  border: 1px solid var(--dg-border);
  border-radius: 14px; padding: 16px;
  background: var(--dg-bg);
  box-shadow: 0 1px 2px rgba(0,0,0,.03);
}
.dgdb-onb-card h2 { margin: 0 0 .25rem; font-size: 1.25rem; }
.dgdb-onb-card p  { margin: .25rem 0 .6rem; color: var(--dg-ink); }
.dgdb-onb-how {
  background: color-mix(in srgb, var(--dg-accent) 8%, var(--dg-surface));
  border: 1px solid color-mix(in srgb, var(--dg-accent) 24%, var(--dg-border));
  border-radius: 10px;
  padding: 10px 12px; margin-top: 10px; font-size: .95rem;
}

/* -------------------------------------------------------
   Footer
   ------------------------------------------------------- */
.dgdb-onb-foot {
  display: flex; justify-content: space-between; align-items: center;
  gap: 8px; padding: 12px 16px; border-top: 1px solid var(--dg-border);
  background: var(--dg-bg);
}
.dgdb-onb-actions { display: flex; gap: 8px; flex-wrap: wrap; }

/* Buttons */
.dgdb-btn {
  appearance: none;
  border: 1px solid var(--dg-border);
  background: var(--dg-bg);
  color: var(--dg-ink);
  padding: .55rem .8rem; border-radius: 10px; cursor: pointer;
  text-decoration: none; line-height: 1.1;
}
.dgdb-btn:hover { background: color-mix(in srgb, var(--dg-bg) 80%, var(--dg-surface)); }
.dgdb-btn-primary {
  background: var(--dg-accent);
  border-color: var(--dg-accent);
  color: #fff;
}
.dgdb-btn-primary:hover { filter: brightness(0.96); }
.dgdb-btn-ghost { background: var(--dg-bg); }
.dgdb-btn-alt {
  background: color-mix(in srgb, var(--dg-accent) 14%, var(--dg-bg));
  border-color: color-mix(in srgb, var(--dg-accent) 36%, var(--dg-border));
}

/* Completion chip */
.dgdb-onb-done {
  display: inline-flex; align-items: center; gap: 6px;
  background: var(--dg-chip-bg);
  color: var(--dg-ink);
  border: 1px solid var(--dg-chip-border);
  padding: .3rem .5rem; border-radius: 999px; font-size: .92rem;
}

/* Progress line (small) */
.dgdb-onb-line {
  height: 8px;
  background: color-mix(in srgb, var(--dg-bg) 70%, var(--dg-surface));
  border-radius: 999px; overflow: hidden; border: 1px solid var(--dg-border);
  margin-bottom: 10px;
}
.dgdb-onb-line > span {
  display: block; height: 100%; width: 0%;
  background: var(--dg-accent);
}

/* Launch block when shortcode is on an empty page */
.dgdb-onb-launch { margin: 12px 0 18px; }
.dgdb-onb-auth .dgdb-btn { margin-right: 8px; }

/* Confetti canvas */
.dgdb-onb-canvas {
  position: fixed; inset: 0; pointer-events: none; z-index: 10000;
}
