// assets/flight-chart.js — 2-phase path with natural RHBH fade shoulder + straight finish + animation
(() => {
  const svgNS = 'http://www.w3.org/2000/svg';

  // ---------- helpers ----------
  const clamp = (v, a, b) => Math.max(a, Math.min(b, v));
  const lerp = (a, b, t) => a + (b - a) * t;
  const easeInOut = t => (t < 0.5) ? (2 * t * t) : (1 - Math.pow(-2 * t + 2, 2) / 2);

  function initChart(root) {
    // data
    let data;
    try { data = JSON.parse(root.getAttribute('data-disc') || '{}'); } catch { return; }
    const req = ['speed', 'glide', 'turn', 'fade'];
    if (!req.every(k => Number.isFinite(Number(data[k])))) return;

    // UI state
    let hand = 'R';     // 'R' | 'L'
    let thrw = 'BH';    // 'BH' | 'FH'
    let pace = 'norm';  // 'slow' | 'norm' | 'fast'
    let useFeet = false; // 'false' for meters, 'true' for feet

    // geometry
    const vb = { w: 680, h: 360, pad: 58 };
    const yMid = vb.h / 2;
    const drawW = vb.w - vb.pad * 2;
    const UNIT = 14; // 1 grid unit in px

    const xPxFromMeters = m => vb.pad + clamp(m / 150, 0, 1) * drawW;

    // handedness (screen Y grows downward)
    function signsFor(H, T) {
      const isRHBH = (H === 'R' && T === 'BH');
      const isLHFH = (H === 'L' && T === 'FH');
      // RHBH / LHFH: high-speed turn dips DOWN (+Y), fade pulls UP (−Y)
      if (isRHBH || isLHFH) return { sTurn: +1, sFade: -1 };
      // Mirror for LHBH / RHFH
      return { sTurn: -1, sFade: +1 };
    }

    // distance model
    function estimateRangeMeters(speed, glide, paceKey) {
      const s = clamp(Number(speed), 3, 14);
      const g = clamp(Number(glide), 1, 7);
      const t = (s - 3) / (14 - 3);
      const baseFt = lerp(220, 430, easeInOut(t));
      const glideAdjFt = (g - 5) * 5.5;
      const modelM = (baseFt + glideAdjFt) * 0.3048;
      const paceTarget = { slow: 100, norm: 120, fast: 140 }[paceKey] ?? 120;
      const anchor = 115;
      return clamp(paceTarget * (modelM / anchor), 60, 150);
    }

    // path geometry
    function buildPath(d, H, T, P) {
      const rangeM = estimateRangeMeters(d.speed, d.glide, P);
      const { sTurn, sFade } = signsFor(H, T);

      const turn = clamp(Number(d.turn), -5, 2);
      const fade = clamp(Number(d.fade), 0, 5);
      const glide = clamp(Number(d.glide), 1, 7);
      const tMag = Math.max(0, -turn);

      // Gentle sag depth (px)
      const baseDip = (tMag === 0 ? 0 : tMag === 1 ? 10 : tMag === 2 ? 18 : tMag === 3 ? 26 : tMag === 4 ? 36 : 48);
      const dipPx = baseDip
        * lerp(1.08, 1.8, (glide - 1) / 6)
        * lerp(1.00, 0.94, clamp((rangeM - 100) / 50, 0, 1))
        * sTurn;

      // Exact fade matching - each fade point = 1 UNIT on the grid
      const fadePx = fade * UNIT * sFade;

      // Make turn phase end earlier to give fade 20-30% of flight distance
      let straightFrac =
        (tMag === 0) ? 0.70 :
          (tMag === 1) ? 0.65 :
            (tMag === 2) ? 0.60 :
              (tMag === 3) ? 0.50 : 0.45;

      straightFrac += (glide - 5) * 0.007;
      if (P === 'fast') straightFrac += 0.02;
      if (P === 'slow') straightFrac -= 0.015;
      straightFrac = clamp(straightFrac, 0.40, 0.75);

      const endM = clamp(rangeM, 80, 145);
      const x0 = xPxFromMeters(0);
      const x1 = xPxFromMeters(endM);
      const xA = lerp(x0, x1, straightFrac);
      const y0 = yMid;
      const yA = yMid + dipPx;

      // End point should exactly match fade value on grid
      const y1 = yMid + fadePx;

      // Segment 1
      const c1x = lerp(x0, xA, 0.25), c1y = y0;
      const c2x = lerp(x0, xA, 0.20), c2y = yA;

      // Segment 2 - Ensure smooth curve to exact fade position
      const dir = Math.sign(sFade) || -1;
      const yLo = Math.min(yA, y1), yHi = Math.max(yA, y1);

      const c3x = lerp(xA, x1, 0.81);
      let c3y = lerp(yA, y1, 0.01);
      c3y = clamp(c3y, yLo + 2 * dir, yHi - 2 * dir);

      const endGapPx = clamp(drawW * 0.001, 5, 100);
      const c4x = x1 - endGapPx;
      let c4y = lerp(yA, y1, 0.85);

      c4y = clamp(c4y, Math.min(y1, c4y), Math.max(y1, c4y));

      return { x0, y0, c1x, c1y, c2x, c2y, xA, yA, c3x, c3y, c4x, c4y, x1, y1: y1, fadePx };
    }

    // ---------- defs ----------
    function drawDefs(svg, uid) {
      const defs = document.createElementNS(svgNS, 'defs');

      const glow = document.createElementNS(svgNS, 'filter');
      glow.setAttribute('id', `fc-glow-${uid}`);
      glow.innerHTML =
        `<feGaussianBlur in="SourceGraphic" stdDeviation="1.15" result="g"/>
         <feMerge><feMergeNode in="g"/><feMergeNode in="SourceGraphic"/></feMerge>`;
      defs.appendChild(glow);

      const grad = document.createElementNS(svgNS, 'linearGradient');
      grad.setAttribute('id', `fc-grad-${uid}`);
      grad.setAttribute('x1', '0%'); grad.setAttribute('y1', '0%');
      grad.setAttribute('x2', '100%'); grad.setAttribute('y2', '0%');
      grad.innerHTML = `<stop offset="0%" stop-color="#37b670"/><stop offset="100%" stop-color="#2c7a96"/>`;
      defs.appendChild(grad);

      const land = document.createElementNS(svgNS, 'filter');
      land.setAttribute('id', `fc-land-${uid}`);
      land.innerHTML = `<feGaussianBlur stdDeviation="2.2"/>`;
      defs.appendChild(land);

      svg.appendChild(defs);
    }

    // ---------- grid ----------
    function drawGrid(svg) {
      const gMinor = document.createElementNS(svgNS, 'g');
      const gMajor = document.createElementNS(svgNS, 'g');
      svg.appendChild(gMinor); svg.appendChild(gMajor);

      // X grid (unchanged)
      for (let m = 10; m < 150; m += 10) {
        if (m % 30 === 0) continue;
        const x = xPxFromMeters(m);
        const p = document.createElementNS(svgNS, 'path');
        p.setAttribute('d', `M ${x} ${vb.pad - 10} V ${vb.h - vb.pad + 10}`);
        p.setAttribute('class', 'fc-grid fc-grid--minor');
        gMinor.appendChild(p);
      }
      
      // Major grid lines and labels
      for (let m = 0; m <= 150; m += 30) {
        const x = xPxFromMeters(m);
        const p = document.createElementNS(svgNS, 'path');
        p.setAttribute('d', `M ${x} ${vb.pad - 14} V ${vb.h - vb.pad + 14}`);
        p.setAttribute('class', 'fc-grid');
        gMajor.appendChild(p);

        const lbl = document.createElementNS(svgNS, 'text');
        lbl.setAttribute('x', x);
        lbl.setAttribute('y', vb.h - vb.pad + 28);
        lbl.setAttribute('text-anchor', 'middle');
        lbl.setAttribute('class', 'fc-tick');
        
        // Convert to feet if needed
        if (useFeet) {
          const feet = Math.round(m * 3.28084);
          lbl.textContent = String(feet);
        } else {
          lbl.textContent = String(m);
        }
        svg.appendChild(lbl);
      }

      // mid (0)
      const mid = document.createElementNS(svgNS, 'path');
      mid.setAttribute('d', `M ${vb.pad - 8} ${yMid} H ${vb.w - vb.pad + 8}`);
      mid.setAttribute('class', 'fc-grid fc-grid--minor');
      mid.setAttribute('opacity', '.35');
      svg.appendChild(mid);

      // Get current hand/throw to determine if we need to invert positioning
      const isRHBH = (hand === 'R' && thrw === 'BH');
      const isLHFH = (hand === 'L' && thrw === 'FH');
      const shouldInvert = !(isRHBH || isLHFH);

      // LEFT SIDE: Turn (+2 to -5) - KEEP VALUES, INVERT POSITIONING for LHBH/RHFH
      const turnValues = [2, 1, 0, -1, -2, -3, -4, -5];
      turnValues.forEach(v => {
        const displayValue = v;
        const y = shouldInvert ? yMid + (v * UNIT) : yMid - (v * UNIT);

        if (v !== 0) {
          const tick = document.createElementNS(svgNS, 'path');
          tick.setAttribute('d', `M ${vb.pad - 18} ${y} H ${vb.pad - 10}`);
          tick.setAttribute('class', 'fc-ytick-line');
          svg.appendChild(tick);
        }
        const lbl = document.createElementNS(svgNS, 'text');
        lbl.setAttribute('x', vb.pad - 22);
        lbl.setAttribute('y', y + 4);
        lbl.setAttribute('text-anchor', 'end');
        lbl.setAttribute('class', 'fc-ytick fc-ytick--left');
        lbl.textContent = displayValue > 0 ? `+${displayValue}` : String(displayValue);
        svg.appendChild(lbl);
      });

      // RIGHT SIDE: Fade (0 to 5) - KEEP VALUES, INVERT POSITIONING for LHBH/RHFH
      const xR = vb.w - vb.pad;
      const fadeValues = [0, 1, 2, 3, 4, 5];
      fadeValues.forEach(v => {
        const displayValue = v;
        const y = shouldInvert ? yMid + (v * UNIT) : yMid - (v * UNIT);

        if (v !== 0) {
          const tick = document.createElementNS(svgNS, 'path');
          tick.setAttribute('d', `M ${xR + 10} ${y} H ${xR + 18}`);
          tick.setAttribute('class', 'fc-ytick-line');
          svg.appendChild(tick);
        }
        const lbl = document.createElementNS(svgNS, 'text');
        lbl.setAttribute('x', xR + 22);
        lbl.setAttribute('y', y + 4);
        lbl.setAttribute('text-anchor', 'start');
        lbl.setAttribute('class', 'fc-ytick fc-ytick--right');
        lbl.textContent = String(displayValue);
        svg.appendChild(lbl);
      });

      // Axis titles
      const capTurn = document.createElementNS(svgNS, 'text');
      capTurn.setAttribute('x', vb.pad - 22);
      capTurn.setAttribute('y', vb.pad - 8);
      capTurn.setAttribute('text-anchor', 'end');
      capTurn.setAttribute('class', 'fc-ycap fc-ycap--turn');
      capTurn.textContent = 'Turn';
      svg.appendChild(capTurn);

      const capFade = document.createElementNS(svgNS, 'text');
      capFade.setAttribute('x', xR + 22);
      capFade.setAttribute('y', vb.pad - 8);
      capFade.setAttribute('text-anchor', 'start');
      capFade.setAttribute('class', 'fc-ycap fc-ycap--fade');
      capFade.textContent = 'Fade';
      svg.appendChild(capFade);

      const xTitle = document.createElementNS(svgNS, 'text');
      xTitle.setAttribute('x', vb.w / 2);
      xTitle.setAttribute('y', vb.h - 10);
      xTitle.setAttribute('text-anchor', 'middle');
      xTitle.setAttribute('class', 'fc-axis');
      xTitle.textContent = useFeet ? 'Distance (ft)' : 'Distance (m)';
      svg.appendChild(xTitle);
    }

    // ---------- curve with animation ----------
    function drawCurve(svg, uid) {
      const P = buildPath(data, hand, thrw, pace);

      // baseline
      const base = document.createElementNS(svgNS, 'path');
      base.setAttribute('d', `M ${P.x0} ${yMid} L ${P.x1} ${yMid}`);
      base.setAttribute('class', 'fc-base');
      svg.appendChild(base);

      // two explicit cubics (no 'S' reflection)
      const d = `M ${P.x0} ${P.y0}
                 C ${P.c1x} ${P.c1y}, ${P.c2x} ${P.c2y}, ${P.xA} ${P.yA}
                 C ${P.c3x} ${P.c3y}, ${P.c4x} ${P.c4y}, ${P.x1} ${P.y1}`;

      const path = document.createElementNS(svgNS, 'path');
      path.setAttribute('d', d);
      path.setAttribute('class', 'fc-line');
      path.setAttribute('stroke', `url(#fc-grad-${uid})`);
      path.setAttribute('filter', `url(#fc-glow-${uid})`);
      path.setAttribute('stroke-linecap', 'round');
      path.setAttribute('fill', 'none');
      
      // Add animation properties
      path.setAttribute('stroke-dasharray', '1000');
      path.setAttribute('stroke-dashoffset', '1000');
      
      svg.appendChild(path);

      // start dot
      const dot = document.createElementNS(svgNS, 'circle');
      dot.setAttribute('cx', P.x0);
      dot.setAttribute('cy', P.y0);
      dot.setAttribute('r', 4.2);
      dot.setAttribute('class', 'fc-dot');
      svg.appendChild(dot);

      // landing rings (slight bias toward fade height)
      [10, 16, 22].forEach((r, i) => {
        const ring = document.createElementNS(svgNS, 'circle');
        ring.setAttribute('cx', P.x1);
        const landY = lerp(P.y1, yMid + P.fadePx, 0.10);
        ring.setAttribute('cy', landY);
        ring.setAttribute('r', r);
        ring.setAttribute('class', `fc-target-ring fc-target-ring--${i + 1}`);
        ring.setAttribute('filter', `url(#fc-land-${uid})`);
        
        // Add animation delay for rings to appear after path completes
        ring.style.opacity = '0';
        ring.style.transition = `opacity 0.3s ease-in ${0.25 + (i * 0.1)}s`;
        svg.appendChild(ring);
      });

      // Animate the path drawing
      setTimeout(() => {
        path.style.transition = 'stroke-dashoffset 2.5s ease-in-out';
        path.style.strokeDashoffset = '0';
        
        // Show landing rings after path animation
        setTimeout(() => {
          svg.querySelectorAll('.fc-target-ring').forEach(ring => {
            ring.style.opacity = '1';
          });
        }, 500);
      }, 100);
    }

    function drawTitle(svg) {
      const ttl = document.createElementNS(svgNS, 'text');
      ttl.setAttribute('x', vb.w / 2);
      ttl.setAttribute('y', 28);
      ttl.setAttribute('text-anchor', 'middle');
      ttl.setAttribute('class', 'fc-title');
      ttl.textContent = `${data.name || 'Disc'}s Flight`;
      svg.appendChild(ttl);
    }

    // ---------- render ----------
    function render() {
      root.innerHTML = '';
      const svg = document.createElementNS(svgNS, 'svg');
      svg.setAttribute('viewBox', `0 0 ${vb.w} ${vb.h}`);
      svg.setAttribute('class', 'fc-svg');
      svg.style.width = '100%';
      svg.style.height = '100%';
      root.appendChild(svg);

      const w = Math.max(260, root.getBoundingClientRect().width || 640);
      root.style.setProperty('--fc-title', `${Math.round(w / 26)}px`);
      root.style.setProperty('--fc-label', `${Math.round(w / 56)}px`);
      root.style.setProperty('--fc-stroke', `${Math.max(6, Math.round(w / 140))}`);

      const uid = Math.random().toString(36).slice(2, 7);
      drawDefs(svg, uid);
      drawGrid(svg);
      drawCurve(svg, uid);
      drawTitle(svg);
    }

    // controls
    const card = root.closest('.chart-card') || document;

    card.querySelectorAll('[data-hand]').forEach(b => {
      b.addEventListener('click', () => {
        card.querySelectorAll('[data-hand]').forEach(x => x.classList.remove('is-active'));
        b.classList.add('is-active');
        hand = (/^r/i.test((b.dataset.hand || b.textContent || '').trim())) ? 'R' : 'L';
        render();
      });
    });

    card.querySelectorAll('[data-throw]').forEach(b => {
      b.addEventListener('click', () => {
        card.querySelectorAll('[data-throw]').forEach(x => x.classList.remove('is-active'));
        b.classList.add('is-active');
        thrw = (/^f/i.test((b.dataset.throw || b.textContent || '').trim())) ? 'FH' : 'BH';
        render();
      });
    });

    card.querySelectorAll('[data-speed]').forEach(b => {
      b.addEventListener('click', () => {
        card.querySelectorAll('[data-speed]').forEach(x => x.classList.remove('is-active'));
        b.classList.add('is-active');
        pace = (b.dataset.speed || 'norm').toLowerCase();
        render();
      });
    });

    // Add units toggle control
    card.querySelectorAll('[data-units]').forEach(b => {
      b.addEventListener('click', () => {
        useFeet = !useFeet;
        card.querySelectorAll('[data-units]').forEach(x => {
          x.classList.toggle('is-active', useFeet);
          // Update button text to show current state
          x.textContent = useFeet ? 'Switch to Ms' : 'Switch to Ft';
        });
        render();
      });
    });

    // Initialize units button text
    card.querySelectorAll('[data-units]').forEach(b => {
      b.textContent = useFeet ? 'Switch to Ms' : 'Switch to Ft';
    });

    render();
    window.addEventListener('resize', render, { passive: true });
  }

  document.querySelectorAll('.dgdb-flight-chart').forEach(initChart);
})();