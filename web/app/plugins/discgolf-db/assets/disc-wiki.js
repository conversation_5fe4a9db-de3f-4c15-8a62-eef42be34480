(function(){
  if (!window.dgdbWiki || !dgdbWiki.ajaxUrl || !dgdbWiki.discId) return;

  function escapeHtml(str){ return String(str).replace(/[&<>"']/g, s=>({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;',"'":'&#039;'}[s])); }

  const ajax = (data, isForm=false) => {
    const body = isForm ? data : new URLSearchParams(data);
    return fetch(dgdbWiki.ajaxUrl, {
      method: 'POST',
      credentials: 'same-origin',
      headers: isForm ? {} : { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: isForm ? body : body.toString()
    }).then(r=>r.json());
  };

  async function loadDiscWiki() {
    const url = new URL(dgdbWiki.ajaxUrl, window.location.origin);
    url.searchParams.set('action', 'dgdb_disc_wiki');
    url.searchParams.set('disc_id', String(dgdbWiki.discId));
    const res = await fetch(url.toString(), { credentials:'same-origin' }).then(r=>r.json());
    if (!res || !res.success) return;

    const data = res.data;

    // Galleri
    const gal = document.querySelector('.dgdb-wiki-gallery');
    if (gal) {
      gal.innerHTML = '';
      (data.gallery || []).forEach(img=>{
        const fig = document.createElement('figure');
        fig.style.width = '120px';
        fig.innerHTML = `
          <img src="${img.image_url}" alt="" style="max-width:100%;height:auto;border:1px solid #ddd;padding:2px;border-radius:4px;">
          ${
            (img.attribution_text || img.license || img.attribution_url)
            ? `<figcaption style="font-size:12px;color:#555;margin-top:4px;">
                 ${img.attribution_text ? escapeHtml(img.attribution_text) : ''}
                 ${img.license ? (img.attribution_text ? ' · ' : '') + 'Licens: ' + escapeHtml(img.license) : ''}
                 ${img.attribution_url ? ' · <a href="${img.attribution_url}" target="_blank" rel="noopener">källa</a>' : ''}
               </figcaption>`
            : ''
          }`;
        gal.appendChild(fig);
      });
    }

    // Active flight
    const fActive = document.querySelector('.dgdb-wiki-flight-active');
    if (fActive) {
      const f = data.flight;
      if (f) {
        fActive.innerHTML = `
          <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
            <span style="font-weight: 600; color: var(--wp--preset--color--ink);">Aktiva flight numbers:</span>
            <div style="display: flex; gap: 8px;">
              <span style="background: var(--wp--preset--color--surface); padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">S ${f.speed}</span>
              <span style="background: var(--wp--preset--color--surface); padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">G ${f.glide}</span>
              <span style="background: var(--wp--preset--color--surface); padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">T ${f.turn}</span>
              <span style="background: var(--wp--preset--color--surface); padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 500;">F ${f.fade}</span>
            </div>
          </div>
        `;
      } else {
        fActive.innerHTML = '<em>Inga aktiva flight numbers ännu. Bidra genom att föreslå några!</em>';
      }
    }

    // Plaster
    const plList = document.querySelector('.dgdb-wiki-plastics-list');
    if (plList) {
      plList.innerHTML = '';
      (data.plastics || []).forEach(p=>{
        const li = document.createElement('li');
        li.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-weight: 500;">${escapeHtml(p.name)}</span>
            ${p.notes ? `<span style="color: var(--wp--preset--color--muted); font-size: 13px;">${escapeHtml(p.notes)}</span>` : ''}
          </div>
        `;
        plList.appendChild(li);
      });
      if ((data.plastics || []).length === 0) {
        const li = document.createElement('li');
        li.innerHTML = '<em style="color: var(--wp--preset--color--muted);">Inga godkända plaster ännu. Bidra genom att lägga till några!</em>';
        li.style.border = 'none';
        li.style.background = 'transparent';
        li.style.padding = '12px 0';
        plList.appendChild(li);
      }
    }

    // Info/Historia
    const info = document.querySelector('.dgdb-wiki-info');
    if (info) {
      info.innerHTML = data.info ? data.info : '<em style="color: var(--wp--preset--color--muted);">Ingen information ännu. Bidra genom att skriva något!</em>';
    }
    const hist = document.querySelector('.dgdb-wiki-history');
    if (hist) {
      hist.innerHTML = data.history ? data.history : '<em style="color: var(--wp--preset--color--muted);">Ingen historia ännu. Bidra genom att skriva något!</em>';
    }
  }

  // --- Bind forms (om inloggad) ---
  if (dgdbWiki.can_contribute) {
    // Image
    (function(){
      const f = document.querySelector('.dgdb-wiki-form--image');
      if (!f) return;
      const s = document.querySelector('.dgdb-wiki-status--image');
      f.addEventListener('submit', async (e)=>{
        e.preventDefault();
        if (!f.image || !f.image.files || !f.image.files.length) { if(s) s.textContent='Välj en bild först.'; return; }
        const fd = new FormData(f);
        fd.append('action', 'dgdb_submit_wiki_image');
        fd.append('disc_id', String(dgdbWiki.discId));
        fd.append('nonce', dgdbWiki.nonce || '');
        const res = await ajax(fd, true);
        if (s) {
          const message = res && res.success ? (res.data.message || 'Skickat!') : (res && res.data && res.data.message) ? res.data.message : 'Något gick fel.';
          s.textContent = message;
          s.style.display = 'block';
          setTimeout(() => { if (s) s.style.display = 'none'; }, 5000);
        }
        if (res && res.success) { f.reset(); loadDiscWiki(); }
      });
    })();

    // Flight
    (function(){
      const f = document.querySelector('.dgdb-wiki-form--flight');
      if (!f) return;
      const s = document.querySelector('.dgdb-wiki-status--flight');
      f.addEventListener('submit', async (e)=>{
        e.preventDefault();
        const data = {
          action: 'dgdb_submit_wiki_flight',
          disc_id: String(dgdbWiki.discId),
          nonce: dgdbWiki.nonce || '',
          speed: f.speed.value, glide: f.glide.value, turn: f.turn.value, fade: f.fade.value
        };
        const res = await ajax(data);
        if (s) {
          const message = res && res.success ? (res.data.message || 'Skickat!') : (res && res.data && res.data.message) ? res.data.message : 'Något gick fel.';
          s.textContent = message;
          s.style.display = 'block';
          setTimeout(() => { if (s) s.style.display = 'none'; }, 5000);
        }
        if (res && res.success) { f.reset(); loadDiscWiki(); }
      });
    })();

    // Plastic
    (function(){
      const f = document.querySelector('.dgdb-wiki-form--plastic');
      if (!f) return;
      const s = document.querySelector('.dgdb-wiki-status--plastic');
      f.addEventListener('submit', async (e)=>{
        e.preventDefault();
        const data = {
          action: 'dgdb_submit_wiki_plastic',
          disc_id: String(dgdbWiki.discId),
          nonce: dgdbWiki.nonce || '',
          name: f.name.value, notes: f.notes.value
        };
        const res = await ajax(data);
        if (s) {
          const message = res && res.success ? (res.data.message || 'Skickat!') : (res && res.data && res.data.message) ? res.data.message : 'Något gick fel.';
          s.textContent = message;
          s.style.display = 'block';
          setTimeout(() => { if (s) s.style.display = 'none'; }, 5000);
        }
        if (res && res.success) { f.reset(); loadDiscWiki(); }
      });
    })();

    // Text (info/history)
    (function(){
      const f = document.querySelector('.dgdb-wiki-form--text');
      if (!f) return;
      const s = document.querySelector('.dgdb-wiki-status--text');
      f.addEventListener('submit', async (e)=>{
        e.preventDefault();
        const data = {
          action: 'dgdb_submit_wiki_text',
          disc_id: String(dgdbWiki.discId),
          nonce: dgdbWiki.nonce || '',
          type: f.type.value,
          content: f.content.value
        };
        const res = await ajax(data);
        if (s) {
          const message = res && res.success ? (res.data.message || 'Skickat!') : (res && res.data && res.data.message) ? res.data.message : 'Något gick fel.';
          s.textContent = message;
          s.style.display = 'block';
          setTimeout(() => { if (s) s.style.display = 'none'; }, 5000);
        }
        if (res && res.success) { f.reset(); loadDiscWiki(); }
      });
    })();
  }

  // Kickoff
  loadDiscWiki();
})();

(function(){
  function ready(cb){ if (document.readyState==='loading') document.addEventListener('DOMContentLoaded', cb, {once:true}); else cb(); }
  function el(q){ return document.querySelector(q); }
  function ce(t,c){ var n=document.createElement(t); if(c) n.className=c; return n; }
  function toast(status, msg, ok){
    status.textContent = msg||''; status.style.display = msg ? 'inline-block':'none';
    if (msg) setTimeout(function(){ status.textContent=''; status.style.display='none'; }, 1600);
  }

  ready(function(){
    var root = el('#dgdb-plastics'); if(!root) return;

    var ajax  = (window.DGDB && DGDB.ajax)  || '/wp-admin/admin-ajax.php';
    var nonce = (window.DGDB && DGDB.nonce) || '';
    var discId = (window.DGDB && DGDB.discId) || 0;
    if(!discId) {
      var r=document.querySelector('.dgdb-reviews');
      if (r) discId = parseInt(r.getAttribute('data-disc-id')||'0',10);
    }
    if(!discId) return;

    // UI
    var chipsApproved = el('#dgdb-plastics-approved');
    var panel = el('#dgdb-mselect-panel');
    var toggle = el('#dgdb-mselect-toggle');
    var countEl = el('#dgdb-mselect-count');
    var input = el('#dgdb-plastics-input');
    var addBtn = el('#dgdb-plastics-add');
    var selectedWrap = el('#dgdb-plastics-selected');
    var saveBtn = el('#dgdb-plastics-save');
    var status = el('#dgdb-plastics-status');

    var state = {
      items: [],      // server items [{id,name,status}]
      brand: [],      // all plastics for brand
      proposed: []    // names selected/typed locally
    };

    function fetchData(){
      var u = new URL(ajax, location.origin);
      u.searchParams.set('action','dg_list_plastics');
      u.searchParams.set('disc_id', String(discId));
      u.searchParams.set('_', Date.now());
      fetch(u, {credentials:'same-origin'})
        .then(r=>r.json())
        .then(j=>{
          if(!j || !j.success) throw 0;
          state.items = j.data.items||[];
          state.brand = (j.data.brand_options||[]).filter(Boolean);
          state.proposed = [];
          renderAll();
        })
        .catch(()=>toast(status,'Could not load plastics',false));
    }

    function renderApproved(){
      chipsApproved.innerHTML = '';
      var approved = state.items.filter(it=>it.status==='approved');
      if (approved.length) {
        approved.forEach(it=>{
          var chip = ce('span','dgdb-chip');
          chip.textContent = it.name;
          chipsApproved.appendChild(chip);
        });
      } else {
        var empty = ce('span','dgdb-subtle');
        empty.textContent = 'No approved plastics yet.';
        chipsApproved.appendChild(empty);
      }
    }

    // Options that can be selected in dropdown
    function availableOptions(){
      var present = new Set(state.items.map(it=>it.name.toLowerCase())); // approved+pending
      var picked  = new Set(state.proposed.map(n=>n.toLowerCase()));
      return state.brand
        .filter(n=>!present.has(n.toLowerCase()))
        .filter(n=>!picked.has(n.toLowerCase()))
        .sort((a,b)=>a.localeCompare(b));
    }

    function renderDropdown(){
      panel.innerHTML = '';
      var opts = availableOptions();
      if (!opts.length) {
        var none = ce('div','dgdb-subtle');
        none.style.padding='6px 8px';
        none.textContent = 'No more plastics to choose.';
        panel.appendChild(none);
        updateCount();
        return;
      }
      opts.forEach(function(name){
        var row = ce('label','dgdb-mopt');
        var cb = ce('input'); cb.type='checkbox'; cb.value=name;
        row.appendChild(cb);
        var txt = ce('span'); txt.textContent = name; row.appendChild(txt);
        cb.addEventListener('change', function(){
          if (cb.checked) addProposed(name); else removeProposed(name);
        });
        panel.appendChild(row);
      });
      updateCount();
    }

    function updateCount(){
      var n = state.proposed.length;
      countEl.textContent = n ? '('+n+' selected)' : '';
    }

    function renderProposed(){
      selectedWrap.innerHTML='';
      state.proposed.forEach(function(name){
        var chip = ce('span','dgdb-chip');
        chip.textContent = name;
        var x = ce('button','remove'); x.type='button'; x.textContent='✕';
        x.addEventListener('click', function(){
          removeProposed(name);
          // also uncheck if visible in dropdown
          var box = panel.querySelector('input[type="checkbox"][value="'+CSS.escape(name)+'"]');
          if (box) box.checked = false;
        });
        chip.appendChild(x);
        selectedWrap.appendChild(chip);
      });
      updateCount();
    }

    function renderAll(){
      renderApproved();
      renderProposed();
      renderDropdown();
    }

    function addProposed(name){
      name = (name||'').trim();
      if (!name) return;
      var lower = name.toLowerCase();
      // block duplicates against server list (approved/pending) and local picks
      if (state.items.some(it=>it.name.toLowerCase()===lower)) { toast(status,'Already listed for this disc',false); return; }
      if (!state.proposed.some(n=>n.toLowerCase()===lower)) state.proposed.push(name);
      renderProposed();
      renderDropdown();
    }
    function removeProposed(name){
      var lower = (name||'').toLowerCase();
      state.proposed = state.proposed.filter(n=>n.toLowerCase()!==lower);
      renderProposed();
      renderDropdown();
    }

    // Toggle dropdown
    toggle.addEventListener('click', function(){
      var open = root.querySelector('.dgdb-mselect').getAttribute('data-open')==='true';
      root.querySelector('.dgdb-mselect').setAttribute('data-open', String(!open));
      toggle.setAttribute('aria-expanded', String(!open));
      panel.hidden = open;
      if (!open) renderDropdown();
    });
    document.addEventListener('click', function(e){
      if (!root.contains(e.target)) { panel.hidden = true; root.querySelector('.dgdb-mselect').setAttribute('data-open','false'); toggle.setAttribute('aria-expanded','false'); }
    });

    // Add custom
    function addCustom(){
      var v = (input.value||'').trim();
      if (!v) return;
      addProposed(v);
      input.value='';
    }
    addBtn.addEventListener('click', addCustom);
    input.addEventListener('keydown', function(e){ if (e.key==='Enter'){ e.preventDefault(); addCustom(); } });

    // Save
    saveBtn.addEventListener('click', function(){
      if (!state.proposed.length) { toast(status,'Nothing to submit',false); return; }
      var fd = new FormData();
      fd.set('action','dg_save_plastics');
      fd.set('nonce', nonce);
      fd.set('disc_id', String(discId));
      state.proposed.forEach(n=>fd.append('add_names[]', n));
      fetch(ajax, { method:'POST', body: fd, credentials:'same-origin' })
        .then(r=>r.json())
        .then(j=>{
          if(!j || !j.success) throw 0;
          toast(status,'Submitted!',true);
          // server returns updated items + options
          state.items = j.data.items||[];
          state.brand = j.data.brand_options||[];
          state.proposed = [];
          renderAll();
        })
        .catch(()=>toast(status,'Submit failed',false));
    });

    fetchData();
  });
})();
