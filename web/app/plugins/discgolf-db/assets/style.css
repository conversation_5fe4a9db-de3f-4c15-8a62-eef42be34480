/* =========================================================
   DiscGolf Discs DB styles (refactored, non-breaking)
   - All selectors preserved; visual output unchanged
   - Duplicates consolidated; order/specificity respected
   ========================================================= */

/* ---------- Theme Bridge ---------- */
:root {
  /* Mirror commonly used tokens for consistency */
  --dg-bg: var(--wp--preset--color--bg);
  --dg-surface: var(--wp--preset--color--surface);
  --dg-ink: var(--wp--preset--color--ink);
  --dg-muted: var(--wp--preset--color--muted);
  --dg-accent: var(--wp--preset--color--accent);
  --dg-border: var(--dg-outline, rgba(0,0,0,.12)); /* fallback if theme doesn’t define */
  --dg-radius-sm: var(--wp--custom--radius--sm, 8px);
  --dg-radius-md: var(--wp--custom--radius--md, 12px);
  --dg-radius-lg: var(--wp--custom--radius--lg, 18px);
  --dg-radius-xl: var(--wp--custom--radius--xl, 24px);
  --dg-shadow-sm: var(--wp--custom--shadow--sm, 0 1px 3px rgba(0,0,0,.04));
  --dg-shadow-md: var(--wp--custom--shadow--md, 0 6px 18px rgba(0,0,0,.06));
  --dg-focus: 0 0 0 2px color-mix(in oklab, var(--dg-accent) 20%, transparent);
  --dg-focus-strong: 0 0 0 3px color-mix(in oklab, var(--dg-accent) 15%, transparent);
}

/* Ensure proper theme inheritance */
.dgdb-wrap,
.dgdb-profile-wrap {
  color: var(--dg-ink);
  background: var(--dg-bg);
}

/* ---------- Utilities ---------- */
.muted { color: var(--dg-muted) }
.sr-only{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(1px,1px,1px,1px);white-space:nowrap}
.dgdb-hidden{display:none!important}

/* ---------- Layout Utilities ---------- */
.dgdb-section-spacing{margin-top:24px}
.dgdb-form-grid{display:grid;gap:8px;max-width:680px}
.dgdb-form-row{display:flex;gap:8px;align-items:center}
.dgdb-form-label-fixed{min-width:62px}
.dgdb-login-prompt{margin-bottom:12px}
.dgdb-reviews-heading{margin-top:16px;margin-bottom:8px}
.dgdb-reviews-grid{margin-top:8px;display:grid;gap:12px}
.dgdb-reviews-more-container{margin-top:8px}
.dgdb-gallery-flex{display:flex;flex-wrap:wrap;gap:10px}

/* ---------- Page / Filters ---------- */
.dgdb-wrap{color:var(--dg-ink)}
.dgdb-filters{display:flex;gap:12px;flex-wrap:wrap;align-items:end;margin-bottom:12px;border: 1px solid var(--dg-border);
    border-radius: var(--dg-radius-lg);
    padding: 14px;
    box-shadow: var(--dg-shadow-md);}
.dgdb-filters label{display:flex;flex-direction:column;gap:6px;font-size:12px;color:var(--dg-muted)}
.dgdb-filters .grow{flex:1}
.dgdb-filters input,
.dgdb-filters select{
  background:var(--dg-surface);
  color:var(--dg-ink);
  border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-md);
  padding:8px 10px;
  outline:none;
  font-family:inherit;
}
.dgdb-filters input:focus,
.dgdb-filters select:focus{
  border-color:var(--dg-accent);
  box-shadow:var(--dg-focus);
}

/* ---------- Buttons (shared base) ---------- */
.dgdb-btn,
.dgdb-btn-primary{
  border-radius:var(--dg-radius-md);
  cursor:pointer;
  font-family:inherit;
  font-weight:600;
  transition:transform 0.1s ease, box-shadow 0.2s ease, background-color .2s ease, border-color .2s ease;
}
.dgdb-btn.disabled{opacity:.5;pointer-events:none}

/* Secondary/neutral button */
.dgdb-btn{
  height:32px;
  padding:0 10px;
  border:1px solid var(--dg-border);
  background:var(--dg-surface);
  color:var(--dg-ink);
}

/* Primary/affirmative button */
.dgdb-btn-primary,
.dgdb-btn--accent,
.dgdb-btn[data-variant="primary"]{
  height:36px;
  border:0;
  padding:0 14px;
  background:var(--dg-accent);
  color:#fff;
}
.dgdb-btn:hover,
.dgdb-btn-primary:hover,
.dgdb-btn--accent:hover{
  transform:translateY(-1px);
}

.dgdb-meta{color:var(--dg-muted);margin-bottom:8px}

/* ---------- Grid / Cards ---------- */
.dgdb-grid{display:grid;grid-template-columns:repeat(1,1fr);gap:14px}
@media (min-width:720px){.dgdb-grid{grid-template-columns:repeat(2,1fr)}}
@media (min-width:1040px){.dgdb-grid{grid-template-columns:repeat(3,1fr)}}
@media (min-width:1320px){.dgdb-grid{grid-template-columns:repeat(4,1fr)}}

.dgdb-card{
  background:var(--dg-bg);
  border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg);
  padding:14px;
  box-shadow:var(--dg-shadow-md);
  cursor:pointer;
  transition:transform .12s ease, box-shadow .2s ease;
}
.dgdb-card:hover{
  transform:translateY(-2px);
  box-shadow:0 10px 30px rgba(0,0,0,.08);
}
:root[data-theme="dark"] .dgdb-card{ box-shadow:0 6px 18px rgba(0,0,0,.45) }
:root[data-theme="dark"] .dgdb-card:hover{ box-shadow:0 10px 30px rgba(0,0,0,.6) }

.dgdb-card-head{display:flex;align-items:center;gap:12px;margin-bottom:8px}
.dgdb-card-title .muted{color:var(--dg-muted);font-weight:400;}
.dgdb-card-head--split{display:grid;grid-template-columns:64px 1fr;gap:30px;align-items:center}
.dgdb-card-avatar{display:flex}
.dgdb-thumb{object-fit:cover;width:90px;height:90px;border-radius:100%}
.dgdb-thumb--svg svg{display:block;border-radius:100%;width:90px;height:90px;}
.dgdb-card-main .dgdb-chips{margin-top:6px}

/* Chips */
.dgdb-chips{display:flex;gap:6px;flex-wrap:wrap;flex-direction:column}
.dgdb-chip{
  background:var(--dg-surface);
  border:1px solid var(--dg-border);
  border-radius:999px;
  padding:5px 10px;
  font-size:12px;
  color:var(--dg-ink);
}
.dgdb-chip.muted{color:var(--dg-muted)}
.dgdb-chip--plastic{background:color-mix(in oklab, var(--dg-accent) 15%, transparent);color:var(--dg-accent);font-weight:500}
.dgdb-chip--weight{background:color-mix(in oklab, #10b981 15%, transparent);color:#10b981;font-weight:500}

.dgdb-pager{display:flex;gap:10px;align-items:center;justify-content:center;margin-top:16px}

/* ---------- Modal (base) ---------- */
.dgdb-no-scroll{overflow:hidden}
.dgdb-modal{
  position:fixed;inset:0;z-index:9999;margin:0;
  display:flex;align-items:center;justify-content:center;
}
.dgdb-modal[hidden],.dgdb-reviews-modal[hidden]{display:none!important}
.dgdb-modal-backdrop{
  position:absolute;inset:0;background:rgba(0,0,0,.55);
  backdrop-filter: blur(12px);
}
.dgdb-modal-card{
  position:relative; /* absolute->relative works with flex centering */
  width:min(1100px,92vw);max-height:84vh;overflow:auto;
  background:var(--dg-bg); color:var(--dg-ink);
  border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg);
  box-shadow:0 10px 30px rgba(0,0,0,.08);
}
:root[data-theme="dark"] .dgdb-modal-card{ box-shadow:0 10px 30px rgba(0,0,0,.5) }

/* Contextual close buttons (avoid conflicts) */
.dgdb-modal-card .dgdb-modal-close{
  position:absolute;top:10px;right:10px;
  background:transparent;
  border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-md);
  padding:6px 10px;
  cursor:pointer;
  color:var(--dg-ink);
  font-weight:600;
  transition:background-color .2s ease;
}
.dgdb-modal-card .dgdb-modal-close:hover{ background:var(--dg-surface) }

/* Generic modal header bits */
.dgdb-modal-head{padding:18px 24px 0}
.dgdb-modal-head h3{margin:0;font-size:22px;font-weight:600}
.dgdb-modal-sub{color:var(--dg-muted);margin-top:4px}

/* ---------- Disc Modal Layout ---------- */
.dgdb-modal-card--disc .dgdb-modal-head{padding:18px 24px 0}
.dgdb-disc-layout{
  display:grid; grid-template-columns:300px 1fr; gap:20px; padding:18px 24px 24px;
}
@media (max-width:1100px){.dgdb-disc-layout{grid-template-columns:1fr}}

.dgdb-media{width:100%}
.dgdb-media-inner{position:relative;width:100%;aspect-ratio:1/1;overflow:hidden}
.dgdb-media-inner>img.dgdb-modal-image,
.dgdb-media-inner>.dgdb-modal-placeholder{position:absolute;inset:0;width:100%;height:100%}
.dgdb-media-inner>img.dgdb-modal-image{object-fit:cover}
.dgdb-media-inner>.dgdb-modal-placeholder svg{width:100%;height:100%;display:block}
.dgdb-upload-form{display:flex;gap:8px;align-items:center;margin-top:12px;flex-wrap:wrap}

.dgdb-modal-details{margin-bottom:10px}
.dgdb-modal-card--disc .dgdb-details-table{width:100%;border-collapse:separate;border-spacing:0;font-size:15px}
.dgdb-modal-card--disc .dgdb-details-table th,
.dgdb-modal-card--disc .dgdb-details-table td{padding:1px 0;line-height:1.25;vertical-align:top}
.dgdb-modal-card--disc .dgdb-details-table th{font-weight:700;text-align:left;white-space:nowrap;padding-right:22px}
.dgdb-modal-card--disc .dgdb-details-table td{text-align:left;white-space:nowrap;border:0;background:transparent}

/* ---------- Reviews (form + list + modal) ---------- */
.dgdb-stars{display:flex;gap:4px;font-size:22px;margin:6px 0 12px}
.dgdb-stars button{background:transparent;border:0;color:var(--dg-border);cursor:pointer}
.dgdb-stars button.active{color:gold}

.dgdb-review-textarea{
  width:95%; min-height:160px; resize:vertical;
  background:var(--dg-surface); color:var(--dg-ink);
  border:1px solid var(--dg-border); border-radius:var(--dg-radius-lg);
  padding:14px 16px; font-family:inherit; font-size:15px; line-height:1.4;
}
.dgdb-review-textarea:focus{ border-color:var(--dg-accent); box-shadow:var(--dg-focus); outline:none }

.dgdb-review-actions{display:flex;align-items:center;justify-content:flex-end;gap:12px;margin-top:16px}
.dgdb-my-review-text{white-space:pre-wrap}

.dgdb-reviews-body{padding:18px 20px 20px}
.dgdb-reviews-list .dgdb-card{margin-bottom:12px;padding:12px 14px}
.dgdb-reviews-list .dgdb-card .dgdb-card-head{margin:0}
.dgdb-reviews-list .dgdb-card .dgdb-card-title{font-size:15px}
.dgdb-reviews-pager{display:flex;align-items:center;gap:10px;margin-top:12px}
.dgdb-reviews-arrows{display:flex;gap:8px}
.dgdb-find-image-status{vertical-align:middle}

/* Card grid for reviews */
.dgdb-review-cards{display:grid;grid-template-columns:repeat(1,minmax(0,1fr));gap:12px}
@media (min-width:560px){.dgdb-review-cards{grid-template-columns:repeat(2,minmax(0,1fr))}}
@media (min-width:920px){.dgdb-review-cards{grid-template-columns:repeat(3,minmax(0,1fr))}}

.dgdb-review-card{
  background:var(--dg-bg);
  border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg);
  box-shadow:var(--dg-shadow-sm);
  padding:14px;
  cursor:pointer;
  transition:transform .12s ease, box-shadow .2s ease, border-color .2s ease;
  outline:none;
}
.dgdb-review-card:hover,
.dgdb-review-card:focus{
  transform:translateY(-2px);
  border-color:var(--dg-accent);
  box-shadow:0 8px 24px color-mix(in oklab, var(--dg-accent) 10%, transparent);
}
.dgdb-review-card__head{display:flex;justify-content:space-between;align-items:center;gap:8px}
.dgdb-review-card__meta{color:var(--dg-muted);font-size:12px}
.dgdb-review-card__title{margin:8px 0 6px;font-size:15px;font-weight:600;color:var(--dg-ink)}
.dgdb-review-card__text{margin:0;color:var(--dg-ink);font-size:14px;line-height:1.4}
.dgdb-review-card__fn{margin-top:8px;font-size:12px;color:var(--dg-muted)}
.dgdb-review-card__stars{color:#f59e0b}

/* Reviews modal */
.dgdb-review-modal[hidden]{display:none!important}
.dgdb-review-modal{position:fixed;inset:0;z-index:9999}
.dgdb-review-modal__backdrop{position:absolute;inset:0;background:rgba(0,0,0,.55);backdrop-filter:blur(2px)}
.dgdb-review-modal__card{
  position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);
  width:min(760px,92vw);
  background:var(--dg-bg); color:var(--dg-ink);
  border:1px solid var(--dg-border); border-radius:var(--dg-radius-lg);
  box-shadow:0 12px 40px rgba(0,0,0,.2);
  max-height:85vh; overflow:auto;
}
.dgdb-review-modal__close{
  position:absolute;top:10px;right:10px;
  border:1px solid var(--dg-border); background:var(--dg-surface);
  color:var(--dg-ink); border-radius:10px; padding:6px 10px; cursor:pointer;
}
.dgdb-review-modal__body{padding:20px}
.dgdb-modal-title{margin:0 28px 6px 0;font-size:18px;font-weight:700}
.dgdb-modal-meta{color:var(--dg-muted);font-size:13px;margin-bottom:8px}
.dgdb-modal-stars{color:#f59e0b;margin-bottom:8px}
.dgdb-modal-fn{color:var(--dg-muted);font-size:13px;margin-bottom:10px}
.dgdb-modal-text{white-space:pre-wrap;font-size:15px;line-height:1.5}

/* ---------- Single Disc page - Compact Contact Card ---------- */
.dgdb-single-compact{max-width:800px;margin:20px auto}
.dgdb-contact-card{
  background:var(--dg-bg);
  border-radius:var(--dg-radius-xl);
  overflow:hidden;max-width:100%;
}

:root[data-theme="dark"] .dgdb-contact-card{ box-shadow:0 6px 18px rgba(0,0,0,.45) }
.dgdb-card-header{padding:16px 20px 0;border-bottom:1px solid var(--dg-border);background:var(--dg-surface)}
.dgdb-card-main{padding:16px}

/* Hero */
.dgdb-hero-compact{display:grid;grid-template-columns:140px 1fr;gap:20px;align-items:start}
@media (max-width:600px){.dgdb-hero-compact{grid-template-columns:1fr;text-align:center;gap:16px}}

.dgdb-image-section{display:flex;flex-direction:column;align-items:center}
.dgdb-media-compact{width:120px;height:120px;border-radius:100%;overflow:hidden;background:var(--dg-surface);position:relative}
.dgdb-disc-image{width:100%;height:100%;object-fit:cover;display:block}
.dgdb-no-image{width:100%;height:100%;display:flex;align-items:center;justify-content:center;color:var(--dg-muted)}
.dgdb-image-credit{margin-top:6px;font-size:11px;color:var(--dg-muted)}
.dgdb-image-credit a{color:var(--dg-accent);text-decoration:none}

/* Info */
.dgdb-info-section{min-width:0}
.dgdb-disc-title{font-size:24px;font-weight:700;margin:0 0 4px 0;line-height:1.2;color:var(--dg-ink)}
.dgdb-disc-subtitle{color:var(--dg-muted);font-size:14px;margin-bottom:16px}

/* Quick specs */
.dgdb-quick-specs{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:16px}
.dgdb-spec-item{
  background:var(--dg-surface);border:1px solid var(--dg-border);
  border-radius:999px;padding:4px 10px;font-size:16px;display:flex;align-items:center;gap:4px;justify-content:center;
}
.dgdb-spec-label{color:var(--dg-muted);font-weight:500}
.dgdb-spec-value{color:var(--dg-ink);font-weight:600}

/* Actions */
.dgdb-actions{display:flex;gap:10px;align-items:center}

/* Content sections */
.dgdb-content-sections{margin-top:20px;display:grid;gap:20px}
.dgdb-section-card{
  background:var(--dg-bg); border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg); padding:20px; box-shadow:var(--dg-shadow-md);
}
:root[data-theme="dark"] .dgdb-section-card{ box-shadow:0 6px 18px rgba(0,0,0,.45) }
.dgdb-section-title{font-size:18px;font-weight:600;color:var(--dg-ink);padding-bottom:8px;margin:0}

/* Wiki panels (within compact) */
.dgdb-wiki-panels .dgdb-wiki-card,
.dgdb-section-content .dgdb-wiki-card{
  margin-top:0;margin-bottom:16px;border-radius:var(--dg-radius-md);
}
.dgdb-wiki-panels .dgdb-wiki-card:last-child,
.dgdb-section-content .dgdb-wiki-card:last-child{margin-bottom:0}

/* Upload section (merged duplicates) */
.dgdb-upload-section{
  margin-top:20px;padding-top:20px;border-top:1px solid var(--dg-border);
}
.dgdb-upload-title,
.dgdb-upload-section h4{
  margin:0 0 12px 0;font-size:16px;font-weight:600;color:var(--dg-ink);
}
.dgdb-upload-grid{display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:12px;margin-bottom:12px}
.dgdb-file-input{
  width:100%;padding:8px;border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-sm);background:var(--dg-surface);color:var(--dg-ink);font-family:inherit;margin-bottom:8px;
}

/* Full specs grid */
.dgdb-specs-grid{display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:12px}
.dgdb-spec-row{
  display:flex;justify-content:space-between;align-items:center;padding:6px 10px;
  background:var(--dg-surface);border-radius:999px;border:1px solid var(--dg-border)
}
.dgdb-spec-row .dgdb-spec-label{color:var(--dg-muted);font-size:10px;font-weight:500}
.dgdb-spec-row .dgdb-spec-value{color:var(--dg-ink);font-weight:600;font-size:10px}

/* Mobile compact adjustments */
@media (max-width:600px){
  .dgdb-single-compact{margin:10px auto;padding:0 12px}
  .dgdb-contact-card{border-radius:var(--dg-radius-lg)}
  .dgdb-card-main{padding:20px 16px}
  .dgdb-disc-title{font-size:20px}
  .dgdb-specs-grid{grid-template-columns:1fr}
  .dgdb-quick-specs{justify-content:center}
  .dgdb-content-sections{gap:16px}
  .dgdb-section-card{padding:16px}
  .dgdb-upload-grid{grid-template-columns:1fr}
}

/* ---------- Profile: Compact + Classic ---------- */
.dgdb-profile-compact{max-width:900px;margin:20px auto;padding:0 16px}
.dgdb-profile-contact-card{
  background:var(--dg-bg); border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-xl); box-shadow:var(--dg-shadow-md);
  overflow:hidden; position:relative; margin-bottom:20px;
}
:root[data-theme="dark"] .dgdb-profile-contact-card{ box-shadow:0 6px 18px rgba(0,0,0,.45) }

.dgdb-profile-header{position:relative;padding:24px;background:var(--dg-surface);border-bottom:1px solid var(--dg-border)}
.dgdb-profile-banner{position:absolute;top:0;left:0;right:0;height:120px;background-size:cover;background-position:center;opacity:.3;border-radius:var(--dg-radius-xl) var(--dg-radius-xl) 0 0}
.dgdb-profile-hero{display:grid;grid-template-columns:80px 1fr;gap:16px;align-items:center;position:relative;z-index:1}
.dgdb-profile-avatar{width:80px;height:80px;border-radius:50%;overflow:hidden;border:3px solid var(--dg-bg);box-shadow:0 4px 12px rgba(0,0,0,.1)}
.dgdb-profile-avatar img{width:100%;height:100%;object-fit:cover;display:block}
.dgdb-profile-name{font-size:24px;font-weight:700;margin:0 0 4px 0;color:var(--dg-ink)}
.dgdb-profile-handle{color:var(--dg-muted);font-size:14px;margin-bottom:12px}

.dgdb-profile-stats{display:flex;flex-wrap:wrap;gap:12px}
.dgdb-stat-item{
  background:var(--dg-bg); border:1px solid var(--dg-border);
  border-radius:999px; padding:4px 12px; font-size:12px; display:flex; align-items:center; gap:4px;
}
.dgdb-stat-label{color:var(--dg-muted);font-weight:500}
.dgdb-stat-value{color:var(--dg-ink);font-weight:600}

.dgdb-profile-content{padding:24px;display:grid;gap:12px}
.dgdb-profile-card{
  background:var(--dg-surface); border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg); padding:20px; box-shadow:var(--dg-shadow-sm);
}
:root[data-theme="dark"] .dgdb-profile-card{ box-shadow:0 1px 3px rgba(0,0,0,.3) }
.dgdb-card-title{font-weight:600;color:var(--dg-ink);padding-bottom:2px;font-size:16px}
.dgdb-collection-count{font-weight:400;color:var(--dg-muted);font-size:14px}

.dgdb-profile-form-grid{display:grid;grid-template-columns:repeat(auto-fit, minmax(250px, 1fr));gap:16px}
.dgdb-form-group{display:flex;flex-direction:column;margin:10px;width:95%}
.dgdb-bag-basics{display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:16px;margin-bottom:20px}

.dgdb-profile-actions{
  padding:20px 24px;border-top:1px solid var(--dg-border);background:var(--dg-surface);
  display:flex;align-items:center;gap:12px;border-radius:0 0 var(--dg-radius-xl) var(--dg-radius-xl);
}

/* Classic profile wrapper */
.dgdb-profile-wrap{max-width:1100px;margin:24px auto;padding:0 16px}
.dgdb-banner{width:100%;aspect-ratio:21/5;border-radius:18px;overflow:hidden;background:#0b0b0c;margin:0}
.dgdb-banner img{width:100%;height:100%;object-fit:cover;display:block}
.dgdb-profile-head{display:flex;gap:16px;align-items:center;margin-top:-42px}
.dgdb-avatar{width:84px;height:84px;border-radius:999px;border:3px solid #fff;overflow:hidden;background:#f3f4f6;box-shadow:0 10px 30px rgba(0,0,0,.2)}
:root[data-theme="dark"] .dgdb-avatar{ border-color:#000 }
.dgdb-username{font-weight:700;font-size:20px}
.dgdb-handle{color:var(--dg-muted);font-size:14px;margin-top:2px}
.dgdb-two{display:grid;grid-template-columns:1fr 1fr;gap:18px}
@media (max-width:900px){.dgdb-two{grid-template-columns:1fr}}

/* Inputs */
.dgdb-input,
.dgdb-textarea{
  width:100%; border:1px solid var(--dg-border); border-radius:var(--dg-radius-md);
  background:var(--dg-bg); color:var(--dg-ink); font-family:inherit;
}
.dgdb-input{height:36px}
.dgdb-input:focus,
.dgdb-textarea:focus{border-color:var(--dg-accent);box-shadow:var(--dg-focus);outline:none}
.dgdb-textarea{resize:vertical;min-height:120px}
.dgdb-label{display:block;font-size:13px;color:var(--dg-muted);margin:8px 0 6px}

/* Subtle */
.dgdb-subtle{color:var(--dg-muted); text-align: center; margin-top: 20px;font-size: 14px;}

/* Bag builder */
.dgdb-bag{margin-top:10px}
.dgdb-bag-sec{border:1px solid var(--dg-border);border-radius:14px;margin-bottom:14px;overflow:hidden}
.dgdb-bag-sec h4{margin:0;padding:10px 12px;background:var(--dg-surface);border-bottom:1px solid var(--dg-border);font-weight:600}
.dgdb-bag-table{width:100%;border-collapse:collapse}
.dgdb-bag-table th,.dgdb-bag-table td{padding:10px;border-top:1px solid var(--dg-border);vertical-align:middle}
.dgdb-bag-table th{font-weight:600;text-align:left;color:var(--dg-muted);font-size:12px}
.dgdb-row-del{white-space:nowrap}

.dgdb-suggest{position:relative}
.dgdb-suggest-list{
  position:absolute;z-index:20;left:0;right:0;top:100%;
  background:var(--dg-bg); border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-md); margin-top:6px; overflow:hidden;
  box-shadow:var(--dg-shadow-md); display:none;
}
:root[data-theme="dark"] .dgdb-suggest-list{ box-shadow:0 6px 18px rgba(0,0,0,.45) }
.dgdb-suggest-list button{
  display:block;width:100%;text-align:left;padding:8px 10px;border:0;
  background:var(--dg-bg); color:var(--dg-ink); font-family:inherit; cursor:pointer;
}
.dgdb-suggest-list button:hover{ background:var(--dg-surface) }

/* Review form small layout */
.dgdb-review-form-row{display:flex;justify-content:flex-start;flex-direction:column;align-items:start;margin:5px}
.dgdb-review-select{
  background:var(--dg-surface); color:var(--dg-ink);
  border:1px solid var(--dg-border); border-radius:var(--dg-radius-md);
  padding:8px 10px; font-family:inherit; outline:none;
}
.dgdb-review-select:focus{border-color:var(--dg-accent);box-shadow:var(--dg-focus);outline:none}

/* Profile page misc */
.dgdb-profile-editor{margin-top:16px}
.dgdb-label-spaced{margin-top:16px}
.dgdb-bag-col-disc{width:40%}
.dgdb-bag-col-qty{width:90px}
.dgdb-bag-col-actions{width:110px}
.dgdb-bag-add-section{padding:10px;border-top:1px solid var(--dg-border)}
.dgdb-profile-actions{display:flex;gap:10px;align-items:center;margin-top:16px}
.dgdb-collection-grid{grid-template-columns:repeat(auto-fill,minmax(260px,1fr))}
.dgdb-thumb--centered{display:flex;align-items:center;justify-content:center}
.dgdb-card-subtitle{font-weight:400;display:block;margin-top:2px;font-size:12px}

/* Single page headings */
.dgdb-back-btn{margin-bottom:16px;display:inline-block}
.dgdb-page-header{margin-bottom:16px}
.dgdb-page-title{margin:0 0 6px 0;font-size:28px;font-weight:700}
.dgdb-page-subtitle{margin:0 0 16px 0}

/* ---------- Wiki Panels (general) ---------- */
.dgdb-wiki-card{
  margin-top:12px;padding:20px;border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-lg); box-shadow:var(--dg-shadow-sm);
}
:root[data-theme="dark"] .dgdb-wiki-card{ box-shadow:0 1px 3px rgba(0,0,0,.3) }
.dgdb-wiki-title{margin-top:0;margin-bottom:12px;font-size:18px;font-weight:600;color:var(--dg-ink)}
.dgdb-wiki-subtitle{margin:20px 0 8px 0;font-size:16px;font-weight:600;color:var(--dg-ink);padding-top:16px;border-top:1px solid var(--dg-border)}
.dgdb-wiki-form{margin-top:12px}
.dgdb-wiki-form--text{margin-top:12px}

.dgdb-flight-grid{display:grid;grid-template-columns:repeat(4,1fr);gap:10px;margin-bottom:12px}
.dgdb-flight-input{
  background:var(--dg-bg); color:var(--dg-ink);
  border:1px solid var(--dg-border); border-radius:var(--dg-radius-md);
  padding:10px 12px; font-family:inherit; font-size:14px; text-align:center; transition:all .2s ease;
}
.dgdb-flight-input:focus{border-color:var(--dg-accent);box-shadow:var(--dg-focus-strong);outline:none;transform:translateY(-1px)}

.dgdb-wiki-submit{
  margin-top:12px;background:var(--dg-accent);color:#fff;border:0;border-radius:var(--dg-radius-md);
  padding:10px 16px;font-weight:600;font-family:inherit;cursor:pointer;transition:all .2s ease;
}
.dgdb-wiki-submit:hover{transform:translateY(-1px);box-shadow:0 4px 12px color-mix(in oklab, var(--dg-accent) 25%, transparent)}

.dgdb-wiki-status{
  margin-top:8px;padding:8px 12px;border-radius:var(--dg-radius-sm);font-size:14px;line-height:1.4;display:none;
}
.dgdb-wiki-status:not(:empty){
  background:color-mix(in oklab, var(--dg-accent) 10%, transparent);
  border:1px solid color-mix(in oklab, var(--dg-accent) 20%, transparent);
  color:var(--dg-accent);
}

.dgdb-wiki-plastics-list{margin:12px 0 0 0;padding:0;list-style:none}
.dgdb-wiki-plastics-list li{
  background:var(--dg-bg);border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-sm);padding:8px 12px;margin-bottom:6px;font-size:14px;
}
.dgdb-wiki-plastics-list li:last-child{margin-bottom:0}

.dgdb-wiki-input,
.dgdb-wiki-select,
.dgdb-wiki-textarea{
  background:var(--dg-bg); color:var(--dg-ink); border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-md); font-family:inherit; font-size:14px; transition:all .2s ease;
}
.dgdb-wiki-input{width:95%;margin-bottom:8px;padding:10px 12px}
.dgdb-wiki-select{padding:10px 12px;margin-bottom:8px}
.dgdb-wiki-textarea{display:block;width:100%;margin-top:0;padding:12px;line-height:1.5;resize:vertical;min-height:100px}
.dgdb-wiki-input:focus,
.dgdb-wiki-select:focus,
.dgdb-wiki-textarea:focus{border-color:var(--dg-accent);box-shadow:var(--dg-focus-strong);outline:none;transform:translateY(-1px)}

.dgdb-wiki-flight-active,
.dgdb-wiki-info,
.dgdb-wiki-history{
  background:var(--dg-bg);border:1px solid var(--dg-border);
  border-radius:var(--dg-radius-sm);padding:12px;margin-bottom:12px;font-size:14px;
}
.dgdb-wiki-info em, .dgdb-wiki-history em{color:var(--dg-muted);font-style:italic}

@media (max-width:600px){
  .dgdb-flight-grid{grid-template-columns:repeat(2,1fr);gap:8px}
  .dgdb-wiki-card{padding:16px}
}

/* ---------- Collection Management ---------- */
.dgdb-collection-card{position:relative;transition:all .2s ease;cursor:grab}
.dgdb-collection-card:hover{transform:translateY(-1px)}
.dgdb-collection-controls{
  position:absolute;top:8px;right:8px;display:flex;gap:4px;opacity:0;transition:opacity .2s ease;z-index:2;
}
.dgdb-collection-card:hover .dgdb-collection-controls{opacity:1}
.dgdb-btn-icon{
  display:flex;align-items:center;justify-content:center;width:28px;height:28px;padding:0;
  border:1px solid var(--dg-border);border-radius:6px;background:var(--dg-surface);color:var(--dg-muted);cursor:pointer;transition:all .2s ease;
}
.dgdb-btn-icon:hover{background:var(--dg-accent);color:#fff;border-color:var(--dg-accent)}
.dgdb-delete-btn:hover{background:#ff453a;border-color:#ff453a}

.dgdb-custom-field{margin-top:4px;font-size:13px}
.dgdb-custom-field-name{color:var(--dg-muted);font-weight:500}
.dgdb-custom-field-value{color:var(--dg-ink);font-weight:600}

.dgdb-collection-header{display:flex;flex-direction:column;gap:16px;margin-bottom:24px}
.dgdb-collection-filters{display:flex;gap:12px;align-items:center;flex-wrap:wrap}
.dgdb-filter-group{position:relative;flex:1;min-width:200px}
.dgdb-filter-input{
  width:100%;height:40px;border:1px solid var(--dg-border);border-radius:10px;background:var(--dg-bg);color:var(--dg-ink);font-size:14px;transition:all .2s ease;
}
.dgdb-filter-input:focus{outline:none;border-color:var(--dg-accent);box-shadow:var(--dg-focus-strong)}
.dgdb-search-icon{position:absolute;right:12px;top:50%;transform:translateY(-50%);color:var(--dg-muted);pointer-events:none}
.dgdb-filter-select{
  height:40px;padding:0 12px;border:1px solid var(--dg-border);border-radius:10px;background:var(--dg-bg);color:var(--dg-ink);font-size:14px;cursor:pointer;transition:all .2s ease;min-width:140px;
}
.dgdb-filter-select:focus{outline:none;border-color:var(--dg-accent);box-shadow:var(--dg-focus-strong)}
.dgdb-filter-btn{
  height:40px;width:40px;border:1px solid var(--dg-border);border-radius:10px;background:var(--dg-bg);color:var(--dg-muted);cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;
}
.dgdb-filter-btn:hover,
.dgdb-filter-btn.active{background:var(--dg-accent);color:#fff;border-color:var(--dg-accent)}
.dgdb-favorite-btn{color:var(--dg-muted)}
.dgdb-favorite-btn:hover,
.dgdb-favorite-btn.favorited{background:#ffd700;color:#333;border-color:#ffd700}
.dgdb-favorite-btn.favorited svg{fill:currentColor}

.dgdb-collection-grid{position:relative}
.dgdb-collection-card.dragging{opacity:.5;transform:rotate(5deg);z-index:1000}
.dgdb-collection-card.drag-over{transform:translateY(-4px);box-shadow:0 8px 25px rgba(0,113,227,.2)}
.dgdb-collection-card:active{cursor:grabbing}
.dgdb-collection-card.favorited{border-color:var(--dg-accent);box-shadow:0 2px 8px rgba(0,113,227,.2)}

.dgdb-favorite-indicator{
  position:absolute;top:8px;right:8px;width:24px;height:24px;background:#ffd700;border-radius:50%;
  display:flex;align-items:center;justify-content:center;color:#333;box-shadow:0 2px 4px rgba(0,0,0,.1);z-index:10;
}

@media (max-width:768px){
  .dgdb-collection-header{gap:12px}
  .dgdb-collection-filters{flex-direction:column;align-items:stretch}
  .dgdb-filter-group{min-width:auto}
  .dgdb-filter-select,.dgdb-filter-btn{width:100%}
}

/* Personal Disc Modal (custom content shell inside base modal) */
.dgdb-modal-content{
  position:relative;width:100%;max-width:700px;max-height:90vh;background:var(--dg-bg);
  border-radius:16px;box-shadow:0 20px 40px rgba(0,0,0,.15);overflow:hidden;display:flex;flex-direction:column;
}
.dgdb-modal-header{
  display:flex;align-items:center;justify-content:space-between;padding:20px 24px;border-bottom:1px solid var(--dg-border);background:var(--dg-surface)
}
.dgdb-modal-header h3{margin:0;font-size:18px;font-weight:600;color:var(--dg-ink)}

/* Icon-style close specific to personal modal header */
.dgdb-modal-header .dgdb-modal-close{
  width:32px;height:32px;border:none;background:none;font-size:24px;color:var(--dg-muted);
  cursor:pointer;border-radius:6px;display:flex;align-items:center;justify-content:center;transition:all .2s ease;
}
.dgdb-modal-header .dgdb-modal-close:hover{background:var(--dg-border);color:var(--dg-ink)}

.dgdb-modal-body{flex:1;overflow-y:auto;padding:16px}
.dgdb-personal-display{display:flex;gap:24px;margin-bottom:24px}
.dgdb-personal-image-display{flex-shrink:0;width:120px}
.dgdb-personal-image-display img{width:100%;height:120px;object-fit:cover;border-radius:12px;border:1px solid var(--dg-border)}
.dgdb-image-placeholder{width:100%;height:120px;border:2px dashed var(--dg-border);border-radius:12px;display:flex;align-items:center;justify-content:center;color:var(--dg-muted);font-size:14px;text-align:center}
.dgdb-personal-info-display{flex:1}
.dgdb-info-row{margin-bottom:16px}
.dgdb-info-row strong{display:block;margin-bottom:4px;color:var(--dg-muted);font-size:14px;font-weight:500}
.dgdb-info-row span,.dgdb-info-row div{color:var(--dg-ink);font-size:15px}

.dgdb-image-upload-area{position:relative}
.dgdb-current-image{position:relative;display:inline-block;margin-bottom:12px}
.dgdb-current-image img{width:120px;height:120px;object-fit:cover;border-radius:12px;border:1px solid var(--dg-border)}
.dgdb-remove-image-btn{
  position:absolute;top:-8px;right:-8px;width:24px;height:24px;background:#ff453a;color:#fff;border:2px solid var(--dg-bg);border-radius:50%;
  font-size:16px;line-height:1;cursor:pointer;
}
.dgdb-upload-hint{font-size:13px;color:var(--dg-muted);margin-top:8px}
.dgdb-modal-actions{display:flex;gap:12px;justify-content:flex-end;padding-top:20px;border-top:1px solid var(--dg-border);margin-top:20px}

@media (max-width:768px){
  .dgdb-personal-display{flex-direction:column;gap:16px}
  .dgdb-personal-image-display{width:100px}
  .dgdb-personal-image-display img,.dgdb-image-placeholder{height:100px}
  .dgdb-current-image img{width:100px;height:100px}
}

/* Custom Fields Interface */
.dgdb-custom-fields-header{display:flex;align-items:center;justify-content:space-between;margin-bottom:16px}
.dgdb-custom-fields-header .dgdb-label{margin:0;font-size:15px;font-weight:600;color:var(--dg-ink)}
.dgdb-add-field-btn{
  background:var(--dg-accent);color:#fff;border-color:var(--dg-accent);width:32px;height:32px;border-radius:8px;
  display:flex;align-items:center;justify-content:center;transition:all .2s ease;
}
.dgdb-add-field-btn:hover{
  background:color-mix(in oklab, var(--dg-accent) 85%, black);
  border-color:color-mix(in oklab, var(--dg-accent) 85%, black);
  transform:scale(1.05);
}

.dgdb-custom-fields-container{
  min-height:60px;border:2px dashed var(--dg-border);border-radius:12px;padding:16px;
  background:color-mix(in oklab, var(--dg-surface) 50%, transparent);transition:all .2s ease;
}
.dgdb-custom-fields-container:has(.dgdb-custom-field-row){
  border-style:solid;background:var(--dg-surface);
}

.dgdb-custom-fields-empty{ text-align:center;color:var(--dg-muted);font-size:14px;padding:20px;display:flex;flex-direction:column;align-items:center;gap:8px }

.dgdb-custom-field-row{
  display:flex;flex-direction:column;gap:12px;align-items:flex-start;margin-bottom:16px;padding:16px;
  border:1px solid var(--dg-border);border-radius:12px;background:var(--dg-bg);position:relative;transition:all .2s ease;
}
.dgdb-custom-field-row:hover{border-color:var(--dg-accent);box-shadow:0 2px 8px rgba(0,113,227,.1)}
.dgdb-custom-field-row:last-child{margin-bottom:0}

.dgdb-field-group{flex:1;min-width:0;width:95%}
.dgdb-field-group label{display:block;font-size:13px;font-weight:500;color:var(--dg-muted);margin-bottom:4px}
.dgdb-field-group input,
.dgdb-field-group select{
  width:100%;height:36px;padding:0 12px;border:1px solid var(--dg-border);border-radius:8px;background:var(--dg-bg);color:var(--dg-ink);font-size:14px;transition:border-color .2s ease;
}
.dgdb-field-group input:focus,
.dgdb-field-group select:focus{outline:none;border-color:var(--dg-accent);box-shadow:var(--dg-focus-strong)}

.dgdb-field-type-group{flex-shrink:0;width:80px}

.dgdb-remove-field-btn{
  position:absolute;top:-8px;right:-8px;width:24px;height:24px;background:#ff453a;color:#fff;border:2px solid var(--dg-bg);
  border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;cursor:pointer;transition:all .2s ease;opacity:0;
}
.dgdb-custom-field-row:hover .dgdb-remove-field-btn{opacity:1}
.dgdb-remove-field-btn:hover{background:color-mix(in oklab, #ff453a 85%, black);transform:scale(1.1)}
@media (max-width:768px){
  .dgdb-custom-field-row{flex-direction:column;gap:12px}
  .dgdb-field-type-group{width:100%}
  .dgdb-remove-field-btn{position:static;align-self:flex-end;opacity:1;margin-top:8px}
}

/* Profile collapsible chevron */
.dgdb-profile-section-summary .dgdb-chevron{transition:transform .2s ease;display:inline-block}
details[open] .dgdb-profile-section-summary .dgdb-chevron{transform:rotate(90deg)}

/* Odds & ends */
#flex-test{display:flex;flex-direction:row;gap:12px;justify-content:space-evenly}
#flight-rating-card{width:95%!important}

/* Layout & form (theme-aware additions) */
.dgdb-form{display:grid;gap:12px}
.dgdb-form-row{display:grid;align-items:center}
.dgdb-form-label-fixed{font-weight:600;color:var(--dg-ink);margin-bottom:5px}

/* Fieldset */
.dgdb-fieldset{border:1px solid var(--dg-border);border-radius:var(--dg-radius-lg);padding:10px 12px 12px;background:var(--dg-bg)}
.dgdb-fieldset>legend{padding:0 6px;font-weight:600;color:var(--dg-ink)}

/* Flight number grid */
.dgdb-fn-actions{display:flex;gap:8px;justify-content:flex-end;margin-bottom:6px}
.dgdb-fn-grid{display:grid;grid-template-columns:repeat(4,minmax(0,1fr));gap:10px}
@media (max-width:900px){.dgdb-fn-grid{grid-template-columns:repeat(2,minmax(0,1fr))}}
.dgdb-fn-cell{display:grid;gap:6px}
.dgdb-fn-label{font-size:12px;color:var(--dg-muted)}
.dgdb-input--num{text-align:center;font-variant-numeric:tabular-nums}
.dgdb-range{width:100%}
.dgdb-fn-preview{display:flex;flex-wrap:wrap;gap:8px;margin-top:6px}

/* Submit spinner */
.dgdb-btn-spinner{width:14px;height:14px;border-radius:999px;border:2px solid transparent;border-top-color:currentColor;display:none}
.dgdb-is-submitting .dgdb-btn-primary .dgdb-btn-spinner{display:inline-block;animation:dgdbspin .9s linear infinite}
@keyframes dgdbspin{to{transform:rotate(360deg)}}

/* Status toast */
.dgdb-status{min-height:1em;margin-left:10px}
.dgdb-status--ok{color:var(--dg-accent)}
.dgdb-status--err{color:#ff453a}

/* Review item (legacy list) */
.dgdb-review-item{padding:12px;border:1px solid var(--dg-border);border-radius:var(--dg-radius-md);background:var(--dg-bg)}
.dgdb-review-item + .dgdb-review-item{margin-top:10px}
.dgdb-review-item__head{display:flex;justify-content:space-between;gap:8px}
.dgdb-review-item__stars{color:#f59e0b}
.dgdb-review-item__body{margin-top:6px;white-space:pre-wrap}
.dgdb-form-grid-review{display:grid;gap:8px}

/* Bag Display (collection summary) */
.dgdb-bag-display{margin-bottom:24px}
.dgdb-section-title{font-size:20px;font-weight:600;color:var(--dg-ink);margin-bottom:10px}
.dgdb-bag-summary{border-radius:12px;padding:20px;border:1px solid var(--dg-border)}
.dgdb-bag-basics-display{display:flex;flex-wrap:wrap;gap:16px;padding-bottom:16px}
.dgdb-bag-item{font-size:14px;color:var(--dg-ink)}
.dgdb-bag-item strong{color:var(--dg-muted);font-weight:500}
.dgdb-bag-contents-table{overflow-x:auto}
.dgdb-bag-display-table{width:100%;border-collapse:collapse;font-size:14px}
.dgdb-bag-display-table th{
  background:var(--dg-bg);color:var(--dg-muted);font-weight:500;padding:12px 8px;text-align:left;border-bottom:2px solid var(--dg-border);
  font-size:13px;text-transform:uppercase;letter-spacing:.5px;
}
.dgdb-bag-display-table td{padding:10px 8px;border-bottom:1px solid var(--dg-border);color:var(--dg-ink)}
.dgdb-bag-display-table tbody tr:hover{background:color-mix(in oklab, var(--dg-accent) 5%, transparent)}
.dgdb-bag-display-table .dgdb-disc-type{font-weight:500;color:var(--dg-accent);text-transform:capitalize}
.dgdb-bag-display-table .dgdb-disc-name{font-weight:500}
.dgdb-bag-display-table .dgdb-disc-notes{color:var(--dg-muted);font-style:italic}
.dgdb-bag-display-table .dgdb-disc-qty{font-weight:500}
.dgdb-bag-empty{text-align:center;padding:40px 20px;color:var(--dg-muted)}
.dgdb-bag-empty p{margin:0;font-size:14px}
@media (max-width:768px){
  .dgdb-bag-basics-display{flex-direction:column;gap:8px}
  .dgdb-bag-display-table th,.dgdb-bag-display-table td{padding:8px 6px}
  .dgdb-bag-display-table{font-size:13px}
}

/* Chips */
.dgdb-chip-row{display:flex;flex-wrap:wrap;gap:8px;margin:6px 0}
.dgdb-chip{display:inline-flex;align-items:center;gap:6px;padding:6px 10px;border:1px solid var(--dg-border);border-radius:999px;background:var(--wp--preset--color--surface);color:var(--wp--preset--color--ink);font-size:12px}
.dgdb-chip .remove{background:transparent;border:0;cursor:pointer;color:var(--wp--preset--color--muted);padding:0 2px}

/* Multi-select dropdown */
.dgdb-mselect{position:relative;}
.dgdb-mselect-btn{
  display:flex;align-items:center;gap:8px;justify-content:space-between;width:100%;
  height:40px;padding:0 12px;border-radius:12px;border:1px solid var(--dg-border);
  background:var(--wp--preset--color--bg);color:var(--wp--preset--color--ink);cursor:pointer;
}
.dgdb-mselect-btn:focus{outline:none;border-color:var(--wp--preset--color--accent);
  box-shadow:0 0 0 3px color-mix(in oklab, var(--wp--preset--color--accent) 15%, transparent);}
.dgdb-caret{margin-left:auto}
.dgdb-mselect-panel{
  position:absolute;left:0;right:0;top:100%;margin-top:8px;z-index:30;
  background:var(--wp--preset--color--bg);color:var(--wp--preset--color--ink);
  border:1px solid var(--dg-border);border-radius:12px;box-shadow:0 6px 18px rgba(0,0,0,.08);
  max-height:260px;overflow:auto;padding:6px;
}
.dgdb-mopt{display:flex;align-items:center;gap:10px;padding:6px 8px;border-radius:8px;cursor:pointer}
.dgdb-mopt:hover{background:var(--wp--preset--color--surface)}
.dgdb-mopt input{pointer-events:none}

/* Custom add row */
.dgdb-custom-add{display:flex;gap:8px;margin-top:10px;}
.dgdb-combobox-input{
  flex:1;height:38px;padding:0 10px;border-radius:12px;border:1px solid var(--dg-border);
  background:var(--wp--preset--color--bg);color:var(--wp--preset--color--ink);
}
.dgdb-combobox-input:focus{outline:none;border-color:var(--wp--preset--color--accent);
  box-shadow:0 0 0 3px color-mix(in oklab, var(--wp--preset--color--accent) 15%, transparent);}

/* Small count pill */
.dgdb-mselect-count{margin-left:auto;font-size:12px;color:var(--wp--preset--color--muted)}

/* ===============================
   Flight Chart — Landscape style
   =============================== */

/* Card & canvas sizing */
.dgdb-flight.chart-card {
  padding: 12px;
  border-radius: 14px;
}
.chart-card .flight-svg-wrap.dgdb-flight-chart,
.chart-card .flight-svg-wrap,
.flight-svg-wrap {
  width: 100%;
  height: clamp(220px, 28vw, 340px);   /* responsive height */
  min-height: 220px;
}
.dgdb-flight.chart-card.compact .flight-svg-wrap {
  height: clamp(180px, 24vw, 280px);
}

/* Controls */
.dgdb-flight .flight-head {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 12px;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.dgdb-flight .flight-controls .dgdb-btn {
  padding: 4px 8px;
  border-radius: 20px;
  line-height: 1;
  font-size: 12px;
  margin-right: 5px;
}

/* SVG root */
.fc-svg { display: block; }

/* Theme tokens (uses your existing variables) */
:root{
  --fc-ink: var(--wp--preset--color--ink, #0a0a0a);
  --fc-muted: var(--wp--preset--color--muted, #6e6e73);
  --fc-bg: var(--wp--preset--color--bg, #fff);
  --fc-border: var(--dg-border, #e5e5ea);
  --fc-line-start: #1f5d74;
  --fc-line-end:   #2d7a96;
}
:root[data-theme="dark"]{
  --fc-ink: #f5f5f7;
  --fc-muted: #9a9aa0;
  --fc-border: #2a2a2e;
}

/* Grid & axes */
.fc-grid {
  stroke: currentColor;
  stroke-opacity: .08;
  stroke-width: 1;
  shape-rendering: crispEdges;
}
.fc-grid--minor { opacity: .45; stroke-dasharray: 2 6; }

.fc-title {
  font-weight: 800;
  font-size: var(--fc-title, 18px);
  fill: var(--fc-ink);
}
.dgdb-flight.chart-card.compact .fc-title { font-size: 16px; }

.fc-axis {
  font-size: var(--fc-label, 11px);
  fill: var(--fc-ink);
  opacity: .8;
}
.fc-tick  {
  font-size: var(--fc-label, 11px);
  fill: var(--fc-ink);
  opacity: .6;
}

/* Curve & baseline
   NOTE: stroke color/gradient is injected by JS via <linearGradient> */
.fc-line {
  fill: none;
  stroke-width: var(--fc-stroke, 4.5);
  stroke-linecap: round;           /* clean round end */
}

/* Faint straight baseline for reference */
.fc-base {
  fill: none;
  stroke: var(--fc-border);
  stroke-dasharray: 4 6;
  opacity: .6;
}

/* Start dot (subtle) */
.fc-dot { fill: var(--fc-line-start); }

/* Hover probe + label */
.fc-probe {
  fill: var(--fc-line-end);
}
.fc-probe-label {
  font-size: var(--fc-label, 12px);
  font-weight: 600;
  fill: var(--fc-muted);
  paint-order: stroke;               /* outline first for contrast */
  stroke: var(--fc-bg);
  stroke-width: 3px;                 /* slimmer halo for readability */
}

/* Layout nicety when charts live inside spec cards */
.dgdb-spec-card .chart-card { margin-top: 10px; }

/* Make the SVG fill the holder completely */
.flight-svg-wrap { width: 100%; height: clamp(240px, 30vw, 380px); }
.flight-svg-wrap > svg { width: 100%; height: 100%; display:block; }

/* Controls – three groups inline, with visible spacing */
.dgdb-flight .flight-head {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-evenly;
  margin-bottom: 8px;
}
.dgdb-flight .flight-controls {
  display: flex; flex-wrap: wrap;
  padding-right: 10px;
}
.dgdb-flight .flight-controls:last-child { border-right: 0; padding-right: 0; }

/* Thicker default stroke (JS also scales this responsively) */
:root { --fc-stroke: 6; }                      /* base */
.dgdb-flight.chart-card.compact { --fc-stroke: 5; }

/* Keep your existing grid/axis/labels rules… only key parts shown here */
.fc-line { stroke-width: var(--fc-stroke); stroke-linecap: round; fill:none; }
.fc-grid { stroke: currentColor; stroke-opacity: .08; stroke-width: 1; }
.fc-axis, .fc-tick { font-size: var(--fc-label, 12px); }

/* --- Signature look additions ------------------------------------------ */

/* Path + wake */
.fc-line { filter: url(#fc-glow-var); }
.fc-wake {
  stroke: var(--fc-wake, currentColor);
  stroke-opacity: .18;
  stroke-width: calc(var(--fc-stroke) + 6);
  fill: none;
  stroke-linecap: round;
}

/* The “lane” under the curve for contrast */
.fc-lane {
  fill: none;
  stroke: color-mix(in oklab, var(--wp--preset--color--accent, #0a84ff) 10%, transparent);
  stroke-width: calc(var(--fc-stroke) + 14);
  stroke-linecap: round;
  opacity: .08;
}

/* Start pulse + landing target */
.fc-start-pulse {
  fill: var(--wp--preset--color--accent, #0a84ff);
  opacity: .16;
  transform-box: fill-box;
  transform-origin: center;
  animation: fc-pulse 2.4s ease-out infinite;
}
@keyframes fc-pulse {
  0%   { transform: scale(0.7); opacity: .20; }
  70%  { transform: scale(1.6); opacity: .02; }
  100% { transform: scale(1.6); opacity: .00; }
}
.fc-target-ring { fill: none; stroke: currentColor; opacity: .15; }
.fc-target-ring--2 { opacity: .10; }
.fc-target-ring--3 { opacity: .06; }

/* Taper mask softens both ends of the line */
.fc-taper { mask: url(#fc-taper-mask); }

/* Respect reduced motion */
@media (prefers-reduced-motion: reduce) {
  .fc-start-pulse { animation: none; opacity: .08; }
}

/* Size (landscape) */
.chart-card .flight-svg-wrap.dgdb-flight-chart,
.chart-card .flight-svg-wrap {
  width: 100%;
  height: clamp(240px, 30vw, 360px);
  min-height: 240px;
}

/* SVG basics */
.fc-svg { display: block; }

/* Grid */
.fc-grid {
  stroke: currentColor;
  stroke-opacity: .10;
  stroke-width: 1;
  shape-rendering: crispEdges;
}
.fc-grid--minor { stroke-opacity: .06; stroke-dasharray: 2 6; }

/* Labels */
.fc-title { font-weight: 800; font-size: var(--fc-title, 20px); fill: currentColor; }
.fc-axis, .fc-tick { font-size: var(--fc-label, 12px); fill: currentColor; opacity: .75; }
.fc-tick { opacity: .6; }

/* Baseline */
.fc-base {
  stroke: currentColor;
  stroke-opacity: .15;
  stroke-dasharray: 4 6;
  fill: none;
}

/* Main line and wake */
.fc-line {
  /* stroke is set from JS (gradient) */
  stroke-width: var(--fc-stroke, 6);
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
}
.fc-wake {
  stroke: currentColor;
  stroke-opacity: .18;
  stroke-width: calc(var(--fc-stroke, 6) * 1.7);
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  color: var(--wp--preset--color--accent, #0a84ff);
}

/* Start & landing */
.fc-dot { fill: var(--wp--preset--color--accent, #0a84ff); }
.fc-target-ring {
  stroke: var(--wp--preset--color--accent, #0a84ff);
  fill: none;
  opacity: .16;
}
.fc-target-ring--2 { opacity: .12; }
.fc-target-ring--3 { opacity: .08; }

/* Probe */
.fc-probe {
  fill: var(--wp--preset--color--accent, #0a84ff);
}
.fc-probe-label {
  font-size: 11px;
  font-weight: 600;
  fill: currentColor;
  paint-order: stroke;
  stroke: var(--wp--preset--color--bg, #fff);
  stroke-width: 5px; /* halo */
}

/* Y-axis numeric ticks */
.fc-ytick { font-size: 11px; opacity: .65; fill: currentColor; }
.fc-ytick-line { stroke: currentColor; stroke-opacity: .25; stroke-width: 1; }

/* Y-axis captions */
.fc-ycap { font-size: 10.5px; opacity: .6; fill: currentColor; }
.fc-ycap--fade { opacity: .70; }
.fc-ycap--turn { opacity: .70; }

/* Y-axis numeric ticks (left & right) */
.fc-ytick { font-size: 11px; opacity: .65; fill: currentColor; }
.fc-ytick--left  { text-anchor: end; }
.fc-ytick--right { text-anchor: start; }

.fc-ytick-line { stroke: currentColor; stroke-opacity: .25; stroke-width: 1; }

/* Axis captions at the top */
.fc-ycap { font-size: 11px; opacity: .7; fill: currentColor; font-weight: 600; }
.fc-ycap--turn { }
.fc-ycap--fade { }

/* ensure there’s room for the right labels */
.chart-card .flight-svg-wrap { padding-right: 8px; }


