(function () {
  // --- Small helpers ---
  const $ = (s, el) => (el || document).querySelector(s);
  const $$ = (s, el) => Array.from((el || document).querySelectorAll(s));
  const esc = (t) => { const d = document.createElement('div'); d.textContent = t == null ? '' : String(t); return d.textContent; };

  // --- One-time init guard (prevents double execution if script is loaded twice) ---
  if (window.__DGDB_PROFILE_INIT__) return;
  window.__DGDB_PROFILE_INIT__ = true;

  const root = document.querySelector('.dgdb-profile-wrap');
  if (!root) return;

  const ajaxUrl =
    (window.DGDB && DGDB.ajax) ||
    window.ajaxurl ||
    (document.querySelector('script[data-ajaxurl]')?.getAttribute('data-ajaxurl')) ||
    '/wp-admin/admin-ajax.php';

  /* -----------------------------
   * Favorite disc – search or free text
   * ----------------------------- */
  const favInput  = $('#dgdb-favorite-disc-input');
  const favHidden = $('#dgdb-favorite-disc-id');
  const favList   = $('#dgdb-fav-suggest');
  let favTimer = 0;

  async function searchDiscs(q) {
    const p = new URLSearchParams({ action: 'dgdb_disc_search', q });
    const res = await fetch(ajaxUrl + '?' + p.toString(), { credentials: 'same-origin' });
    if (!res.ok) return [];
    const data = await res.json();
    return data && data.success ? (data.data || []) : [];
  }

  function showFavResults(items) {
    favList.innerHTML = '';
    if (!items.length) { favList.style.display = 'none'; return; }
    items.forEach(it => {
      const b = document.createElement('button');
      b.type = 'button';
      b.textContent = `${it.manufacturer} ${it.name}`;
      b.addEventListener('click', () => {
        favInput.value  = `${it.manufacturer} ${it.name}`;
        favHidden.value = String(it.id);
        favList.style.display = 'none';
      });
      favList.appendChild(b);
    });
    favList.style.display = 'block';
  }

  if (favInput && favHidden && favList) {
    favInput.addEventListener('input', () => {
      favHidden.value = '';
      const q = favInput.value.trim();
      clearTimeout(favTimer);
      if (q.length < 2) { favList.style.display = 'none'; return; }
      favTimer = setTimeout(async () => showFavResults(await searchDiscs(q)), 150);
    });
    document.addEventListener('click', (e) => {
      if (!favList.contains(e.target) && e.target !== favInput) favList.style.display = 'none';
    });
  }

  /* -----------------------------
   * Bag display and builder
   * ----------------------------- */

  function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  const sections = [
    { key: 'distance_driver', title: 'Distance Driver' },
    { key: 'fairway_driver',  title: 'Fairway Driver'  },
    { key: 'midrange',        title: 'Midrange'        },
    { key: 'approach',        title: 'Approach'        },
    { key: 'putter',          title: 'Putters'         },
    { key: 'specialty',       title: 'Specialty'       },
  ];

  function updateBagDisplay() {
    const tbody = document.getElementById('dgdb-bag-display-tbody');
    const emptyState = document.getElementById('dgdb-bag-empty-state');
    if (!tbody) return;

    tbody.innerHTML = '';

    let totalDiscs = 0;
    const sectionTitles = {
      'distance_driver': 'Distance Driver',
      'fairway_driver': 'Fairway Driver',
      'midrange': 'Midrange',
      'approach': 'Approach',
      'putter': 'Putter',
      'specialty': 'Specialty'
    };

    // Collect and consolidate discs from all sections
    const discMap = new Map();

    sections.forEach(section => {
      const secEl = document.querySelector(`.dgdb-bag-sec[data-key="${section.key}"]`);
      if (!secEl) return;

      const rows = secEl.querySelectorAll('.dgdb-bag-table tbody tr');
      rows.forEach(row => {
        const nameInput = row.querySelector('.dgdb-disc-name');
        const notesInput = row.querySelector('.dgdb-notes');
        const qtyInput   = row.querySelector('.dgdb-qty');
        const idInput    = row.querySelector('.dgdb-disc-id');

        const name  = (nameInput?.value || '').trim();
        const notes = (notesInput?.value || '').trim();
        const qty   = parseInt(qtyInput?.value || '1', 10) || 1;
        const discId = parseInt(idInput?.value || '0', 10) || 0;

        if (!name) return;

        // Prefer a stable key by disc_id; fall back to name lowercased
        const key = discId ? `id:${discId}` : `name:${name.toLowerCase()}`;

        if (discMap.has(key)) {
          const existing = discMap.get(key);
          existing.qty += qty;
          // Combine unique notes (simple containment guard)
          if (notes && !existing.notes.includes(notes)) {
            existing.notes = existing.notes ? `${existing.notes}, ${notes}` : notes;
          }
        } else {
          discMap.set(key, {
            name,
            type: sectionTitles[section.key] || section.key,
            notes,
            qty
          });
        }
      });
    });

    // Render consolidated table
    discMap.forEach(disc => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td class="dgdb-disc-type">${disc.type}</td>
        <td class="dgdb-disc-name">${escapeHtml(disc.name)}</td>
        <td class="dgdb-disc-notes">${disc.notes ? escapeHtml(disc.notes) : '-'}</td>
        <td class="dgdb-disc-qty">${disc.qty}</td>
      `;
      tbody.appendChild(tr);
      totalDiscs += disc.qty;
    });

    // Empty state toggle
    if (totalDiscs === 0) {
      emptyState.style.display = 'block';
      tbody.parentElement.style.display = 'none';
    } else {
      emptyState.style.display = 'none';
      tbody.parentElement.style.display = 'table';
    }
  }

  function addRow(secEl, value = {}) {
    const tbody = $('tbody', secEl);
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td style="min-width:220px;">
        <div class="dgdb-suggest">
          <input class="dgdb-input dgdb-disc-name" placeholder="Disc (search or type)" value="${esc(value.name||'')}">
          <input type="hidden" class="dgdb-disc-id" value="${value.disc_id ? String(value.disc_id) : ''}">
          <div class="dgdb-suggest-list"></div>
        </div>
      </td>
      <td><input class="dgdb-input dgdb-notes" placeholder="Notes / plastic" value="${esc(value.notes||'')}"></td>
      <td style="width:90px;"><input type="number" class="dgdb-input dgdb-qty" min="1" value="${value.qty ? String(value.qty) : '1'}"></td>
      <td class="dgdb-row-del"><button type="button" class="dgdb-btn dgdb-del">Remove</button></td>
    `;
    tbody.appendChild(tr);

    const nameInput = $('.dgdb-disc-name', tr);
    const idInput   = $('.dgdb-disc-id', tr);
    const list      = $('.dgdb-suggest-list', tr);
    let t = 0;

    nameInput.addEventListener('input', () => {
      idInput.value = '';
      const q = nameInput.value.trim();
      clearTimeout(t);
      if (q.length < 2) { list.style.display='none'; return; }
      t = setTimeout(async () => {
        const items = await searchDiscs(q);
        list.innerHTML = '';
        if (!items.length){ list.style.display='none'; return;}
        items.forEach(it=>{
          const b = document.createElement('button');
          b.type='button';
          b.textContent = `${it.manufacturer} ${it.name}`;
          b.addEventListener('click', ()=>{
            nameInput.value = `${it.manufacturer} ${it.name}`;
            idInput.value = String(it.id);
            list.style.display='none';
          });
          list.appendChild(b);
        });
        list.style.display = 'block';
      }, 150);
    });

    document.addEventListener('click', (e)=>{
      if (!tr.contains(e.target)) list.style.display='none';
    });

    $('.dgdb-del', tr).addEventListener('click', () => {
      tr.remove();
      updateBagDisplay();
    });

    // Update display when inputs change
    nameInput.addEventListener('input', () => { setTimeout(updateBagDisplay, 100); });
    $('.dgdb-notes', tr).addEventListener('input', updateBagDisplay);
    $('.dgdb-qty',   tr).addEventListener('input', updateBagDisplay);

    // Initial update
    setTimeout(updateBagDisplay, 50);
  }

  // Wire "Add disc" buttons
  $$('.dgdb-bag-sec').forEach(sec => {
    const addBtn = $('.dgdb-add', sec);
    if (addBtn) addBtn.addEventListener('click', () => addRow(sec));
  });

  // Load saved bag data (with dedupe to prevent double rows if init ever ran twice in the past)
  const bagDataEl = $('#dgdb-bag-json');
  if (bagDataEl && bagDataEl.value) {
    try {
      const saved = JSON.parse(bagDataEl.value) || {};
      sections.forEach(s => {
        const secEl = document.querySelector(`.dgdb-bag-sec[data-key="${s.key}"]`);
        const list = Array.isArray(saved[s.key]) ? saved[s.key] : [];

        // Dedupe: key by disc_id if present; else by name+notes+qty to be conservative
        const seen = new Set();
        list.forEach(item => {
          const id  = parseInt(item.disc_id || '0', 10) || 0;
          const key = id
            ? `id:${id}|notes:${(item.notes||'')}`
            : `name:${(item.name||'').toLowerCase()}|notes:${(item.notes||'')}|qty:${item.qty||1}`;
          if (seen.has(key)) return;
          seen.add(key);
          addRow(secEl, item);
        });
      });
    } catch (_) { /* ignore */ }
  }

  // Initial bag display update
  setTimeout(updateBagDisplay, 100);

  /* -----------------------------
   * Save profile
   * ----------------------------- */
  const saveBtn = $('#dgdb-save-profile');
  const status  = $('#dgdb-save-status');

  if (saveBtn) saveBtn.addEventListener('click', async () => {
    if (!(window.DGDB && DGDB.loggedIn)) {
      alert('Please log in.');
      return;
    }

    saveBtn.disabled = true; if (status) status.textContent = 'Saving…';

    const bag = {};
    sections.forEach(s=>{
      const secEl = document.querySelector(`.dgdb-bag-sec[data-key="${s.key}"]`);
      bag[s.key] = $$('.dgdb-bag-table tbody tr', secEl).map(tr => ({
        disc_id: parseInt($('.dgdb-disc-id', tr).value || '0', 10) || 0,
        name: $('.dgdb-disc-name', tr).value || '',
        notes: $('.dgdb-notes', tr).value || '',
        qty: parseInt($('.dgdb-qty', tr).value || '1', 10) || 1,
      })).filter(it => it.name.trim() !== '' || it.disc_id);
    });

    const fd = new FormData();
    fd.append('action', 'dgdb_profile_update');
    fd.append('nonce', (window.DGDB && DGDB.nonce) ? DGDB.nonce : '');
    fd.append('pdga_number', $('#dgdb-pdga')?.value || '');
    fd.append('favorite_course', $('#dgdb-fav-course')?.value || '');
    fd.append('favorite_disc_id', favHidden?.value || '0');
    fd.append('favorite_disc_text', favInput?.value || '');
    fd.append('bag_name', $('#dgdb-bag-name')?.value || '');
    fd.append('mini_disc', $('#dgdb-mini')?.value || '');
    fd.append('accessories', $('#dgdb-accessories')?.value || '');
    fd.append('bag_json', JSON.stringify(bag));

    try{
      const res  = await fetch(ajaxUrl, { method:'POST', body: fd, credentials:'same-origin' });
      const data = await res.json();
      if (data && data.success) {
        if (status) status.textContent = 'Saved ✓';
      } else {
        if (status) status.textContent = (data && data.data) ? data.data : 'Error';
      }
    }catch(e){
      if (status) status.textContent = 'AJAX failed';
      console.warn(e);
    }finally{
      saveBtn.disabled = false;
    }
  });

  /* -----------------------------
   * Avatar/Banner upload (ratio locked in CSS)
   * ----------------------------- */
  function upload(kind, fileInput, imgTarget){
    if (!fileInput.files || !fileInput.files[0]) return;
    if (!(window.DGDB && DGDB.loggedIn)) { alert('Please log in.'); return; }

    const fd = new FormData();
    fd.append('action','dgdb_profile_upload');
    fd.append('nonce', (window.DGDB && DGDB.nonce) ? DGDB.nonce : '');
    fd.append('kind', kind);
    fd.append('file', fileInput.files[0]);

    fetch(ajaxUrl, { method: 'POST', body: fd, credentials:'same-origin' })
      .then(r=>r.json())
      .then(data=>{
        if (data && data.success && data.data && data.data.url && imgTarget) {
          imgTarget.src = data.data.url;
        } else if (data && !data.success && data.data) {
          alert(data.data);
        }
      })
      .catch(console.warn);
  }

  const avatarInput = $('#dgdb-avatar-input');
  const avatarImg   = $('#dgdb-avatar-img');
  if (avatarInput) avatarInput.addEventListener('change', ()=> upload('avatar', avatarInput, avatarImg));

  const bannerInput = $('#dgdb-banner-input');
  const bannerImg   = $('#dgdb-banner-img');
  if (bannerInput) bannerInput.addEventListener('change', ()=> upload('banner', bannerInput, bannerImg));
})();
