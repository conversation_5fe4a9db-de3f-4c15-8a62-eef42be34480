(function () {
  if (!window.DGDB_ONB) return;

  var ajax = DGDB_ONB.ajax;
  var nonce = DGDB_ONB.nonce;
  var steps = DGDB_ONB.steps || [];
  var copy = DGDB_ONB.copy || {};

  var root = document.getElementById('dgdb-onb-root');
  var openBtn = document.querySelector('[data-dgdb-onb-open]');

  function fetchProgress() {
    var fd = new FormData();
    fd.append('action', 'dgdb_tutorial_progress');
    fd.append('nonce', nonce);
    return fetch(ajax, { method: 'POST', credentials: 'same-origin', body: fd })
      .then(function (r) { return r.json(); })
      .catch(function () { return { success: false }; });
  }

  function build() {
    if (!root) return;
    root.innerHTML =
      '<div class="dgdb-onb-overlay" role="dialog" aria-modal="true" aria-label="XP tutorial wizard">' +
      '  <div class="dgdb-onb">' +
      '    <header class="dgdb-onb-head">' +
      '      <h1 class="dgdb-onb-title">Get XP: Quick Start</h1>' +
      '      <button class="dgdb-onb-close" aria-label="'+(copy.close||'Close')+'">×</button>' +
      '    </header>' +
      '    <div class="dgdb-onb-body">' +
      '      <nav class="dgdb-onb-nav">' +
      '        <ol class="dgdb-onb-steps" role="tablist"></ol>' +
      '      </nav>' +
      '      <section class="dgdb-onb-content">' +
      '        <div class="dgdb-onb-card">' +
      '          <div class="dgdb-onb-line" aria-hidden="true"><span></span></div>' +
      '          <h2 data-h></h2>' +
      '          <p data-d></p>' +
      '          <div class="dgdb-onb-done" data-done style="display:none;">✓ '+(copy.done||'Completed')+'</div>' +
      '          <div class="dgdb-onb-how"><strong>How:</strong> <span data-how></span></div>' +
      '        </div>' +
      '      </section>' +
      '    </div>' +
      '    <footer class="dgdb-onb-foot">' +
      '      <div class="dgdb-onb-actions">' +
      '        <a href="#" class="dgdb-btn dgdb-btn-ghost" data-back>&larr; '+(copy.back||'Back')+'</a>' +
      '      </div>' +
      '      <div class="dgdb-onb-actions">' +
      '        <a href="#" class="dgdb-btn" data-cta>'+ (steps[0] && steps[0].cta ? steps[0].cta.label : 'Open') +'</a>' +
      '        <button class="dgdb-btn" data-check>'+ (copy.check||'Check') +'</button>' +
      '        <button class="dgdb-btn dgdb-btn-primary" data-next>'+ (copy.next||'Next') +' &rarr;</button>' +
      '      </div>' +
      '    </footer>' +
      '  </div>' +
      '</div>' +
      '<canvas class="dgdb-onb-canvas" width="'+window.innerWidth+'" height="'+window.innerHeight+'"></canvas>';

    // Build step list
    var list = root.querySelector('.dgdb-onb-steps');
    steps.forEach(function (s, idx) {
      var li = document.createElement('li');
      li.className = 'dgdb-onb-step';
      li.setAttribute('role','tab');
      li.setAttribute('data-k', s.key);
      li.innerHTML =
        '<div class="idx">'+(idx+1)+'</div>' +
        '<div class="meta"><span class="ttl">'+s.title+'</span><span class="xp">+'+s.xp+' XP</span></div>';
      list.appendChild(li);
    });
  }

  // Confetti (tiny, no dependency)
  function confettiBurst() {
    var canvas = root.querySelector('.dgdb-onb-canvas');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    var W = canvas.width = window.innerWidth;
    var H = canvas.height = window.innerHeight;
    var pieces = [];
    for (var i=0;i<80;i++) {
      pieces.push({
        x: W/2, y: H/3, r: Math.random()*6+2,
        c: 'hsl('+(Math.random()*360|0)+',95%,60%)',
        vx: (Math.random()-0.5)*6, vy: (Math.random()-0.5)*6-3, ay: 0.12, life: 60+Math.random()*30
      });
    }
    var frames = 0;
    (function tick(){
      ctx.clearRect(0,0,W,H);
      pieces.forEach(function(p){
        p.vy += p.ay; p.x += p.vx; p.y += p.vy; p.life--;
        ctx.beginPath(); ctx.fillStyle = p.c; ctx.arc(p.x,p.y,p.r,0,Math.PI*2); ctx.fill();
      });
      pieces = pieces.filter(function(p){ return p.life>0; });
      frames++;
      if (frames<120 && pieces.length) requestAnimationFrame(tick);
      else ctx.clearRect(0,0,W,H);
    })();
  }

  var state = {
    idx: 0,
    map: {},       // progress by key
    done: {},      // completed flags
  };

  function open() {
    build();
    root.hidden = false;
    root.setAttribute('aria-hidden','false');
    document.body.style.overflow = 'hidden';
    bind();
    go(0);
    refreshProgress(); // initial
  }

  function close() {
    if (!root) return;
    root.hidden = true;
    root.setAttribute('aria-hidden','true');
    document.body.style.overflow = '';
  }

  function bind() {
    root.querySelector('.dgdb-onb-close').addEventListener('click', close);
    root.addEventListener('click', function(e){
      if (e.target.classList.contains('dgdb-onb-overlay')) close();
    });
    document.addEventListener('keydown', function(e){
      if (root.hidden) return;
      if (e.key === 'Escape') close();
      if (e.key === 'ArrowRight') next();
      if (e.key === 'ArrowLeft') back();
      if (e.key === 'Enter') primary();
    });

    root.addEventListener('click', function(e){
      var el;
      if ((el = e.target.closest('[data-next]'))) { e.preventDefault(); next(); }
      else if ((el = e.target.closest('[data-back]'))) { e.preventDefault(); back(); }
      else if ((el = e.target.closest('[data-cta]'))) { e.preventDefault(); openCTA(); }
      else if ((el = e.target.closest('[data-check]'))) { e.preventDefault(); refreshProgress(true); }
      else if ((el = e.target.closest('.dgdb-onb-step'))) {
        e.preventDefault();
        var idx = Array.prototype.indexOf.call(root.querySelectorAll('.dgdb-onb-step'), el);
        if (idx >= 0) go(idx);
      }
    });
  }

  function setProgressBar() {
    var total = steps.length;
    var done = Object.values(state.done).filter(Boolean).length;
    var pct = total ? Math.round(done*100/total) : 0;
    var bar = root.querySelector('.dgdb-onb-line > span');
    if (bar) bar.style.width = pct+'%';
  }

  function go(i) {
    state.idx = Math.max(0, Math.min(steps.length-1, i));
    var step = steps[state.idx];
    root.querySelectorAll('.dgdb-onb-step').forEach(function(li, idx){
      li.setAttribute('aria-current', idx===state.idx ? 'step' : 'false');
      if (state.done[steps[idx].key]) li.classList.add('done'); else li.classList.remove('done');
    });

    root.querySelector('[data-h]').textContent = step.title + '  ·  +' + step.xp + ' XP';
    root.querySelector('[data-d]').textContent = step.desc;
    root.querySelector('[data-how]').textContent = step.how;

    var cta = root.querySelector('[data-cta]');
    if (step.cta) {
      cta.href = step.cta.href; cta.textContent = step.cta.label;
      cta.style.display = '';
    } else {
      cta.style.display = 'none';
    }

    var backBtn = root.querySelector('[data-back]');
    backBtn.style.visibility = state.idx === 0 ? 'hidden' : 'visible';

    updateControls();
    setProgressBar();
  }

  function updateControls() {
    var k = steps[state.idx].key;
    var done = !!state.done[k];
    var nextBtn = root.querySelector('[data-next]');
    var doneChip = root.querySelector('[data-done]');
    nextBtn.disabled = !done;
    doneChip.style.display = done ? 'inline-flex' : 'none';
    nextBtn.textContent = state.idx === steps.length-1 ? 'Finish' : (copy.next || 'Next') + ' →';
  }

  function next() {
    if (state.idx === steps.length-1) { close(); return; }
    go(state.idx+1);
  }
  function back() { go(state.idx-1); }
  function primary() {
    var k = steps[state.idx].key;
    if (state.done[k]) next(); else openCTA();
  }
  function openCTA() {
    var step = steps[state.idx];
    if (step.cta && step.cta.href) {
      window.open(step.cta.href, '_blank', 'noopener');
    }
  }

  function refreshProgress(showConfetti) {
    fetchProgress().then(function (res) {
      if (!res || !res.success) return;
      state.map = res.data.steps || {};
      var changedToDone = false;
      steps.forEach(function (s) {
        var was = !!state.done[s.key];
        var now = !!(state.map[s.key] && state.map[s.key].done);
        state.done[s.key] = now;
        if (now && !was) changedToDone = true;
      });
      updateControls();
      setProgressBar();
      root.querySelectorAll('.dgdb-onb-step').forEach(function(li, idx){
        var k = steps[idx].key;
        if (state.done[k]) li.classList.add('done'); else li.classList.remove('done');
      });
      if (showConfetti && changedToDone) confettiBurst();
    });
  }

  if (openBtn) openBtn.addEventListener('click', open);
})();
