(function () {
  function ready(cb) {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', cb, { once: true });
    } else { cb(); }
  }

  function clamp(v, min, max, step) {
    if (v === '' || v === null || isNaN(v)) return '';
    var num = Math.max(min, Math.min(max, parseFloat(v)));
    var snapped = Math.round(num / step) * step;
    return parseFloat(snapped.toFixed(2));
  }

  ready(function () {
    var root = document.querySelector('.dgdb-reviews');
    if (!root) return;

    var cfg    = (window.DGDB || {});
    var discId = parseInt(root.getAttribute('data-disc-id'), 10) || (cfg.discId || 0);
    var ajax   = cfg.ajax || '/wp-admin/admin-ajax.php';
    var nonce  = cfg.nonce || '';

    var form    = document.getElementById('dgdb-review-form');
    var rating  = document.getElementById('dgdb-rating');
    var textEl  = document.getElementById('dgdb-review');
    var status  = form ? form.querySelector('.dgdb-status') : null;
    var listEl  = document.getElementById('dgdb-review-list');
    var emptyEl = document.getElementById('dgdb-review-empty');
    var moreBtn = document.getElementById('dgdb-review-more');
    var submitBtn = form ? form.querySelector('button[type="submit"],input[type="submit"]') : null;

    // Title / Plastic / Weight
    var titleEl   = document.getElementById('dgdb-title');
    var plasticEl = document.getElementById('dgdb-plastic');
    var weightEl  = document.getElementById('dgdb-weight');

    // Flight numbers
    var spNum = document.getElementById('dgdb-fn-speed');
    var glNum = document.getElementById('dgdb-fn-glide');
    var tnNum = document.getElementById('dgdb-fn-turn');
    var fdNum = document.getElementById('dgdb-fn-fade');

    var spRng = document.getElementById('dgdb-fn-speed-range');
    var glRng = document.getElementById('dgdb-fn-glide-range');
    var tnRng = document.getElementById('dgdb-fn-turn-range');
    var fdRng = document.getElementById('dgdb-fn-fade-range');

    var btnUseMfg = document.getElementById('dgdb-fn-use-mfg');
    var btnClear  = document.getElementById('dgdb-fn-clear');

    // Live preview chips
    var prevSpeed = document.getElementById('dgdb-fn-prev-speed');
    var prevGlide = document.getElementById('dgdb-fn-prev-glide');
    var prevTurn  = document.getElementById('dgdb-fn-prev-turn');
    var prevFade  = document.getElementById('dgdb-fn-prev-fade');

    // Modal
    var modal      = document.getElementById('dgdb-review-modal');
    var modalTitle = modal ? modal.querySelector('.dgdb-modal-title') : null;
    var modalMeta  = modal ? modal.querySelector('.dgdb-modal-meta') : null;
    var modalStars = modal ? modal.querySelector('.dgdb-modal-stars') : null;
    var modalFN    = modal ? modal.querySelector('.dgdb-modal-flight') : null;
    var modalText  = modal ? modal.querySelector('.dgdb-modal-text') : null;

    var submitting = false;
    var page = 1, pages = 1, loading = false;

    // ---------- helpers ----------
    function syncPair(numEl, rngEl, min, max, step) {
      function fromNum() {
        var v = clamp(numEl.value, min, max, step);
        if (v === '') { rngEl.value = ''; updatePreview(); return; }
        numEl.value = v;
        rngEl.value = v;
        updatePreview();
      }
      function fromRange() {
        var v = clamp(rngEl.value, min, max, step);
        rngEl.value = v;
        numEl.value = v; // keep number in sync so FormData captures it
        updatePreview();
      }
      numEl.addEventListener('change', fromNum);
      numEl.addEventListener('blur', fromNum);
      rngEl.addEventListener('input', fromRange);
      rngEl.addEventListener('change', fromRange);
    }

    function setIf(val, numEl, rngEl) {
      if (val === '' || val === null || typeof val === 'undefined') return;
      var v = parseFloat(val);
      if (isNaN(v)) return;
      numEl.value = v;
      rngEl.value = v;
    }

    function clearAll() {
      [spNum, glNum, tnNum, fdNum, spRng, glRng, tnRng, fdRng].forEach(function (el) { if (el) el.value = ''; });
      updatePreview();
    }

    function chipText(val, label) {
      if (val === '' || val === null || typeof val === 'undefined' || isNaN(val)) return label + ' —';
      return label + ' ' + val;
    }

    function updatePreview() {
      if (prevSpeed) prevSpeed.textContent = chipText(spNum && spNum.value, 'Speed');
      if (prevGlide) prevGlide.textContent = chipText(glNum && glNum.value, 'Glide');
      if (prevTurn)  prevTurn.textContent  = chipText(tnNum && tnNum.value,  'Turn');
      if (prevFade)  prevFade.textContent  = chipText(fdNum && fdNum.value,  'Fade');
    }

    if (spNum && spRng) syncPair(spNum, spRng, 0, 15, 0.5);
    if (glNum && glRng) syncPair(glNum, glRng, 0, 7, 0.5);
    if (tnNum && tnRng) syncPair(tnNum, tnRng, -5, 2, 0.5);
    if (fdNum && fdRng) syncPair(fdNum, fdRng, 0, 5, 0.5);

    // Manufacturer values button (global first, then data-* fallback)
    if (btnUseMfg) {
      btnUseMfg.addEventListener('click', function (ev) {
        ev.preventDefault();

        var mfg = (window.DGDB && window.DGDB.mfgFlight) || {};
        var hasAny = mfg && (mfg.speed != null || mfg.glide != null || mfg.turn != null || mfg.fade != null);

        if (!hasAny && root && root.dataset) {
          mfg = {
            speed: root.dataset.mfgSpeed ? parseFloat(root.dataset.mfgSpeed) : undefined,
            glide: root.dataset.mfgGlide ? parseFloat(root.dataset.mfgGlide) : undefined,
            turn:  root.dataset.mfgTurn  ? parseFloat(root.dataset.mfgTurn)  : undefined,
            fade:  root.dataset.mfgFade  ? parseFloat(root.dataset.mfgFade)  : undefined
          };
        }

        setIf(mfg.speed, spNum, spRng);
        setIf(mfg.glide, glNum, glRng);
        setIf(mfg.turn,  tnNum, tnRng);
        setIf(mfg.fade,  fdNum, fdRng);
        updatePreview();
      });
    }

    if (btnClear) btnClear.addEventListener('click', function (ev) { ev.preventDefault(); clearAll(); });

    updatePreview();

    function toast(text, ok) {
      if (!status) return;
      status.textContent = text || '';
      status.classList.remove('dgdb-status--ok', 'dgdb-status--err');
      status.classList.add(ok ? 'dgdb-status--ok' : 'dgdb-status--err');
      if (text) {
        setTimeout(function () {
          status.textContent = '';
          status.classList.remove('dgdb-status--ok', 'dgdb-status--err');
        }, 1800);
      }
    }

    function esc(s) { return (s || '').toString().replace(/[&<>]/g, function (c) { return ({ '&': '&amp;', '<': '&lt;', '>': '&gt;' })[c]; }); }
    function stars(n) {
      n = Math.max(0, Math.min(5, parseInt(n || 0, 10)));
      var full = '&#9733;', empty = '&#9734;';
      return new Array(n + 1).join(full) + new Array(6 - n).join(empty);
    }

    function metaLine(it) {
      var bits = [];
      if (it.user) bits.push('<strong>' + esc(it.user) + '</strong>');
      if (it.created_at) bits.push('<span class="muted">' + esc(it.created_at) + '</span>');
      if (it.plastic_type) bits.push('<span class="muted">' + esc(it.plastic_type) + '</span>');
      if (it.weight) bits.push('<span class="muted">' + esc(String(it.weight)) + ' g</span>');
      return bits.join(' · ');
    }

    // -------- card + modal render --------
    function renderCard(it) {
      var title = (it.title && it.title.toString().trim()) ? esc(it.title) : null;
      var preview = (it.review || '').toString();
      var short = preview.length > 160 ? esc(preview.slice(0, 160)) + '…' : esc(preview);

      var fn = [];
      if (typeof it.speed !== 'undefined' && it.speed !== null) fn.push('Speed ' + esc(it.speed));
      if (typeof it.glide !== 'undefined' && it.glide !== null) fn.push('Glide ' + esc(it.glide));
      if (typeof it.turn  !== 'undefined' && it.turn  !== null) fn.push('Turn '  + esc(it.turn));
      if (typeof it.fade  !== 'undefined' && it.fade  !== null) fn.push('Fade '  + esc(it.fade));
      var fnHtml = fn.length ? '<div class="dgdb-review-card__fn">' + fn.join(' · ') + '</div>' : '';

      var html = ''
        + '<article class="dgdb-review-card" tabindex="0" role="button" aria-label="Open review" data-review=\'' + esc(JSON.stringify(it)) + '\'>'
        +   '<div class="dgdb-review-card__head">'
        +     '<div class="dgdb-review-card__stars" aria-hidden="true">' + stars(it.rating) + '</div>'
        +     '<div class="dgdb-review-card__meta">' + metaLine(it) + '</div>'
        +   '</div>'
        +   (title ? '<h4 class="dgdb-review-card__title">' + title + '</h4>' : '')
        +   (short ? '<p class="dgdb-review-card__text">' + short + '</p>' : '')
        +   fnHtml
        + '</article>';
      var wrap = document.createElement('div');
      wrap.innerHTML = html;
      return wrap.firstChild;
    }

    function openModal(it) {
      if (!modal) return;
      modalTitle.textContent = (it.title && it.title.toString().trim()) ? it.title : 'Review';
      modalMeta.innerHTML  = metaLine(it);
      modalStars.innerHTML = '<div class="dgdb-review-item__stars" aria-label="' + esc(it.rating) + ' stars">' + stars(it.rating) + '</div>';
      var fn = [];
      if (typeof it.speed !== 'undefined' && it.speed !== null) fn.push('Speed ' + esc(it.speed));
      if (typeof it.glide !== 'undefined' && it.glide !== null) fn.push('Glide ' + esc(it.glide));
      if (typeof it.turn  !== 'undefined' && it.turn  !== null) fn.push('Turn '  + esc(it.turn));
      if (typeof it.fade  !== 'undefined' && it.fade !== null) fn.push('Fade '  + esc(it.fade));
      modalFN.innerHTML   = fn.length ? '<div class="dgdb-modal-fn">' + fn.join(' · ') + '</div>' : '';
      modalText.textContent = (it.review || '');
      modal.removeAttribute('hidden');
      modal.setAttribute('aria-hidden', 'false');
      document.body.classList.add('dgdb-no-scroll');
      modal.querySelector('[data-close]')?.focus();
    }

    function closeModal() {
      if (!modal || modal.hasAttribute('hidden')) return;
      modal.setAttribute('hidden', '');
      modal.setAttribute('aria-hidden', 'true');
      document.body.classList.remove('dgdb-no-scroll');
    }

    if (modal) {
      modal.addEventListener('click', function (e) {
        if (e.target && (e.target.matches('[data-close]') || e.target.classList.contains('dgdb-review-modal__backdrop'))) {
          closeModal();
        }
      });
      document.addEventListener('keydown', function (e) {
        if (!modal || modal.hasAttribute('hidden')) return;
        if (e.key === 'Escape') closeModal();
      });
    }

    // -------- fetching & pagination --------
    function listPage(reset) {
      if (loading) return; loading = true;
      if (reset) { listEl.innerHTML = ''; page = 1; pages = 1; if (emptyEl) emptyEl.style.display = 'none'; }
      var url = new URL(ajax, window.location.origin);
      url.searchParams.set('action', 'dg_list_reviews');
      url.searchParams.set('disc_id', String(discId));
      url.searchParams.set('page', String(page));
      url.searchParams.set('per_page', '5');
      url.searchParams.set('_', String(Date.now()));
      fetch(url.toString(), { credentials: 'same-origin', cache: 'no-store' })
        .then(function (r) { return r.text(); })
        .then(function (t) { try { return JSON.parse(t); } catch (e) { throw { parse: true, text: t }; } })
        .then(function (json) {
          var data = (json && (json.data || json)) || {};
          if (!(json && json.success) && !data.items) { throw new Error('Could not fetch'); }
          var items = data.items || [];
          items.forEach(function (it) { var node = renderCard(it); if (node) listEl.appendChild(node); });
          // delegation
          listEl.addEventListener('click', function (e) {
            var card = e.target.closest('.dgdb-review-card');
            if (!card) return;
            try { openModal(JSON.parse(card.getAttribute('data-review') || '{}')); } catch (err) {}
          });
          listEl.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.key === ' ') {
              var card = e.target.closest('.dgdb-review-card');
              if (!card) return;
              e.preventDefault();
              try { openModal(JSON.parse(card.getAttribute('data-review') || '{}')); } catch (err) {}
            }
          });

          var meta = data.meta || { page: 1, pages: 1, total: 0 };
          pages = meta.pages || 1;
          page = (meta.page || 1) + 1;
          if (moreBtn) moreBtn.style.display = (page <= pages) ? 'inline-flex' : 'none';
          if (emptyEl) emptyEl.style.display = (meta.total || 0) === 0 ? 'block' : 'none';
        })
        .catch(function (err) {
          if (window.console) { console.error('dg_list_reviews response', (err && err.text) ? err.text : err); }
          if (emptyEl && listEl.children.length === 0) emptyEl.style.display = 'block';
        })
        .finally(function () { loading = false; });
    }

    // -------- check existing review --------
    function checkExistingReview() {
      if (!form || !nonce || !discId) return;

      var url = new URL(ajax, window.location.origin);
      url.searchParams.set('action', 'dg_get_my_review');
      url.searchParams.set('nonce', nonce);
      url.searchParams.set('disc_id', String(discId));

      fetch(url.toString(), { credentials: 'same-origin', cache: 'no-store' })
        .then(function (res) { return res.json(); })
        .then(function (json) {
          if (json && json.success && json.data && json.data.has) {
            populateFormWithReview(json.data);
            updateButtonText('update');
          } else {
            updateButtonText('submit');
          }
        })
        .catch(function (err) {
          if (window.console) console.error('dg_get_my_review error', err);
          updateButtonText('submit');
        });
    }

    function populateFormWithReview(reviewData) {
      if (!form) return;

      // Populate basic fields
      if (rating && reviewData.rating) rating.value = reviewData.rating;
      if (titleEl && reviewData.title) titleEl.value = reviewData.title;
      if (textEl && reviewData.review) textEl.value = reviewData.review;
      if (plasticEl && reviewData.plastic_type) plasticEl.value = reviewData.plastic_type;
      if (weightEl && reviewData.weight) weightEl.value = reviewData.weight;

      // Populate flight numbers
      if (spNum && reviewData.speed !== null) spNum.value = reviewData.speed;
      if (glNum && reviewData.glide !== null) glNum.value = reviewData.glide;
      if (tnNum && reviewData.turn !== null) tnNum.value = reviewData.turn;
      if (fdNum && reviewData.fade !== null) fdNum.value = reviewData.fade;

      // Update range sliders to match
      if (spRng && reviewData.speed !== null) spRng.value = reviewData.speed;
      if (glRng && reviewData.glide !== null) glRng.value = reviewData.glide;
      if (tnRng && reviewData.turn !== null) tnRng.value = reviewData.turn;
      if (fdRng && reviewData.fade !== null) fdRng.value = reviewData.fade;

      // Update preview
      updatePreview();
    }

    function updateButtonText(mode) {
      if (!submitBtn) return;

      if (mode === 'update') {
        submitBtn.textContent = 'Uppdatera recension';
        submitBtn.setAttribute('title', 'Uppdatera din befintliga recension');
      } else {
        submitBtn.textContent = 'Spara recension';
        submitBtn.setAttribute('title', 'Spara ny recension');
      }
    }

    // -------- submit --------
    function submitForm(ev) {
      ev.preventDefault();
      if (!form || submitting) return;

      // If a range has a value and the paired number is empty (or NaN), copy it over
      if (spRng && spNum && (spNum.value === '' || isNaN(spNum.value))) spNum.value = spRng.value;
      if (glRng && glNum && (glNum.value === '' || isNaN(glNum.value))) glNum.value = glRng.value;
      if (tnRng && tnNum && (tnNum.value === '' || isNaN(tnNum.value))) tnNum.value = tnRng.value;
      if (fdRng && fdNum && (fdNum.value === '' || isNaN(fdNum.value))) fdNum.value = fdRng.value;

      // clamp FNs
      if (spNum) spNum.value = clamp(spNum.value, 0, 15, 0.5);
      if (glNum) glNum.value = clamp(glNum.value, 0, 7, 0.5);
      if (tnNum) tnNum.value = clamp(tnNum.value, -5, 2, 0.5);
      if (fdNum) fdNum.value = clamp(fdNum.value, 0, 5, 0.5);
      updatePreview();

      if (weightEl && weightEl.value !== '') {
        var w = parseInt(weightEl.value, 10);
        if (!isNaN(w)) weightEl.value = String(Math.max(100, Math.min(300, w)));
      }

      submitting = true;
      if (submitBtn) submitBtn.disabled = true;
      form.classList.add('dgdb-is-submitting');

      var fd = new FormData(form);
      fd.set('action', 'dg_add_review');
      if (nonce) fd.set('nonce', nonce);
      fd.set('disc_id', String(discId));

      fetch(ajax, { method: 'POST', body: fd, credentials: 'same-origin' })
        .then(function (r) { return r.text(); })
        .then(function (t) { try { return JSON.parse(t); } catch (e) { throw { parse: true, text: t }; } })
        .then(function (json) {
          if (!json || !json.success) throw new Error((json && json.data) || 'Error');
          toast('Saved!', true);

          // optimistic prepend
          var it = {
            user: 'You',
            rating: parseInt(rating && rating.value || 5, 10) || 5,
            title:  (titleEl   && titleEl.value)   || '',
            review: (textEl    && textEl.value)    || '',
            plastic_type: (plasticEl && plasticEl.value) || '',
            weight: (weightEl  && weightEl.value)  || '',
            speed: spNum && spNum.value || null,
            glide: glNum && glNum.value || null,
            turn:  tnNum && tnNum.value || null,
            fade:  fdNum && fdNum.value || null,
            created_at: 'just now'
          };
          var node = renderCard(it);
          if (node && listEl) {
            listEl.insertBefore(node, listEl.firstChild);
            if (emptyEl) emptyEl.style.display = 'none';
          }
          if (textEl) textEl.value = '';

          // --- Gamification HUD trigger (if server returned data) ---
          var g = json.data && json.data.gamify ? json.data.gamify : null;
          if (g) {
            if (g.leveled_up) {
              window.dispatchEvent(new CustomEvent('dgdb:levelUp', { detail: { level: g.level, label: g.rank } }));
            } else {
              // even without level-up, tell HUD to refresh progress quietly
              window.dispatchEvent(new CustomEvent('dgdb:levelUp', { detail: { level: g.level, label: g.rank } }));
            }
          }
          // ----------------------------------------------------------

          // Update button text to "Update Review" since user now has a review
          updateButtonText('update');

          // refresh server data shortly after
          setTimeout(function () { listPage(true); }, 100);
          document.dispatchEvent(new CustomEvent('dgdb:reviewSaved', { detail: { discId: discId } }));
        })
        .catch(function (err) {
          var errorMsg = 'Something went wrong.';

          // Handle rate limiting errors specifically
          if (err && err.data && err.data.type === 'rate_limit') {
            errorMsg = err.data.message || 'För många recensioner. Vänta innan du försöker igen.';
          } else if (err && err.text) {
            errorMsg = err.text;
          } else if (err && err.data && err.data.message) {
            errorMsg = err.data.message;
          }

          toast(errorMsg, false);
          if (window.console) console.error('dg_add_review response', err);
        })
        .finally(function () {
          submitting = false;
          if (submitBtn) submitBtn.disabled = false;
          form.classList.remove('dgdb-is-submitting');
        });
    }

    if (form) {
      form.setAttribute('action', '');
      form.addEventListener('submit', submitForm);
    }
    if (moreBtn) moreBtn.addEventListener('click', function () { listPage(false); });

    // Check if user has existing review and populate form
    checkExistingReview();

    listPage(true);
  });
})();
