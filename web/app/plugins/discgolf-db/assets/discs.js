// assets/discs.js — minimal: cards -> /disc/{id}
(function () {
  const grid = document.querySelector('.dgdb-grid');
  if (!grid) return;

  function toDisc(disc) {
    if (!disc || !disc.id) return;
    const base = (window.DGDB && DGDB.discBase) ? DGDB.discBase : '/disc/';
    window.location.href = base + disc.id;
  }

  function parseCard(el) {
    const raw = el.getAttribute('data-disc') || '{}';
    try { return JSON.parse(raw); }
    catch (_) {
      // handle HTML-escaped JSON
      try { return JSON.parse(raw.replace(/&quot;/g,'"').replace(/&#039;/g,"'")); }
      catch (__) { return null; }
    }
  }

  grid.addEventListener('click', (e) => {
    const card = e.target.closest('.dgdb-card');
    if (!card) return;
    if (e.target.closest('a,button,input,select,textarea,label')) return;
    const disc = parseCard(card);
    toDisc(disc);
  });

  grid.addEventListener('keydown', (e) => {
    const card = e.target.closest('.dgdb-card');
    if (!card) return;
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      toDisc(parseCard(card));
    }
  });
})();
