/* container the JS moves into header or fixes to top-right */
.dgdb-hud-mount {
  position: relative;
  z-index: 5000;
}

/* fallback (no recognizable header found) */
.dgdb-hud-mount.dgdb-hud--fixed {
  position: fixed;
  top: 10px;
  right: 12px;
}

/* HUD pill */
.dgdb-hud {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,.85);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border: 1px solid rgba(0,0,0,.06);
  box-shadow: 0 6px 20px rgba(0,0,0,.08);
  border-radius: 999px;
  padding: 8px 12px;
  max-width: 420px;
  font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
}

:root.dark .dgdb-hud,
body.dark .dgdb-hud,
[data-theme="dark"] .dgdb-hud {
  background: rgba(20,20,26,.75);
  border-color: rgba(255,255,255,.08);
  color: #eaeaea;
}

/* avatar */
.dgdb-hud__avatar {
  flex: 0 0 auto;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,.15);
}
.dgdb-hud__avatar img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* text chunk */
.dgdb-hud__meta {
  min-width: 200px;
  flex: 1 1 auto;
}
.dgdb-hud__line {
  display: flex;
  align-items: baseline;
  gap: 8px;
  line-height: 1.1;
  margin-bottom: 6px;
  white-space: nowrap;
}
.dgdb-hud__name {
  font-weight: 600;
  font-size: 13px;
}
.dgdb-hud__rank {
  font-size: 11px;
  opacity: .7;
  padding: 2px 8px;
  border-radius: 999px;
  background: rgba(0,0,0,.06);
}
:root.dark .dgdb-hud__rank,
body.dark .dgdb-hud__rank,
[data-theme="dark"] .dgdb-hud__rank {
  background: rgba(255,255,255,.08);
}
.dgdb-hud__level {
  margin-left: auto;
  font-size: 11px;
  opacity: .75;
}

/* progress bar */
.dgdb-hud__bar {
  position: relative;
  height: 8px;
  border-radius: 999px;
  background: rgba(0,0,0,.07);
  overflow: hidden;
}
:root.dark .dgdb-hud__bar,
body.dark .dgdb-hud__bar,
[data-theme="dark"] .dgdb-hud__bar {
  background: rgba(255,255,255,.1);
}
.dgdb-hud__bar-fill {
  position: absolute;
  height: 100%;
  width: 0%;
  left: 0;
  top: 0;
  border-radius: inherit;
  background: linear-gradient(90deg, #7dd3fc, #60a5fa, #a78bfa);
  transition: width .45s ease-in-out, transform .2s ease-out;
}

/* toast */
.dgdb-hud__toast {
  position: absolute;
  right: 10px;
  bottom: -10px;
  transform: translateY(100%);
  background: #111827;
  color: #fff;
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,.18);
}
:root.dark .dgdb-hud__toast,
body.dark .dgdb-hud__toast,
[data-theme="dark"] .dgdb-hud__toast {
  background: #f3f4f6;
  color: #111827;
}

/* header alignment helper: place it on the right of header row */
.dgdb-hud--header-slot {
  position: relative;
  display: flex;
  justify-content: flex-end;
  padding: 6px 8px;
}

/* Level-up burst (simple particles) */
.dgdb-hud__burst {
  position: fixed;
  pointer-events: none;
  inset: 0;
  overflow: visible;
  z-index: 9999;
}
.dgdb-burst__p {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  opacity: 0;
  transform: translate(-50%, -50%) scale(.8);
  animation: dgdb-burst .9s cubic-bezier(.2,.7,.15,1) forwards;
}
@keyframes dgdb-burst {
  0% { opacity: 0; transform: translate(-50%,-50%) scale(.4); }
  15%{ opacity: 1; }
  100%{ opacity: 0; transform: translate(var(--dx), var(--dy)) scale(1); }
}

/* small screens */
@media (max-width: 640px) {
  .dgdb-hud { max-width: 86vw; }
  .dgdb-hud__name { display: none; }
}

/* right-align inside an existing flex row */
.dgdb-hud--header-slot {
  position: relative;
  display: flex;
  align-items: center;
}

/* absolute pin inside header when no flex row is found */
.dgdb-hud--header-abs {
  position: absolute;
  right: 12px;
  top: 8px;
}


.dgdb-xp-ladder {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
}
#referral-table td {
    padding: 4px 2px;
}