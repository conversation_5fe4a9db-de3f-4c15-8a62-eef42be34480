(function(){
  function $(sel, ctx){ return (ctx||document).querySelector(sel); }
  function $all(sel, ctx){ return Array.prototype.slice.call((ctx||document).querySelectorAll(sel)); }
  function css(el, prop){ return window.getComputedStyle(el || document.body).getPropertyValue(prop); }

  /* ---------- header mount helpers ---------- */
  function findFlexRow(root){
    var selectors = [
      '.is-layout-flex',
      '.wp-block-group.is-layout-flex',
      '.wp-block-navigation__container',
      '.wp-block-group__inner-container.is-layout-flex',
      '.wp-block-group > .is-layout-flex',
      'nav .is-layout-flex',
      'nav > ul',
      'nav'
    ];
    for (var i=0;i<selectors.length;i++){
      var cand = root.querySelector(selectors[i]);
      if (!cand) continue;
      var disp = css(cand,'display');
      if (disp === 'flex' || disp === 'inline-flex') return cand;
    }
    var q=[root],step=0;
    while(q.length && step++<500){
      var el=q.shift();
      try{
        var d=css(el,'display'), h=el.getBoundingClientRect().height;
        if((d==='flex'||d==='inline-flex') && h>28 && h<120) return el;
      }catch(e){}
      for(var j=0;j<el.children.length;j++) q.push(el.children[j]);
    }
    return null;
  }

  function mountIntoHeader(mount, candidates){
    var extra = ['.dg-header', '.wp-block-template-part__header', '.wp-site-blocks > header'];
    var all = (candidates || []).concat(extra);
    for (var i=0;i<all.length;i++){
      var header = document.querySelector(all[i]);
      if(!header) continue;
      var row = findFlexRow(header);
      if (row){
        var slot=document.createElement('div');
        slot.className='dgdb-hud--header-slot';
        slot.style.marginLeft='auto';
        row.appendChild(slot);
        slot.appendChild(mount);
        mount.classList.remove('dgdb-hud--fixed','dgdb-hud--header-abs');
        return true;
      }
      if (css(header,'position')==='static') header.style.position='relative';
      mount.classList.add('dgdb-hud--header-abs');
      header.appendChild(mount);
      mount.classList.remove('dgdb-hud--fixed');
      return true;
    }
    mount.classList.add('dgdb-hud--fixed');
    document.body.appendChild(mount);
    return false;
  }

  /* ---------- HUD progress ---------- */
  function setProgress(hud, curr, next){
    var pct = 0;
    if (typeof curr === 'number' && typeof next === 'number' && next > 0) {
      pct = Math.max(0, Math.min(100, Math.round((curr / next) * 100)));
    }
    var bar  = hud.querySelector('.dgdb-hud__bar');
    var fill = hud.querySelector('.dgdb-hud__bar-fill');
    if (bar && fill){
      bar.setAttribute('aria-valuenow', String(pct));
      fill.style.width = pct + '%';
    }
    var xpText = hud.querySelector('.dgdb-hud__xp');
    if (xpText) { /* optional label */ }
  }

  function toast(hud, text){
    var t = hud.querySelector('.dgdb-hud__toast');
    if(!t) return;
    t.textContent = text || '';
    t.hidden = !text;
    if(text){
      clearTimeout(t._timer);
      t._timer = setTimeout(function(){ t.hidden = true; }, 2200);
    }
  }

  function burst(x,y){
    var wrap = document.querySelector('.dgdb-hud__burst'); if(!wrap) return;
    var colors=['#7dd3fc','#60a5fa','#a78bfa','#34d399','#f472b6','#f59e0b'];
    for (var i=0;i<24;i++){
      var p = document.createElement('div');
      p.className='dgdb-burst__p';
      var ang=(i/24)*Math.PI*2;
      var dist=80+Math.random()*40;
      var dx=Math.cos(ang)*dist;
      var dy=Math.sin(ang)*dist;
      p.style.left=x+'px';
      p.style.top =y+'px';
      p.style.setProperty('--dx', dx+'px');
      p.style.setProperty('--dy', dy+'px');
      p.style.background=colors[i%colors.length];
      wrap.appendChild(p);
      (function(node){ setTimeout(function(){ node.remove(); }, 1100); })(p);
    }
  }

  function applyHUD(hud, user){
    if (!user) return;
    var img = hud.querySelector('.dgdb-hud__avatar img');
    if (img && user.avatar) img.src = user.avatar;

    var name = hud.querySelector('.dgdb-hud__name');
    if (name) name.textContent = user.name || 'You';

    var rank = hud.querySelector('.dgdb-hud__rank');
    if (rank) rank.textContent = user.rank || 'Rookie';

    var level = hud.querySelector('.dgdb-hud__level');
    if (level) level.textContent = 'Lv.' + (user.level || 1);

    setProgress(hud, Number(user.curr_xp || 0), Number(user.next_xp || 0));
    var xpText = hud.querySelector('.dgdb-hud__xp');
    if (xpText) xpText.textContent = (user.total_xp || 0) + ' XP';

    hud.hidden = false;
  }

  function fetchHUD(hud){
    var ajax = (window.DGDB && DGDB.ajax) || '/wp-admin/admin-ajax.php';
    var nonce = (window.DGDB && DGDB.nonce) || '';
    var form = new URLSearchParams();
    form.set('action', 'dgdb_gamify_hud');
    if (nonce) form.set('nonce', nonce);
    return fetch(ajax, {method:'POST', body:form, credentials:'same-origin'})
      .then(function(r){ return r.json(); })
      .then(function(json){
        if (json && json.success && json.data && json.data.user) {
          applyHUD(hud, json.data.user);
        }
      })
      .catch(function(e){ if (window.console) console.warn('HUD fetch failed', e); });
  }

  /* ---------- modal (single-column, paginated) ---------- */

  function injectScopedStyles(){
    if ($('#dgdb-xp-inline-style')) return;
    var s=document.createElement('style');
    s.id='dgdb-xp-inline-style';
    s.textContent = `
      /* layout */
      .dgdb-modal-card{ width:min(1040px,92vw); max-height:90vh; overflow:auto; }
      .dgdb-xp-stack{ display:grid; gap:16px; padding:18px 24px 24px; }
      .dgdb-xp-toprow{
        display:grid;
        grid-template-columns:repeat(auto-fit,minmax(240px,1fr));
        gap:16px;
      }
      .dgdb-xp-card{
        border:1px solid var(--dg-border);
        border-radius:var(--dg-radius-lg);
        padding:10px;
        box-shadow:var(--dg-shadow-sm);
        background:var(--dg-surface);
      }

      /* profile card */
      .dgdb-xp-profile{ display:grid; grid-template-columns:64px 1fr; gap:12px; align-items:center; }
      .dgdb-xp-profile .dgdb-ava{ width:64px; height:64px; border-radius:50%; overflow:hidden; border:2px solid var(--dg-bg); }
      .dgdb-xp-profile .dgdb-ava img{ width:100%;height:100%;object-fit:cover;display:block; }
      .dgdb-xp-rank{ margin-top:6px; font-size:13px; color:var(--dg-muted); }
      .dgdb-xp-progress{ margin-top:8px; }
      .dgdb-xp-progress__bar{height:10px;border-radius:999px;background:var(--dg-bg);border:1px solid var(--dg-border);overflow:hidden}
      .dgdb-xp-progress__fill{height:100%;width:0;transition:width .35s ease;background:linear-gradient(90deg, #7dd3fc, #60a5fa, #a78bfa);)}
      .dgdb-xp-meta{color:var(--dg-muted);font-size:12px;margin-top:6px}

      /* contributions */
      .dgdb-chip-row{ display:flex; flex-wrap:wrap; gap:8px; }
      .dgdb-chip{ display:inline-flex; align-items:center; gap:6px; padding:6px 10px; border:1px solid var(--dg-border); border-radius:999px; background:var(--dg-surface); color:var(--dg-ink); font-size:12px; }

      /* ladder graph */
      .dgdb-ladder-graph{ margin-top:8px }
      .dgdb-ladder-graph svg{ width:100%; height:180px; display:block }
      .dgdb-lg-axis{ stroke: var(--dg-border); stroke-width:1 }
      .dgdb-lg-grid{ stroke: color-mix(in oklab, var(--dg-border) 70%, transparent); stroke-width:.5 }
      .dgdb-lg-path{ fill:none; stroke: var(--dg-accent); stroke-width:2.5 }
      .dgdb-lg-node{ stroke: var(--dg-accent); stroke-width:2; fill: var(--dg-bg) }
      .dgdb-lg-node--active{ fill: var(--dg-accent); }
      .dgdb-lg-label{ font-size:10px; fill: var(--dg-ink) }
      .dgdb-lg-xp{ font-size:9px; fill: var(--dg-muted) }

      /* invite */
      .dgdb-invite{ display:grid; gap:8px; }
      .dgdb-invite-row{ display:flex; gap:8px; }
      .dgdb-invite-input{ flex:1; height:36px; padding:0 10px; border-radius:12px; border:1px solid var(--dg-border); background:var(--dg-bg); color:var(--dg-ink); }
      .dgdb-invite small{ color:var(--dg-muted); }

      /* recent */
      .dgdb-recent-card .dgdb-card-title{ display:flex; align-items:center; gap:8px; }
      .dgdb-recent-total{ margin-left:auto; }
      .dgdb-bag-contents-table{ margin-top:8px; }
      .dgdb-pager-line{ display:flex; justify-content:flex-end; gap:8px; margin-top:10px; }
    `;
    document.head.appendChild(s);
  }

  function buildModal(){
    injectScopedStyles();

    var wrap = document.createElement('div');
    wrap.className = 'dgdb-modal';
    wrap.setAttribute('role', 'dialog');
    wrap.setAttribute('aria-modal', 'true');
    wrap.setAttribute('aria-labelledby', 'dgdbXPTtl');

    wrap.innerHTML = `
      <div class="dgdb-modal-backdrop" data-close></div>
      <div class="dgdb-modal-card">
        <button class="dgdb-modal-close dgdb-btn" data-close>Close</button>

        <div class="dgdb-modal-head">
          <h3 id="dgdbXPTtl">Experience</h3>
        </div>

        <div class="dgdb-xp-stack">

          <!-- Row 1: cards -->
          <div class="dgdb-xp-toprow">
            <!-- Profile / Progress -->
            <div class="dgdb-xp-card">
              <div class="dgdb-xp-profile">
                <div class="dgdb-ava"><img alt="Avatar"></div>
                <div>
                  <div class="dgdb-profile__name" style="font-weight:700"></div>
                  <div class="dgdb-profile__joined muted"></div>
                  <div class="dgdb-xp-rank"><strong>Level</strong>: <span class="dgdb-profile__rank">Rookie</span></div>
                </div>
              </div>
              <div class="dgdb-xp-progress">
                <div class="dgdb-xp-progress__bar"><div class="dgdb-xp-progress__fill"></div></div>
                <div class="dgdb-xp-meta dgdb-progress__numbers"></div>
              </div>
            </div>

            <!-- Contributions -->
            <div class="dgdb-xp-card">
              <div class="dgdb-card-title" style="font-weight:600">Contributions</div>
              <div class="dgdb-chip-row" style="margin-top:8px">
                <span class="dgdb-chip dgdb-contrib__reviews">Reviews: 0</span>
                <span class="dgdb-chip dgdb-contrib__plastics-sub">Plastics submitted: 0</span>
                <span class="dgdb-chip dgdb-contrib__plastics-ok">Approved: 0</span>
                <span class="dgdb-chip dgdb-contrib__streak">Weekly streak: 0 / 0</span>
              </div>
            </div>

            <!-- Invite -->
            <div class="dgdb-xp-card">
              <div class="dgdb-card-title" style="font-weight:600">Invite friends</div>
              <div class="dgdb-invite" style="margin-top:8px">
                <div class="dgdb-invite-row">
                  <input class="dgdb-invite-input" type="text" readonly value="">
                  <button class="dgdb-btn" type="button" data-copy>Copy</button>
                </div>
                <small>Share your link. Each signup via your link gives you <strong>+250 XP</strong> (not capped).</small>
              </div>
            </div>
          </div>

          <!-- Ladder Graph 
          <div class="dgdb-xp-card" id="dgdb-ladder">
            <div class="dgdb-card-title" style="font-weight:600">Level Ladder</div>
            <div class="dgdb-ladder-graph" aria-label="Level ladder chart"></div>
          </div> -->

          <!-- Recent (paginated) -->
          <div class="dgdb-xp-card dgdb-recent-card">
            <div class="dgdb-card-title">
              <span>Recent XP</span>
              <span class="dgdb-chip dgdb-recent-total dgdb-recent__total">0 XP total</span>
            </div>

            <div class="dgdb-bag-contents-table" id="referral-table">
              <table class="dgdb-bag-display-table">
                <tbody class="dgdb-xp-rows"></tbody>
              </table>
              <div class="dgdb-subtle dgdb-xp-empty" hidden>No recent entries.</div>
            </div>

            <div class="dgdb-pager-line">
              <button class="dgdb-btn" type="button" data-prev>&larr; Prev</button>
              <span class="muted" data-page-label>Page 1</span>
              <button class="dgdb-btn" type="button" data-next>Next &rarr;</button>
            </div>
          </div>

        </div>
      </div>
    `;

    document.body.appendChild(wrap);
    return wrap;
  }

  /* ---------- Ladder chart (SVG) ---------- */
  function renderLadderChart(modal, thresholds, currentLevel, totalXP){
    var host = modal.querySelector('.dgdb-ladder-graph');
    if (!host) return;
    host.innerHTML = '';

    if (!thresholds || !thresholds.length){
      host.textContent = 'No ladder data';
      return;
    }

    // sort by level just in case
    thresholds = thresholds.slice().sort(function(a,b){ return (a.level||0)-(b.level||0); });

    var maxXP = thresholds[thresholds.length-1].min_xp || 1;
    var w = host.clientWidth || 600;
    var h = 180;
    var pad = {t:18,r:14,b:28,l:36};

    var innerW = Math.max(1, w - pad.l - pad.r);
    var innerH = Math.max(1, h - pad.t - pad.b);

    function xScale(i){ return pad.l + (thresholds.length===1 ? innerW/2 : (i/(thresholds.length-1))*innerW); }
    function yScale(xp){ var v = xp/maxXP; return pad.t + (1 - v) * innerH; }

    // Build path through points
    var d = '';
    thresholds.forEach(function(t, i){
      var x = xScale(i);
      var y = yScale(t.min_xp||0);
      d += (i===0 ? 'M' : 'L') + x + ' ' + y + ' ';
    });

    // create SVG
    var svg = document.createElementNS('http://www.w3.org/2000/svg','svg');
    svg.setAttribute('viewBox', `0 0 ${w} ${h}`);
    svg.setAttribute('role','img');
    svg.setAttribute('focusable','false');

    // axes + grid
    var axisX = document.createElementNS(svg.namespaceURI,'line');
    axisX.setAttribute('x1', pad.l);
    axisX.setAttribute('y1', yScale(0));
    axisX.setAttribute('x2', w - pad.r);
    axisX.setAttribute('y2', yScale(0));
    axisX.setAttribute('class','dgdb-lg-axis');
    svg.appendChild(axisX);

    // 3 horizontal grid lines (25/50/75%)
    [0.25,0.5,0.75].forEach(function(fr){
      var y = yScale(maxXP*fr);
      var g = document.createElementNS(svg.namespaceURI,'line');
      g.setAttribute('x1', pad.l);
      g.setAttribute('y1', y);
      g.setAttribute('x2', w - pad.r);
      g.setAttribute('y2', y);
      g.setAttribute('class','dgdb-lg-grid');
      svg.appendChild(g);
    });

    // path
    var path = document.createElementNS(svg.namespaceURI,'path');
    path.setAttribute('d', d.trim());
    path.setAttribute('class','dgdb-lg-path');
    svg.appendChild(path);

    // nodes + labels
    thresholds.forEach(function(t, i){
      var x = xScale(i);
      var y = yScale(t.min_xp||0);

      var circle = document.createElementNS(svg.namespaceURI,'circle');
      circle.setAttribute('cx', x);
      circle.setAttribute('cy', y);
      circle.setAttribute('r', 6);
      circle.setAttribute('class','dgdb-lg-node' + ((currentLevel||1)===t.level ? ' dgdb-lg-node--active' : ''));
      svg.appendChild(circle);

      var lbl = document.createElementNS(svg.namespaceURI,'text');
      lbl.setAttribute('x', x);
      lbl.setAttribute('y', y - 10);
      lbl.setAttribute('text-anchor','middle');
      lbl.setAttribute('class','dgdb-lg-label');
      lbl.textContent = 'Lv.' + (t.level||'?');
      svg.appendChild(lbl);

      var xp = document.createElementNS(svg.namespaceURI,'text');
      xp.setAttribute('x', x);
      xp.setAttribute('y', y + 18);
      xp.setAttribute('text-anchor','middle');
      xp.setAttribute('class','dgdb-lg-xp');
      xp.textContent = (t.min_xp||0) + ' XP';
      svg.appendChild(xp);
    });

    // marker for current total XP (between nodes)
    if (typeof totalXP === 'number'){
      var yCurr = yScale(Math.min(totalXP, maxXP));
      var currLine = document.createElementNS(svg.namespaceURI,'line');
      currLine.setAttribute('x1', pad.l);
      currLine.setAttribute('x2', w - pad.r);
      currLine.setAttribute('y1', yCurr);
      currLine.setAttribute('y2', yCurr);
      currLine.setAttribute('class','dgdb-lg-grid');
      svg.appendChild(currLine);
    }

    host.appendChild(svg);
  }

  function openModal(){
    var modal = buildModal();
    var closeBtns = $all('[data-close]', modal);

    // Focus trap
    var focusables = function(){
      return $all('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])', modal)
        .filter(function(n){ return !n.hasAttribute('disabled') && n.offsetParent !== null; });
    };
    function trap(e){
      if (e.key !== 'Tab') return;
      var items = focusables();
      if (!items.length) return;
      var first = items[0], last = items[items.length-1];
      if (e.shiftKey && document.activeElement === first) { e.preventDefault(); last.focus(); }
      else if(!e.shiftKey && document.activeElement === last) { e.preventDefault(); first.focus(); }
    }
    function esc(e){ if (e.key === 'Escape') close(); }
    function onBack(e){ if (e.target.matches('.dgdb-modal-backdrop,[data-close]')) close(); }

    function close(){
      document.removeEventListener('keydown', trap);
      document.removeEventListener('keydown', esc);
      modal.removeEventListener('click', onBack);
      modal.remove();
    }

    document.addEventListener('keydown', trap);
    document.addEventListener('keydown', esc);
    modal.addEventListener('click', onBack);
    if (closeBtns[0]) closeBtns[0].focus();

    // Load details
    fetchDetails(modal);
  }

  function fetchDetails(modal){
    var ajax  = (window.DGDB && DGDB.ajax) || '/wp-admin/admin-ajax.php';
    var nonce = (window.DGDB && DGDB.nonce) || '';
    var form = new URLSearchParams();
    form.set('action', 'dgdb_gamify_details');
    if (nonce) form.set('nonce', nonce);

    fetch(ajax, {method:'POST', body:form, credentials:'same-origin'})
      .then(function(r){ return r.json(); })
      .then(function(json){
        if (!json || !json.success || !json.data) return;
        renderDetails(modal, json.data);
      })
      .catch(function(e){ if (window.console) console.warn('details fetch failed', e); });
  }

  function renderDetails(modal, data){
    var p   = data.profile || {};
    var xp  = data.xp      || {};
    var rec = data.recent  || [];
    var cnt = data.counts  || {};
    var invite = data.invite || {};

    // profile
    var img = modal.querySelector('.dgdb-ava img');
    if (img && p.avatar) img.src = p.avatar;
    var nm  = modal.querySelector('.dgdb-profile__name');
    if (nm) nm.textContent = p.name || 'You';
    var jd  = modal.querySelector('.dgdb-profile__joined');
    if (jd) jd.textContent = 'Joined ' + (p.joined || '');
    var rk  = modal.querySelector('.dgdb-profile__rank');
    if (rk) rk.textContent = (xp.rank && xp.rank.label ? xp.rank.label : 'Rookie') + ' (Lv.' + (xp.level || 1) + ')';

    // progress bar within current level
    var curr = Math.max(0, (xp.total_xp||0) - (xp.floor_xp||0));
    var need = Math.max(1, (xp.next_xp||0)  - (xp.floor_xp||0));
    var bar  = modal.querySelector('.dgdb-xp-progress__fill');
    if (bar){
      var pct = Math.max(0, Math.min(100, Math.round(curr/need*100)));
      bar.style.width = pct + '%';
    }
    var nums = modal.querySelector('.dgdb-progress__numbers');
    if (nums) nums.textContent = curr + ' / ' + need + ' XP to next level';

    // contributions
    var rv = modal.querySelector('.dgdb-contrib__reviews');
    if (rv) rv.textContent = 'Reviews: ' + (cnt.reviews || 0);
    var ps = modal.querySelector('.dgdb-contrib__plastics-sub');
    if (ps) ps.textContent = 'Plastics submitted: ' + (cnt.plastics_submitted || 0);
    var pa = modal.querySelector('.dgdb-contrib__plastics-ok');
    if (pa) pa.textContent = 'Approved: ' + (cnt.plastics_approved || 0);
    var st = modal.querySelector('.dgdb-contrib__streak');
    if (st) st.textContent = 'Weekly streak: ' + ((xp.streak && xp.streak.week_count) || 0) + ' / ' + ((xp.streak && xp.streak.week_goal) || 0);

    // ladder graph (replaces list)
    renderLadderChart(modal, xp.thresholds || [], xp.level || 1, xp.total_xp || 0);

    // invite
    var field = modal.querySelector('.dgdb-invite-input');
    if (field) field.value = invite.link || invite.url || (window.location.origin + '/?ref=' + (invite.code || ''));

    var copyBtn = modal.querySelector('[data-copy]');
    if (copyBtn){
      copyBtn.addEventListener('click', function(){
        if (!field) return;
        field.select(); field.setSelectionRange(0, 99999);
        try { document.execCommand('copy'); } catch(e) {}
        copyBtn.textContent = 'Copied!';
        setTimeout(function(){ copyBtn.textContent = 'Copy'; }, 1200);
      });
    }

    // recent (client-side pagination)
    modal._recent = rec.slice();
    modal._page   = 1;
    modal._per    = 8;

    var totalLabel = modal.querySelector('.dgdb-recent__total');
    if (totalLabel) totalLabel.textContent = (xp.total_xp || 0) + ' XP total';

    function renderPage(){
      var tbody = modal.querySelector('.dgdb-xp-rows');
      var empty = modal.querySelector('.dgdb-xp-empty');
      var pageLabel = modal.querySelector('[data-page-label]');
      var start = (modal._page - 1) * modal._per;
      var items = modal._recent.slice(start, start + modal._per);

      if (!items.length){
        if (tbody) tbody.innerHTML = '';
        if (empty) empty.hidden = false;
      } else {
        if (empty) empty.hidden = true;
        tbody.innerHTML = items.map(function(r){
          var src = (r.source || '').replace(/^./, function(c){ return c.toUpperCase(); });
          var act = r.action || '';
          var delta = (r.delta > 0 ? '+' : '') + r.delta;
          return `<tr>
            <td>${r.created_at || ''}</td>
            <td>${act}</td>
            <td>${src}</td>
            <td>${delta}</td>
          </tr>`;
        }).join('');
      }

      var pages = Math.max(1, Math.ceil(modal._recent.length / modal._per));
      if (pageLabel) pageLabel.textContent = 'Page ' + modal._page + ' / ' + pages;
      var prev = modal.querySelector('[data-prev]'), next = modal.querySelector('[data-next]');
      if (prev) prev.disabled = (modal._page <= 1);
      if (next) next.disabled = (modal._page >= pages);
    }

    var prevBtn = modal.querySelector('[data-prev]');
    var nextBtn = modal.querySelector('[data-next]');
    if (prevBtn) prevBtn.addEventListener('click', function(){ if (modal._page > 1){ modal._page--; renderPage(); } });
    if (nextBtn) nextBtn.addEventListener('click', function(){
      var pages = Math.max(1, Math.ceil((modal._recent||[]).length / modal._per));
      if (modal._page < pages){ modal._page++; renderPage(); }
    });

    renderPage();
  }

  /* ---------- boot ---------- */
  function init(){
    var mount = document.getElementById('dgdb-hud-mount');
    var hud   = mount ? mount.querySelector('.dgdb-hud') : null;
    if (!mount || !hud) return;

    // Slot into header
    mountIntoHeader(mount, (window.DGDB_HUD && window.DGDB_HUD.mountSelectorCandidates) || []);

    // Pre-fill (if server provided) then fetch fresh
    if (window.DGDB_HUD && window.DGDB_HUD.loggedIn && window.DGDB_HUD.user) {
      applyHUD(hud, window.DGDB_HUD.user);
    } else {
      hud.hidden = true;
    }
    fetchHUD(hud);

    // Refresh when XP may change
    document.addEventListener('dgdb:xpChanged', function(){ fetchHUD(hud); });
    document.addEventListener('dgdb:reviewSaved', function(){ document.dispatchEvent(new Event('dgdb:xpChanged')); });

    // Level-up micro-anim
    window.addEventListener('dgdb:levelUp', function(e){
      var d = (e && e.detail) || {};
      var lvlEl = hud.querySelector('.dgdb-hud__level');
      var rankEl= hud.querySelector('.dgdb-hud__rank');
      if (typeof d.level !== 'undefined' && lvlEl) lvlEl.textContent = 'Lv.' + d.level;
      if (d.label && rankEl) rankEl.textContent = d.label;

      toast(hud, 'Level up!');
      var rect = hud.getBoundingClientRect();
      burst(rect.left + rect.width - 20, rect.top + 10);

      var fill = hud.querySelector('.dgdb-hud__bar-fill');
      if (fill) { fill.style.transform='scaleY(1.2)'; setTimeout(function(){ fill.style.transform=''; }, 400); }
    });

    // Open modal on click/Enter
    hud.addEventListener('click', function(){ openModal(); });
    hud.addEventListener('keydown', function(e){
      if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); openModal(); }
    });

    // a11y button affordances
    if (!hud.hasAttribute('tabindex')) hud.setAttribute('tabindex','0');
    if (!hud.getAttribute('role')) hud.setAttribute('role','button');
    if (!hud.getAttribute('aria-label')) hud.setAttribute('aria-label','Open XP profile');
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init, { once: true });
  } else {
    init();
  }
})();
