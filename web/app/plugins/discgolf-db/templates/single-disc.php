<?php
/** @var array $ctx */
require_once __DIR__ . '/_helpers.php';

$disc = $ctx['disc'];
$img  = $ctx['img'];

use DiscGolfDB\Collection;

$title = trim(($disc['manufacturer'] ? $disc['manufacturer'].' ' : '') . $disc['name']);
$subs  = [];
if (!empty($disc['type']))          $subs[] = esc_html($disc['type']);
if (!empty($disc['approval_date'])) $subs[] = 'Approved: ' . esc_html($disc['approval_date']);
$subtitle = implode(' · ', $subs);

/* Disc Type chip label */
$dt_map = [
  'distance' => 'Distance Driver',
  'fairway'  => 'Fairway Driver',
  'midrange' => 'Midrange',
  'putter'   => 'Putter',
];
$disc_type_label = isset($dt_map[$disc['disc_type'] ?? '']) ? $dt_map[$disc['disc_type']] : '';

// Get specs
$fields = [];
if (!empty($disc['meta_json'])) {
  $meta = json_decode($disc['meta_json'], true);
  if (is_array($meta) && !empty($meta['fields']) && is_array($meta['fields'])) {
    $fields = $meta['fields'];
  }
}

// Collection status
$disc_id = isset($disc['id']) ? (int)$disc['id'] : 0;
$already = false;
if ($disc_id && is_user_logged_in()) {
  $already = Collection::has_item(get_current_user_id(), $disc_id);
}

// --- Resolve flight numbers: Manufacturer first, else active Community ---
global $wpdb;
$disc_id  = (int)($disc['id'] ?? 0);           // or however you have $disc in scope
$discName = (string)($disc['name'] ?? 'Disc');

$S=$G=$T=$F=null;

// Manufacturer numbers from discs table
if ($disc_id > 0) {
    $row = $wpdb->get_row($wpdb->prepare(
        "SELECT speed, glide, turn, fade FROM {$wpdb->prefix}discgolf_discs WHERE id=%d",
        $disc_id
    ), ARRAY_A);

    if ($row && $row['speed'] !== null && $row['glide'] !== null && $row['turn'] !== null && $row['fade'] !== null) {
        $S=(float)$row['speed']; $G=(float)$row['glide']; $T=(float)$row['turn']; $F=(float)$row['fade'];
    }
}

// Fallback: active community numbers
if ($S===null || $G===null || $T===null || $F===null) {
    $c = $wpdb->get_row($wpdb->prepare(
        "SELECT f.speed, f.glide, f.turn, f.fade
           FROM {$wpdb->prefix}discgolf_flight_props_active a
           JOIN {$wpdb->prefix}discgolf_flight_props f ON f.id = a.flight_props_id
          WHERE a.disc_id=%d LIMIT 1",
        $disc_id
    ), ARRAY_A);

    if ($c) {
        $S = $S===null ? (float)$c['speed']  : $S;
        $G = $G===null ? (float)$c['glide']  : $G;
        $T = $T===null ? (float)$c['turn']   : $T;
        $F = $F===null ? (float)$c['fade']   : $F;
    }
}

$has_numbers = is_finite($S ?? INF) && is_finite($G ?? INF) && is_finite($T ?? INF) && is_finite($F ?? INF);

if ($has_numbers) :
    $discPayload = [
        'name'  => $discName,
        'speed' => $S, 'glide' => $G, 'turn' => $T, 'fade' => $F,
    ];
    else:
              // Optional: show your existing “No flight numbers” note instead of the chart
    endif;
?>

<div class="dgdb-container dgdb-single-compact">
  <div class="dgdb-contact-card">
    <div class="dgdb-card-main">

      <div class="dgdb-section-card">
        <div class="dgdb-hero-compact">
          <div class="dgdb-image-section">
            <div class="dgdb-media dgdb-media-compact">
              <?php if (!empty($img['image_url'])): ?>
                <img src="<?php echo esc_url($img['image_url']); ?>" alt="<?php echo esc_attr($title); ?>" class="dgdb-disc-image">
              <?php else: ?>
                <div class="dgdb-no-image">
                  <?php echo dgdb_disc_placeholder_svg_markup($disc['manufacturer'] ?? '', $disc['name'] ?? '', 120); ?>
                </div>
              <?php endif; ?>
            </div>
            <?php if (!empty($img['source_url'])): ?>
              <div class="dgdb-image-credit">
                <a href="<?php echo esc_url($img['source_url']); ?>" target="_blank" rel="noopener">Bildkälla</a>
              </div>
            <?php endif; ?>
          </div>

          <div class="dgdb-info-section">
            <h1 class="dgdb-disc-title"><?php echo esc_html($title); ?></h1>
            <?php if ($subtitle): ?>
              <div class="dgdb-disc-subtitle"><?php echo $subtitle; ?></div>
            <?php endif; ?>

            <!-- Quick specs (hidden flight numbers) -->
            <?php if ($fields): ?>
              <div class="dgdb-quick-specs">
                <?php
                $priority_fields = ['Speed', 'Glide', 'Turn', 'Fade', 'Stability', 'Plastic'];
                $shown = 0;
                foreach ($priority_fields as $key):
                  if (isset($fields[$key]) && $fields[$key] !== '' && $shown < 4):
                    $shown++;
                ?>
                  <!-- reserved -->
                <?php
                  endif;
                endforeach;
                ?>
              </div>
            <?php endif; ?>

            <div class="dgdb-actions">
              <?php if (is_user_logged_in()): ?>
                <button class="dgdb-btn dgdb-btn-primary dgdb-collect-btn"
                        data-disc="<?php echo (int)$disc_id; ?>"
                        data-state="<?php echo $already ? 'in' : 'out'; ?>">
                  <?php echo $already ? 'Remove from my collection' : 'Add to my collection'; ?>
                </button>
              <?php endif; ?>
            </div>
          </div>
        </div>
      </div>

      <div class="dgdb-content-sections">
        <!-- Full specifications (hide flight ratings) -->
        <?php if (!empty($fields) && is_array($fields)): ?>
          <?php $dgdb_hidden_spec_keys = ['speed','glide','turn','fade','stability']; ?>
          <div class="dgdb-section-card">
            <h3 class="dgdb-section-title">Specifications</h3>
            <div class="dgdb-specs-grid">
              <?php foreach ($fields as $label => $value):
                if ($value === '' || $value === null) continue;
                if (in_array(strtolower((string)$label), $dgdb_hidden_spec_keys, true)) continue;
              ?>
                <div class="dgdb-spec-row">
                  <span class="dgdb-spec-label"><?php echo esc_html($label); ?></span>
                  <span class="dgdb-spec-value">
                    <?php echo esc_html(is_array($value) ? wp_json_encode($value) : (string)$value); ?>
                  </span>
                </div>
              <?php endforeach; ?>
            </div>
          </div>
        <?php endif; ?>

        <!-- flight numbers panels -->
        <div class="dgdb-section-card" id="flex-test">
          <?php include __DIR__ . '/parts/flight-numbers.php'; ?>
          <?php include __DIR__ . '/parts/community-flight-numbers.php'; ?>
          
        </div>

        

        <!-- Wiki panels -->
        <div class="dgdb-section-card">
          <h3 class="dgdb-section-title">Community Info</h3>
          <div class="dgdb-wiki-panels">
            <?php include __DIR__ . '/parts/single-wiki-panels.php'; ?>
            <!-- Gallery -->
        <div class="dgdb-section-card">
          <h3 class="dgdb-section-title">Gallery</h3>
          <div class="dgdb-gallery-content">
            <?php include __DIR__ . '/parts/single-gallery.php'; ?>
            <?php if (is_user_logged_in()): ?>
            <div class="dgdb-upload-section">
              <h4 class="dgdb-upload-title">Upload Image</h4>
              <form class="dgdb-wiki-form dgdb-wiki-form--image" enctype="multipart/form-data">
                <div class="dgdb-upload-grid">
                  <input type="file" name="image" accept="image/*" class="dgdb-file-input">
                  <input type="text" name="attribution_text" placeholder="Attribution (optional)" class="dgdb-wiki-input">
                  <input type="url"  name="attribution_url"  placeholder="Source URL (optional)" class="dgdb-wiki-input">
                  <input type="text" name="license"          placeholder="License (e.g. CC BY-SA 4.0)" class="dgdb-wiki-input">
                </div>
                <button type="submit" class="dgdb-btn">Upload to Gallery</button>
                <div class="dgdb-wiki-status--image muted dgdb-wiki-status"></div>
              </form>
            </div>
            <?php endif; ?>
          </div>
        </div>
          </div>
        </div>

        

        <!-- Reviews -->
        <div class="dgdb-section-card">
          <h3 class="dgdb-section-title">Reviews</h3>
          <div class="dgdb-reviews-content">
            <?php include __DIR__ . '/parts/single-reviews.php'; ?>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>


<style>
.dgdb-chip.dgdb-chip--type{
  display:inline-block; font-size:12px; line-height:18px; padding:2px 10px;
  border-radius:999px; border:1px solid #cfe6ff; background:#eef7ff; color:#1463d6;
}
</style>  