<?php
defined('ABSPATH') || exit;

/** Local image → get registered sizes if attachment exists */
function dgdb_best_image_versions($url) : array {
    if (!$url) return ['thumb' => '', 'large' => ''];
    $att = attachment_url_to_postid($url);
    if (!$att) return ['thumb' => $url, 'large' => $url];

    $t = wp_get_attachment_image_src($att, 'dgdb_thumb');
    $l = wp_get_attachment_image_src($att, 'dgdb_large');
    return [
        'thumb' => $t[0] ?? $url,
        'large' => $l[0] ?? $url,
    ];
}

if (!function_exists('dgdb_disc_placeholder_svg_markup')) {
    function dgdb_disc_placeholder_svg_markup(string $manufacturer, string $name, int $size = 56): string {
    $seed = md5(strtolower(trim($manufacturer.'|'.$name)));

    // Pseudo-slumptal från seed (stabilt per disc)
    $b = [];
    for ($i=0; $i<8; $i++) { $b[$i] = hexdec(substr($seed, $i*2, 2)) / 255; }

    $h1 = (int) round($b[0] * 360);
    $h2 = (int) round(fmod($b[1] + 0.25, 1) * 360);
    $s1 = 45 + (int) round($b[2] * 25);
    $l1 = 60 + (int) round($b[3] * 20);
    $s2 = 50 + (int) round($b[4] * 25);
    $l2 = 55 + (int) round($b[5] * 20);
    $angle = (int) round($b[6] * 360);

    // Unikt, deterministiskt gradient-id (viktigt för inline-SVG)
    $gid = 'dgdb_g_' . substr($seed, 0, 10) . '_' . $size;

    // Initialer (max 2)
    $initials = '';
    foreach (preg_split('/\s+/', trim($name)) as $w) {
        if ($w === '') continue;
        $ch = mb_substr($w, 0, 1);
        if (preg_match('/[A-Za-z0-9]/u', $ch)) $initials .= mb_strtoupper($ch);
        if (mb_strlen($initials) >= 2) break;
    }
    if ($initials === '') $initials = 'DG';

    $font = (int) round($size * 0.50);
    $rx   = (int) round($size * 0.22);

    return <<<SVG
<svg xmlns="http://www.w3.org/2000/svg" width="$size" height="$size" viewBox="0 0 $size $size" aria-hidden="true" focusable="false">
  <defs>
    <linearGradient id="$gid" x1="0" y1="0" x2="1" y2="1" gradientTransform="rotate($angle .5 .5)">
      <stop offset="0" stop-color="hsl($h1, {$s1}%, {$l1}%)"/>
      <stop offset="1" stop-color="hsl($h2, {$s2}%, {$l2}%)"/>
    </linearGradient>
  </defs>
  <rect width="$size" height="$size" rx="$rx" fill="url(#$gid)"/>
  <text x="50%" y="50%" text-anchor="middle" dominant-baseline="central"
        font-family="system-ui,-apple-system,Segoe UI,Roboto" font-size="$font"
        fill="rgba(255,255,255,0.95)" style="font-weight:700; letter-spacing:.5px">$initials</text>
</svg>
SVG;

    }
}

