<?php
defined('ABSPATH') || exit;
global $wpdb;

require_once __DIR__ . '/_helpers.php';

/* -------------------------------------------------
 * Query params
 * ------------------------------------------------- */
$dg_m    = isset($_GET['dg_m'])    ? sanitize_text_field(wp_unslash($_GET['dg_m'])) : '';
$dg_y    = isset($_GET['dg_y'])    ? intval($_GET['dg_y']) : 0;
$dg_q    = isset($_GET['dg_q'])    ? sanitize_text_field(wp_unslash($_GET['dg_q'])) : '';
$dg_sort = isset($_GET['dg_sort']) ? sanitize_text_field(wp_unslash($_GET['dg_sort'])) : 'newest';
$dg_p    = isset($_GET['dg_p'])    ? max(1, intval($_GET['dg_p'])) : 1;

/* Flight number filters */
$dg_sp = isset($_GET['dg_sp']) ? trim((string) wp_unslash($_GET['dg_sp'])) : '';
$dg_gl = isset($_GET['dg_gl']) ? trim((string) wp_unslash($_GET['dg_gl'])) : '';
$dg_tn = isset($_GET['dg_tn']) ? trim((string) wp_unslash($_GET['dg_tn'])) : '';
$dg_fd = isset($_GET['dg_fd']) ? trim((string) wp_unslash($_GET['dg_fd'])) : '';

/* Disc Type filter */
$dg_dt = isset($_GET['dg_dt']) ? sanitize_text_field(wp_unslash($_GET['dg_dt'])) : '';

$base_url = get_permalink();
$per_page = 25;

/* -------------------------------------------------
 * WHERE
 * ------------------------------------------------- */
$where = [];
$args  = [];

/* existing filters */
if ($dg_m !== '') {
  $where[] = "manufacturer = %s";
  $args[]  = $dg_m;
}
if ($dg_y > 0) {
  $where[] = "approval_year = %d";
  $args[]  = $dg_y;
}
if ($dg_q !== '') {
  $like    = '%' . $wpdb->esc_like($dg_q) . '%';
  $where[] = "(name LIKE %s OR manufacturer LIKE %s)";
  $args[]  = $like;
  $args[]  = $like;
}

/* Disc Type exact match */
if ($dg_dt !== '') {
  $where[] = "disc_type = %s";
  $args[]  = $dg_dt;
}

/**
 * Exact numeric match on DECIMAL columns, ignore NULLs.
 * Also supports legacy JSON fallback (for any older rows).
 */
$add_eq = function (string $col, string $jsonKey, $val) use (&$where, &$args) {
  if ($val === '' || !is_numeric($val)) return;

  $where[] = "
  (
    (
      `$col` IS NOT NULL
      AND ABS(CAST(`$col` AS DECIMAL(6,2)) - CAST(%s AS DECIMAL(6,2))) < 0.001
    )
    OR
    (
      JSON_EXTRACT(meta_json, %s) IS NOT NULL
      AND JSON_UNQUOTE(JSON_EXTRACT(meta_json, %s)) <> ''
      AND JSON_UNQUOTE(JSON_EXTRACT(meta_json, %s)) REGEXP '^-?[0-9]+(\\.[0-9]+)?$'
      AND ABS(
        CAST(JSON_UNQUOTE(JSON_EXTRACT(meta_json, %s)) AS DECIMAL(6,2))
        - CAST(%s AS DECIMAL(6,2))
      ) < 0.001
    )
  )";

  $path = '$.fields.' . $jsonKey;

  $args[] = (string)(float)$val; // column compare value
  $args[] = $path;               // JSON path (x4)
  $args[] = $path;
  $args[] = $path;
  $args[] = $path;
  $args[] = (string)(float)$val; // JSON compare value
};

$add_eq('speed', 'speed', $dg_sp);
$add_eq('glide', 'glide', $dg_gl);
$add_eq('turn',  'turn',  $dg_tn);
$add_eq('fade',  'fade',  $dg_fd);

$where_sql = $where ? ('WHERE ' . implode(' AND ', $where)) : '';

/* -------------------------------------------------
 * Sorting
 * ------------------------------------------------- */
switch ($dg_sort) {
  case 'newest': $order_by = "ORDER BY approval_date DESC, id DESC"; break;
  case 'oldest': $order_by = "ORDER BY approval_date ASC, id ASC";   break;
  case 'za':     $order_by = "ORDER BY name DESC, manufacturer ASC"; break;
  default:       $order_by = "ORDER BY name ASC, manufacturer ASC";
}

/* -------------------------------------------------
 * Facets (cached)
 * ------------------------------------------------- */
$mans  = \DiscGolfDB\Core\Cache::get_manufacturers();
$years = \DiscGolfDB\Core\Cache::get_approval_years();

/* -------------------------------------------------
 * Count + pagination
 * ------------------------------------------------- */
$count_sql = "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_discs {$where_sql}";
$count_q   = $args ? $wpdb->prepare($count_sql, $args) : $count_sql;
$total     = (int) $wpdb->get_var($count_q);

$pages  = max(1, (int) ceil($total / $per_page));
$offset = ($dg_p - 1) * $per_page;

/* -------------------------------------------------
 * Slice (list)
 * ------------------------------------------------- */
$list_sql = "SELECT *
             FROM {$wpdb->prefix}discgolf_discs
             {$where_sql}
             {$order_by}
             LIMIT %d OFFSET %d";
$list_args   = $args;
$list_args[] = $per_page;
$list_args[] = $offset;

$list_q = $wpdb->prepare($list_sql, $list_args);
$rows   = $wpdb->get_results($list_q, ARRAY_A) ?: [];

/* -------------------------------------------------
 * Enrich row with single page URL + disc type label for card chip
 * ------------------------------------------------- */
$disc_base = trailingslashit(home_url('/disc'));

$dt_map = [
  'distance' => 'Distance Driver',
  'fairway'  => 'Fairway Driver',
  'midrange' => 'Midrange',
  'putter'   => 'Putter',
];
foreach ($rows as &$r) {
  $r['disc_url'] = esc_url($disc_base . (int) $r['id']);
  $dt = isset($r['disc_type']) ? (string)$r['disc_type'] : '';
  $r['disc_type_label'] = isset($dt_map[$dt]) ? $dt_map[$dt] : '';
}
unset($r);

/* -------------------------------------------------
 * Link helper — preserve ALL filters across pagination
 * ------------------------------------------------- */
$dgdb_link = function(array $overrides = []) use ($base_url, $dg_m, $dg_y, $dg_q, $dg_sort, $dg_p, $dg_sp, $dg_gl, $dg_tn, $dg_fd, $dg_dt) {
  $q = [
    'dg_m'    => $dg_m,
    'dg_y'    => $dg_y,
    'dg_q'    => $dg_q,
    'dg_sort' => $dg_sort,
    'dg_p'    => $dg_p,
    'dg_sp'   => $dg_sp,
    'dg_gl'   => $dg_gl,
    'dg_tn'   => $dg_tn,
    'dg_fd'   => $dg_fd,
    'dg_dt'   => $dg_dt,
  ];
  foreach ($overrides as $k => $v) $q[$k] = $v;
  return esc_url(add_query_arg($q, $base_url));
};

?>
<div class="dgdb-wrap">
  <?php include __DIR__ . '/parts/filters.php'; ?>

  <div class="dgdb-meta">
    <?php echo esc_html($total); ?> results · Page <?php echo (int)$dg_p; ?> / <?php echo (int)$pages; ?>
  </div>

  <script>
    window.DGDB = window.DGDB || {};
    DGDB.useDiscPage = true;
    DGDB.discBase = <?php echo wp_json_encode(trailingslashit(home_url('/disc'))); ?>;
  </script>

  <?php
  /**
   * NOTE FOR grid.php:
   * Each $row now has $row['disc_type_label'] you can render as a chip on the card.
   * Example inside grid card:
   *   <?php if (!empty($row['disc_type_label'])): ?>
   *     <span class="dgdb-chip dgdb-chip--type"><?php echo esc_html($row['disc_type_label']); ?></span>
   *   <?php endif; ?>
   */
  include __DIR__ . '/parts/grid.php';
  ?>

  <?php if ($pages > 1): ?>
    <nav class="dgdb-pager">
      <?php if ($dg_p > 1): ?>
        <a class="dgdb-btn" href="<?php echo $dgdb_link(['dg_p' => $dg_p - 1]); ?>">‹ Previous</a>
      <?php else: ?>
        <span class="dgdb-btn disabled">‹ Previous</span>
      <?php endif; ?>
      <span class="muted">Page <?php echo (int)$dg_p; ?> of <?php echo (int)$pages; ?></span>
      <?php if ($dg_p < $pages): ?>
        <a class="dgdb-btn" href="<?php echo $dgdb_link(['dg_p' => $dg_p + 1]); ?>">Next ›</a>
      <?php else: ?>
        <span class="dgdb-btn disabled">Next ›</span>
      <?php endif; ?>
    </nav>
  <?php endif; ?>
</div>

<style>
/* optional minimal style for the chip if your theme doesn't already have one */
.dgdb-chip.dgdb-chip--type{
  display:inline-block; font-size:12px; line-height:18px; padding:2px 10px;
  border-radius:999px; border:1px solid #cfe6ff; background:#eef7ff; color:#1463d6;
}
</style>
