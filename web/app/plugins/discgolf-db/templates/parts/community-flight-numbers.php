<?php
defined('ABSPATH') || exit;

/** @var array $disc */
global $wpdb;

$disc_id   = isset($disc['id']) ? (int)$disc['id'] : 0;
$disc_name = (string)($disc['name'] ?? 'Disc');

// Averages from reviews (only where at least one value exists)
$avg = null;
$contributors = 0;

if ($disc_id) {
  $avg = $wpdb->get_row(
    $wpdb->prepare("
      SELECT
        ROUND(AVG(speed), 1) AS speed,
        ROUND(AVG(glide), 1) AS glide,
        ROUND(AVG(turn),  1) AS turn,
        ROUND(AVG(fade),  1) AS fade,
        COUNT(*)            AS n_reviews
      FROM {$wpdb->prefix}discgolf_reviews
      WHERE disc_id=%d
        AND (speed IS NOT NULL OR glide IS NOT NULL OR turn IS NOT NULL OR fade IS NOT NULL)
    ", $disc_id),
    ARRAY_A
  );
  $contributors = $avg ? (int)$avg['n_reviews'] : 0;
}

$has = $avg && (
  $avg['speed'] !== null || $avg['glide'] !== null || $avg['turn'] !== null || $avg['fade'] !== null
);

// Build payload for the chart
$discPayload = null;
if ($has) {
  $discPayload = [
    'name'  => $disc_name,
    'speed' => $avg['speed'] !== null ? (float)$avg['speed'] : null,
    'glide' => $avg['glide'] !== null ? (float)$avg['glide'] : null,
    'turn'  => $avg['turn']  !== null ? (float)$avg['turn']  : null,
    'fade'  => $avg['fade']  !== null ? (float)$avg['fade']  : null,
  ];
}

$contributors_label = ($contributors === 1)
  ? 'Based on 1 review'
  : ($contributors > 1 ? 'Based on ' . number_format_i18n($contributors) . ' reviews' : '');
?>

<div class="dgdb-card dgdb-spec-card" id="flight-rating-card">
  <div style="display:flex;align-items:center;justify-content:space-between;gap:8px;">
    <h3 class="dgdb-section-title" style="margin:0;">Community Flight Numbers</h3>
    <?php if ($has && $contributors > 0): ?>
      <span class="muted" style="font-size:12px;white-space:nowrap;"><?php echo esc_html($contributors_label); ?></span>
    <?php endif; ?>
  </div>

  <?php if ($has && !empty($discPayload)): ?>
    <section class="dgdb-flight chart-card">
      <header class="flight-head">
        <!-- Measuremnets -->
        <div class="flight-controls" role="group" aria-label="data-units" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Unit</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-units="meters">Meters</button>
          </div>
        </div>
        <!-- Hand -->
        <div class="flight-controls" role="group" aria-label="Hand" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Hand</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-hand="R">Right</button>
            <button class="dgdb-btn" data-hand="L">Left</button>
          </div>
        </div>
        <!-- Throw -->
        <div class="flight-controls" role="group" aria-label="Throw" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Type of throw</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-throw="BH">Backhand</button>
            <button class="dgdb-btn" data-throw="FH">Forehand</button>
          </div>
        </div>
        <!-- Arm speed -->
        <div class="flight-controls" role="group" aria-label="Armspeed" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Armspeed</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn"        data-speed="slow">Weak</button>
            <button class="dgdb-btn is-active" data-speed="norm">Decent</button>
            <button class="dgdb-btn"        data-speed="fast">Pro</button>
          </div>
        </div>
      </header>

      <div
        class="flight-svg-wrap dgdb-flight-chart"
        data-disc="<?php echo esc_attr(
          wp_json_encode($discPayload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
        ); ?>">
      </div>
    </section>
  <?php endif; ?>

  <?php if ($has): ?>
    <div class="dgdb-flight-grid dgdb-flight-readonly">
      <?php if ($avg['speed'] !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Speed</span>
          <span class="dgdb-spec-value"><?php echo esc_html($avg['speed']); ?></span>
        </div>
      <?php endif; ?>
      <?php if ($avg['glide'] !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Glide</span>
          <span class="dgdb-spec-value"><?php echo esc_html($avg['glide']); ?></span>
        </div>
      <?php endif; ?>
      <?php if ($avg['turn'] !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Turn</span>
          <span class="dgdb-spec-value"><?php echo esc_html($avg['turn']); ?></span>
        </div>
      <?php endif; ?>
      <?php if ($avg['fade'] !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Fade</span>
          <span class="dgdb-spec-value"><?php echo esc_html($avg['fade']); ?></span>
        </div>
      <?php endif; ?>
    </div>
  <?php else: ?>
    <p class="muted">No community flight numbers yet — be the first to add yours in a review.</p>
  <?php endif; ?>

  
</div>
