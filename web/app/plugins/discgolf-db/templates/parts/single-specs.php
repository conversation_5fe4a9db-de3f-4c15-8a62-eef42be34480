<?php
/** @var array $disc */
$fields = [];
if (!empty($disc['meta_json'])) {
  $meta = json_decode($disc['meta_json'], true);
  if (is_array($meta) && !empty($meta['fields']) && is_array($meta['fields'])) {
    $fields = $meta['fields']; // ✅ samma som griden
  }
}
?>
<?php if ($fields): ?>
<div class="dgdb-card" style="border:1px solid #eee;border-radius:8px;padding:12px;margin-top:12px;">
  <h3 style="margin-top:0;">Specifikationer</h3>
  <table class="dgdb-details-table dgdb-details-table--compact">
    <?php foreach ($fields as $label => $value):
      if ($value === '' || $value === null) continue; ?>
      <tr>
        <th style="text-align:left;padding:4px 8px;"><?php echo esc_html($label); ?></th>
        <td style="padding:4px 8px;"><?php
          echo esc_html(is_array($value) ? wp_json_encode($value) : (string)$value);
        ?></td>
      </tr>
    <?php endforeach; ?>
  </table>
</div>
<?php endif; ?>
