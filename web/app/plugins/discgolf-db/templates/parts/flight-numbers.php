<?php
// Always start with a defined array
$fields = [];

// Decode meta_json → fields (if present)
if (!empty($disc['meta_json'])) {
    $meta = json_decode($disc['meta_json'], true);
    if (is_array($meta) && !empty($meta['fields']) && is_array($meta['fields'])) {
        $fields = $meta['fields'];
    }
}

/**
 * Helper: fetch a value from meta fields; if empty, fall back to DB column.
 * Returns null when no usable value.
 */
$val = function (string $key) use ($fields, $disc) {
    if (isset($fields[$key]) && $fields[$key] !== '') {
        return (string)$fields[$key];
    }
    if (isset($disc[$key]) && $disc[$key] !== '' && $disc[$key] !== null) {
        return (string)$disc[$key];
    }
    return null;
};

$speed = $val('speed');
$glide = $val('glide');
$turn  = $val('turn');
$fade  = $val('fade');

// do we have anything to show?
$has_flight_data = ($speed !== null) || ($glide !== null) || ($turn !== null) || ($fade !== null);

// Payload for the JS chart (floats)
$discPayload = [
  'name'  => $disc['name'] ?? 'Disc',
  'speed' => (float)($speed ?? 0),
  'glide' => (float)($glide ?? 0),
  'turn'  => (float)($turn  ?? 0),
  'fade'  => (float)($fade  ?? 0),
];
?>

<div class="dgdb-card dgdb-spec-card" id="flight-rating-card">
  <div style="display:flex;align-items:center;justify-content:space-between;gap:8px;">
    <h3 class="dgdb-section-title" style="margin:0;">Manufacturer Flight Numbers</h3>
    <span class="muted" style="font-size:12px;white-space:nowrap;">Based on Manufacturer data</span>
  </div>

  <?php if ($has_flight_data): ?>
    <section class="dgdb-flight chart-card">
      <header class="flight-head">
        <!-- Group 0: Measuremnets -->
        <div class="flight-controls" role="group" aria-label="data-units" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Unit</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-units="meters">Meters</button>
          </div>
        </div>
        <!-- Group 1: Hand -->
        <div class="flight-controls" role="group" aria-label="Hand" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Hand</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-hand="R">Right</button>
            <button class="dgdb-btn" data-hand="L">Left</button>
          </div>
        </div>

        <!-- Group 2: Throw -->
        <div class="flight-controls" role="group" aria-label="Type of throw" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Type of throw</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn is-active" data-throw="BH">Backhand</button>
            <button class="dgdb-btn" data-throw="FH">Forehand</button>
          </div>
        </div>

        <!-- Group 3: Arm Speed -->
        <div class="flight-controls" role="group" aria-label="Armspeed" style="display:flex;flex-direction:column;">
          <div class="dgdb-flight-control-label" style="font-size:10px;">Armspeed</div>
          <div class="dgdb-flight-control-buttons">
            <button class="dgdb-btn"        data-speed="slow">Weak</button>
            <button class="dgdb-btn is-active" data-speed="norm">Decent</button>
            <button class="dgdb-btn"        data-speed="fast">Pro</button>
          </div>
        </div>
      </header>

      <div
        class="flight-svg-wrap dgdb-flight-chart"
        data-disc='<?php echo esc_attr( wp_json_encode( $discPayload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES ) ); ?>'>
      </div>
    </section>
  <?php else: ?>
    <p class="muted">No flight numbers available for this disc yet.</p>
  <?php endif; ?>

  <?php if ($has_flight_data): ?>
    <div class="dgdb-flight-grid dgdb-flight-readonly">
      <?php if ($speed !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Speed</span>
          <span class="dgdb-spec-value"><?php echo esc_html($speed); ?></span>
        </div>
      <?php endif; ?>

      <?php if ($glide !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Glide</span>
          <span class="dgdb-spec-value"><?php echo esc_html($glide); ?></span>
        </div>
      <?php endif; ?>

      <?php if ($turn !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Turn</span>
          <span class="dgdb-spec-value"><?php echo esc_html($turn); ?></span>
        </div>
      <?php endif; ?>

      <?php if ($fade !== null): ?>
        <div class="dgdb-spec-item">
          <span class="dgdb-spec-label">Fade</span>
          <span class="dgdb-spec-value"><?php echo esc_html($fade); ?></span>
        </div>
      <?php endif; ?>
    </div>
  <?php endif; ?>
</div>
