<?php
defined('ABSPATH') || exit;
/** @var array $disc */
$title = esc_html(($disc['manufacturer'] ?? '') . ' ' . ($disc['name'] ?? ''));
$subs  = [];
if (!empty($disc['type']))          $subs[] = esc_html($disc['type']);
if (!empty($disc['approval_date'])) $subs[] = 'Approved: ' . esc_html($disc['approval_date']);
$subtitle = implode(' · ', $subs);
?>
<a href="<?php echo esc_url(get_permalink(get_option('page_for_posts')) ?: home_url('/')); ?>"
   class="dgdb-btn dgdb-back-btn">← Back</a>

<header class="dgdb-page-header">
  <h1 class="dgdb-page-title"><?php echo $title; ?></h1>
  <?php if ($subtitle): ?>
    <div class="muted dgdb-page-subtitle"><?php echo esc_html($subtitle); ?></div>
  <?php endif; ?>
</header>
