<?php
defined('ABSPATH') || exit;

/** Pull disc + id from $ctx coming from single-disc.php */
$disc    = $ctx['disc'] ?? [];
$disc_id = isset($ctx['disc_id']) ? (int)$ctx['disc_id'] : (int)($disc['id'] ?? 0);

$ajax  = admin_url('admin-ajax.php');
$nonce = wp_create_nonce('dgdb');

/* ---- Build manufacturer flight map for JS ---- */
$fields = [];
if (!empty($disc['meta_json'])) {
  $meta = json_decode($disc['meta_json'], true);
  if (is_array($meta) && !empty($meta['fields']) && is_array($meta['fields'])) {
    $fields = $meta['fields'];
  }
}
$pick = function ($key) use ($disc, $fields) {
  if (isset($disc[$key]) && $disc[$key] !== '' && $disc[$key] !== null) {
    return (float)$disc[$key];
  }
  foreach ([$key, ucfirst($key), strtoupper($key), ucfirst(strtolower($key))] as $k) {
    if (isset($fields[$k]) && $fields[$k] !== '') return (float)$fields[$k];
  }
  return null;
};
$mfg = [
  'speed' => $pick('speed'),
  'glide' => $pick('glide'),
  'turn'  => $pick('turn'),
  'fade'  => $pick('fade'),
];
?>
<script>
  // Provide config + manufacturer flight numbers for reviews.js
  window.DGDB = window.DGDB || {};
  DGDB.ajax      = <?php echo wp_json_encode($ajax); ?>;
  DGDB.nonce     = <?php echo wp_json_encode($nonce); ?>;
  DGDB.discId    = <?php echo (int)$disc_id; ?>;
  DGDB.mfgFlight = <?php echo wp_json_encode($mfg); ?>;
</script>

<section
  class="dgdb-reviews dgdb-section-spacing"
  data-disc-id="<?= (int)$disc_id ?>"
  data-mfg-speed="<?= esc_attr($mfg['speed'] ?? '') ?>"
  data-mfg-glide="<?= esc_attr($mfg['glide'] ?? '') ?>"
  data-mfg-turn="<?= esc_attr($mfg['turn'] ?? '') ?>"
  data-mfg-fade="<?= esc_attr($mfg['fade'] ?? '') ?>"
>
  <?php if (is_user_logged_in()): ?>
    <form id="dgdb-review-form" class="dgdb-review-form dgdb-form-grid-review" method="post" action="">
      <!-- Title -->
      <div class="dgdb-form-row">
        <label for="dgdb-title" class="dgdb-form-label-fixed">Title</label>
        <input id="dgdb-title" name="title" type="text" class="dgdb-input" placeholder="Short headline (optional)" />
      </div>

      <!-- Plastic / Weight -->
      <div class="dgdb-form-row">
        <label for="dgdb-plastic" class="dgdb-form-label-fixed">Plastic</label>
        <input id="dgdb-plastic" name="plastic_type" type="text" class="dgdb-input" placeholder="e.g. Champion, Neo, 400G (optional)" />
      </div>
      <div class="dgdb-form-row">
        <label for="dgdb-weight" class="dgdb-form-label-fixed">Weight</label>
        <input id="dgdb-weight" name="weight" type="number" min="100" max="300" step="1" class="dgdb-input" placeholder="grams (optional)" />
      </div>

      <!-- Community Flight Numbers (optional) -->
      <fieldset class="dgdb-fieldset">
        <legend>Community Flight Numbers (optional)</legend>

        <div class="dgdb-fn-actions">
          <button id="dgdb-fn-use-mfg" class="dgdb-btn dgdb-btn-light" type="button">Use manufacturer values</button>
          <button id="dgdb-fn-clear" class="dgdb-btn dgdb-btn-ghost" type="button">Clear</button>
        </div>

        <div class="dgdb-fn-grid">
          <div class="dgdb-fn-cell">
            <label class="dgdb-fn-label" for="dgdb-fn-speed">Speed</label>
            <input id="dgdb-fn-speed" name="speed" class="dgdb-input dgdb-input--num" type="number" min="0" max="15" step="0.5" inputmode="decimal" />
            <input id="dgdb-fn-speed-range" class="dgdb-range" type="range" min="0" max="15" step="0.5" />
          </div>
          <div class="dgdb-fn-cell">
            <label class="dgdb-fn-label" for="dgdb-fn-glide">Glide</label>
            <input id="dgdb-fn-glide" name="glide" class="dgdb-input dgdb-input--num" type="number" min="0" max="7" step="0.5" inputmode="decimal" />
            <input id="dgdb-fn-glide-range" class="dgdb-range" type="range" min="0" max="7" step="0.5" />
          </div>
          <div class="dgdb-fn-cell">
            <label class="dgdb-fn-label" for="dgdb-fn-turn">Turn</label>
            <input id="dgdb-fn-turn" name="turn" class="dgdb-input dgdb-input--num" type="number" min="-5" max="2" step="0.5" inputmode="decimal" />
            <input id="dgdb-fn-turn-range" class="dgdb-range" type="range" min="-5" max="2" step="0.5" />
          </div>
          <div class="dgdb-fn-cell">
            <label class="dgdb-fn-label" for="dgdb-fn-fade">Fade</label>
            <input id="dgdb-fn-fade" name="fade" class="dgdb-input dgdb-input--num" type="number" min="0" max="5" step="0.5" inputmode="decimal" />
            <input id="dgdb-fn-fade-range" class="dgdb-range" type="range" min="0" max="5" step="0.5" />
          </div>
        </div>

        <div class="dgdb-fn-preview">
          <span id="dgdb-fn-prev-speed" class="dgdb-chip muted">Speed —</span>
          <span id="dgdb-fn-prev-glide" class="dgdb-chip muted">Glide —</span>
          <span id="dgdb-fn-prev-turn"  class="dgdb-chip muted">Turn —</span>
          <span id="dgdb-fn-prev-fade"  class="dgdb-chip muted">Fade —</span>
        </div>
      </fieldset>

      <!-- Text -->
      <div class="dgdb-review-form-row">
        <label for="dgdb-review" class="dgdb-label">Text</label>
        <textarea id="dgdb-review" name="review" rows="4" maxlength="2000" class="dgdb-review-textarea" placeholder="How does it fly for you?"></textarea>
      </div>

      <!-- Rating -->
      <div class="dgdb-form-row">
        <label for="dgdb-rating" class="dgdb-form-label-fixed">Score</label>
        <select id="dgdb-rating" name="rating" class="dgdb-review-select">
          <option value="5">5 ★★★★★</option>
          <option value="4">4 ★★★★☆</option>
          <option value="3">3 ★★★☆☆</option>
          <option value="2">2 ★★☆☆☆</option>
          <option value="1">1 ★☆☆☆☆</option>
        </select>
      </div>

      <div class="dgdb-review-actions">
        <button type="submit" class="dgdb-btn dgdb-btn-primary">
          <span class="dgdb-btn-spinner" aria-hidden="true"></span>
          Save Review
        </button>
        <span class="dgdb-status muted" aria-live="polite"></span>
      </div>

      <input type="hidden" name="disc_id" value="<?= (int)$disc_id ?>" />
      <input type="hidden" name="action" value="dg_add_review" />
      <input type="hidden" name="nonce" value="<?php echo esc_attr($nonce); ?>" />
    </form>
  <?php else: ?>
    <div class="muted dgdb-login-prompt">Login to add a review.</div>
  <?php endif; ?>

  <h3 class="dgdb-reviews-heading">Latest Reviews</h3>
  <div id="dgdb-review-empty" class="muted dgdb-hidden">No reviews yet.</div>

  <div id="dgdb-review-list" class="dgdb-review-cards" aria-live="polite"></div>

  <div class="dgdb-reviews-more-container">
    <button id="dgdb-review-more" class="dgdb-btn">Show more</button>
  </div>

  <div id="dgdb-review-modal" class="dgdb-review-modal" hidden aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="dgdb-review-modal-title">
    <div class="dgdb-review-modal__backdrop" data-close></div>
    <div class="dgdb-review-modal__card">
      <button class="dgdb-review-modal__close" type="button" title="Close" aria-label="Close" data-close>✕</button>
      <div class="dgdb-review-modal__body">
        <h4 id="dgdb-review-modal-title" class="dgdb-modal-title"></h4>
        <div class="dgdb-modal-meta"></div>
        <div class="dgdb-modal-stars"></div>
        <div class="dgdb-modal-flight"></div>
        <div class="dgdb-modal-text"></div>
      </div>
    </div>
  </div>
</section>
