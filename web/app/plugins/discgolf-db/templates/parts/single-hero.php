<?php
/** @var array $disc, $img */
use DiscGolfDB\CollectionCPT;

$title = trim(($disc['manufacturer'] ? $disc['manufacturer'].' ' : '') . $disc['name']);
$subs  = [];
if (!empty($disc['type']))          $subs[] = esc_html($disc['type']);
if (!empty($disc['approval_date'])) $subs[] = 'Approved: ' . esc_html($disc['approval_date']);
$subtitle = implode(' · ', $subs);

// Resolve current disc id safely
$disc_id = isset($disc['id']) ? (int)$disc['id'] : 0;

// Determine if this disc is already in the current user's collection (using CPT + meta)
$already = false;
if ($disc_id && is_user_logged_in()) {
  $posts = get_posts([
    'post_type'      => CollectionCPT::SLUG,
    'author'         => get_current_user_id(),
    'post_status'    => 'any',
    'posts_per_page' => 1,
    'fields'         => 'ids',
    'meta_query'     => [[
      'key'     => 'disc_id',
      'value'   => $disc_id,
      'compare' => '=',
      'type'    => 'NUMERIC',
    ]],
  ]);
  $already = !empty($posts);
}
?>
<header style="margin-bottom:16px;">
  <h1 style="margin:0 0 6px 0;"><?php echo esc_html($title); ?></h1>
  <?php if ($subtitle): ?>
    <div class="muted" style="margin:0 0 16px 0;"><?php echo $subtitle; ?></div>
  <?php endif; ?>
</header>

<section class="dgdb-disc-hero" style="display:grid;grid-template-columns:260px 1fr;gap:20px;align-items:start;">
  <div>
    <div class="dgdb-media" style="background:#fafafa;border:1px solid #eee;border-radius:8px;overflow:hidden;">
      <?php if (!empty($img['image_url'])): ?>
        <img src="<?php echo esc_url($img['image_url']); ?>" alt="" style="width:100%;height:auto;display:block;">
      <?php else: ?>
        <div style="aspect-ratio:1/1;display:flex;align-items:center;justify-content:center;color:#777;">Ingen bild ännu</div>
      <?php endif; ?>
    </div>
    <?php if (!empty($img['source_url'])): ?>
      <div class="muted" style="margin-top:6px;">Bildkälla:
        <a href="<?php echo esc_url($img['source_url']); ?>" target="_blank" rel="noopener">länk</a>
      </div>
    <?php endif; ?>

    <?php if (is_user_logged_in()): ?>
    <form class="dgdb-wiki-form dgdb-wiki-form--image" enctype="multipart/form-data" style="margin-top:12px;">
      <input type="file" name="image" accept="image/*">
      <input type="text" name="attribution_text" placeholder="Attribution (valfritt)" style="display:block;margin-top:6px;width:100%;">
      <input type="url"  name="attribution_url"  placeholder="Källa URL (valfritt)" style="display:block;margin-top:6px;width:100%;">
      <input type="text" name="license"          placeholder="Licens (t.ex. CC BY-SA 4.0)" style="display:block;margin-top:6px;width:100%;">
      <button type="submit" class="dgdb-btn" style="margin-top:8px;">Ladda upp till galleri</button>
      <div class="dgdb-wiki-status--image muted" style="margin-top:6px;"></div>
    </form>
    <?php endif; ?>

    <div style="display:flex;gap:10px;align-items:center;margin:10px 0 16px;">
      <a href="<?php echo esc_url(home_url('/')); ?>" class="dgdb-btn">← Back</a>

      <?php if (is_user_logged_in()): ?>
        <button class="dgdb-btn dgdb-collect-btn"
                data-disc="<?php echo (int)$disc_id; ?>"
                data-state="<?php echo $already ? 'in' : 'out'; ?>">
          <?php echo $already ? 'Remove from my collection' : 'Add to my collection'; ?>
        </button>
        <span class="dgdb-collect-status muted"></span>
      <?php else: ?>
        <span class="muted">Log in to save to your collection.</span>
      <?php endif; ?>
    </div>

  </div>
  <div><!-- Right column continues in specs.php --></div>
</section>
