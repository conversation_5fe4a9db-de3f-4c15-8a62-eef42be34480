<?php
/** @var string $base_url, $dg_m, $dg_y, $dg_q, $dg_sort */
/** @var array<int,string> $mans, $years */

/* Current selections */
$dg_sp = isset($_GET['dg_sp']) ? (string) $_GET['dg_sp'] : '';
$dg_gl = isset($_GET['dg_gl']) ? (string) $_GET['dg_gl'] : '';
$dg_tn = isset($_GET['dg_tn']) ? (string) $_GET['dg_tn'] : '';
$dg_fd = isset($_GET['dg_fd']) ? (string) $_GET['dg_fd'] : '';
$dg_dt = isset($_GET['dg_dt']) ? (string) $_GET['dg_dt'] : ''; // Disc Type

/* Options (integers only; switch to 0.5 steps if you prefer) */
$speeds = range(1, 15);   // 1..15
$glides = range(1, 7);    // 1..7
$turns  = range(-5, 2);   // -5..2
$fades  = range(0, 5);    // 0..5

$disc_types = [
  ''          => 'All',
  'distance'  => 'Distance Drivers',
  'fairway'   => 'Fairway Drivers',
  'midrange'  => 'Midranges',
  'putter'    => 'Putters',
];
?>
<form class="dgdb-filters" method="get" action="<?php echo esc_url($base_url); ?>">

  <label class="grow">
    <span>Search</span>
    <input type="search" name="dg_q" value="<?php echo esc_attr($dg_q); ?>" placeholder="Search model or manufacturer…" />
  </label>

  <label>
    <span>Manufacturer</span>
    <select name="dg_m" onchange="this.form.submit()">
      <option value="">All</option>
      <?php foreach ($mans as $m): ?>
        <option value="<?php echo esc_attr($m); ?>" <?php selected($dg_m, $m); ?>>
          <?php echo esc_html($m); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <!-- Disc Type -->
  <label>
    <span>Disc Type</span>
    <select name="dg_dt" onchange="this.form.submit()">
      <?php foreach ($disc_types as $val => $label): ?>
        <option value="<?php echo esc_attr($val); ?>" <?php selected($dg_dt, $val); ?>>
          <?php echo esc_html($label); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <!-- Flight number dropdowns -->
  <label>
    <span>Speed</span>
    <select name="dg_sp" onchange="this.form.submit()">
      <option value="">All</option>
      <?php foreach ($speeds as $v): ?>
        <option value="<?php echo esc_attr($v); ?>" <?php selected($dg_sp, (string)$v); ?>>
          <?php echo esc_html($v); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <label>
    <span>Glide</span>
    <select name="dg_gl" onchange="this.form.submit()">
      <option value="">All</option>
      <?php foreach ($glides as $v): ?>
        <option value="<?php echo esc_attr($v); ?>" <?php selected($dg_gl, (string)$v); ?>>
          <?php echo esc_html($v); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <label>
    <span>Turn</span>
    <select name="dg_tn" onchange="this.form.submit()">
      <option value="">All</option>
      <?php foreach ($turns as $v): ?>
        <option value="<?php echo esc_attr($v); ?>" <?php selected($dg_tn, (string)$v); ?>>
          <?php echo esc_html($v); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <label>
    <span>Fade</span>
    <select name="dg_fd" onchange="this.form.submit()">
      <option value="">All</option>
      <?php foreach ($fades as $v): ?>
        <option value="<?php echo esc_attr($v); ?>" <?php selected($dg_fd, (string)$v); ?>>
          <?php echo esc_html($v); ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <label>
    <span>Year (approved)</span>
    <select name="dg_y" onchange="this.form.submit()">
      <option value="0">All</option>
      <?php foreach ($years as $y): ?>
        <option value="<?php echo (int)$y; ?>" <?php selected($dg_y, (int)$y); ?>>
          <?php echo (int)$y; ?>
        </option>
      <?php endforeach; ?>
    </select>
  </label>

  <label>
    <span>Sort</span>
    <select name="dg_sort" onchange="this.form.submit()">
      <option value="newest" <?php selected($dg_sort,'newest'); ?>>Newest → Oldest</option>
      <option value="oldest" <?php selected($dg_sort,'oldest'); ?>>Oldest → Newest</option>
      <option value="az"     <?php selected($dg_sort,'az'); ?>>A–Z</option>
      <option value="za"     <?php selected($dg_sort,'za'); ?>>Z–A</option>
    </select>
  </label>

  <input type="hidden" name="dg_p" value="1" />
</form>
