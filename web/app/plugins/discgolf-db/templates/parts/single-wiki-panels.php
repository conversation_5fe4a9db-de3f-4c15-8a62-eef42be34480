<div class="dgdb-card dgdb-wiki-card">
  <?php if (is_user_logged_in()): ?>
<div id="dgdb-plastics" >
  <h3 class="dgdb-wiki-title">Plastic Type (community)</h3>

  <!-- Approved (read-only) -->
  <div id="dgdb-plastics-approved" class="dgdb-chip-row" aria-live="polite"></div>

  <!-- Picker -->
  <div class="dgdb-plastics-picker">
    <label class="dgdb-label">Add plastics for this disc</label>

    <!-- Dropdown multi-select -->
    <div class="dgdb-mselect" data-open="false">
      <button type="button" id="dgdb-mselect-toggle" class="dgdb-mselect-btn" aria-expanded="false" aria-haspopup="listbox">
        Choose plastics
        <span class="dgdb-mselect-count" id="dgdb-mselect-count"></span>
        <svg class="dgdb-caret" width="14" height="14" viewBox="0 0 20 20" aria-hidden="true"><path d="M5 7l5 5 5-5" fill="none" stroke="currentColor" stroke-width="2"/></svg>
      </button>
      <div id="dgdb-mselect-panel" class="dgdb-mselect-panel" role="listbox" aria-multiselectable="true" hidden></div>
    </div>

    <!-- Add custom -->
    <div class="dgdb-custom-add">
      <input id="dgdb-plastics-input" class="dgdb-combobox-input" type="text" placeholder="Or type a custom plastic…">
      <button type="button" id="dgdb-plastics-add" class="dgdb-btn">Add</button>
    </div>

    <!-- Local selections (to be submitted) -->
    <div id="dgdb-plastics-selected" class="dgdb-chip-row"></div>

    <div class="dgdb-review-actions">
      <button id="dgdb-plastics-save" class="dgdb-btn dgdb-btn-primary" type="button">Submit for moderation</button>
      <span id="dgdb-plastics-status" class="dgdb-wiki-status" aria-live="polite"></span>
    </div>

    <div class="dgdb-subtle">
      Approved plastics appear as chips above. The dropdown excludes already approved or pending items.
    </div>
  </div>
</div>

  <?php endif; ?>
</div>

<div class="dgdb-card dgdb-wiki-card">
  <h3 class="dgdb-wiki-title">Information</h3>
  <div class="dgdb-wiki-info muted">Loading…</div>

  <h3 class="dgdb-wiki-subtitle">History</h3>
  <div class="dgdb-wiki-history muted">Loading…</div>

  <?php if (is_user_logged_in()): ?>
  <form class="dgdb-wiki-form dgdb-wiki-form--text">
    <select name="type" class="dgdb-wiki-select">
      <option value="info">Information</option>
      <option value="history">History</option>
    </select>
    <textarea name="content" rows="4" placeholder="Add your fantastic anecdote…" class="dgdb-wiki-textarea"></textarea>
    <button class="dgdb-btn dgdb-wiki-submit" type="submit">Save Text</button>
    <div class="dgdb-wiki-status--text muted dgdb-wiki-status"></div>
  </form>
  <?php endif; ?>
</div>
