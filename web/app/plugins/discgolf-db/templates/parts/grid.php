<?php
/** @var array<int,array> $rows */
global $wpdb;
?>
<div class="dgdb-grid">
  <?php foreach ($rows as $r):
    // Decode meta (for fallbacks)
    $meta   = $r['meta_json'] ? json_decode($r['meta_json'], true) : [];
    $fields = isset($meta['fields']) && is_array($meta['fields']) ? $meta['fields'] : [];

    // Local image (if any)
    $img_row = $wpdb->get_row($wpdb->prepare(
      "SELECT image_url, source_url FROM {$wpdb->prefix}discgolf_images WHERE disc_id=%d",
      $r['id']
    ));
    $img_url = $img_row->image_url ?? '';
    $versions = dgdb_best_image_versions($img_url);

    // Avatar fallbacks (inline SVG)
    $avatar56  = dgdb_disc_placeholder_svg_markup($r['manufacturer'], $r['name'], 56);
    $avatar220 = dgdb_disc_placeholder_svg_markup($r['manufacturer'], $r['name'], 220);

    // --- Disc type (Distance / Fairway / Midrange / Putter) ---
    // Prefer normalized column speed; fallback to meta_json fields.
    $speed_val = null;
    if (isset($r['speed']) && $r['speed'] !== '' && is_numeric($r['speed'])) {
      $speed_val = (float)$r['speed'];
    } else {
      $speed_raw = $fields['speed'] ?? $fields['Speed'] ?? '';
      if ($speed_raw !== '') {
        $speed_raw = str_replace(',', '.', (string)$speed_raw);
        if (is_numeric($speed_raw)) $speed_val = (float)$speed_raw;
      }
    }

    // If query already provided a computed label (from disc-lists.php), use it.
    $disc_type_label = $r['disc_type_label'] ?? '';
    if ($disc_type_label === '' && $speed_val !== null) {
      if ($speed_val >= 10)      $disc_type_label = 'Distance Driver';
      elseif ($speed_val >= 6)   $disc_type_label = 'Fairway Driver';
      elseif ($speed_val >= 4)   $disc_type_label = 'Midrange';
      elseif ($speed_val >= 0)   $disc_type_label = 'Putter';
    }

    // Optional: a CSS-friendly class if you want per-type colors.
    $disc_type_class = '';
    if ($disc_type_label !== '') {
      $disc_type_class = 'type-' . strtolower(str_replace(' ', '-', $disc_type_label));
    }

    $data = [
      'id'               => (int)$r['id'],
      'name'             => $r['name'],
      'manufacturer'     => $r['manufacturer'],
      'type'             => $r['type'],
      'approval_date'    => $r['approval_date'],
      'max_weight'       => $r['max_weight'],
      'image'            => ($img_url ? ($versions['large'] ?? $img_url) : ''),
      'image_thumb'      => ($img_url ? ($versions['thumb'] ?? $img_url) : ''),
      'placeholder_svg'  => $avatar220,
      'fields'           => $fields,

      // NEW: expose disc type to the card template
      'disc_type_label'  => $disc_type_label,
      'disc_type_class'  => $disc_type_class,
    ];

    include __DIR__ . '/card.php';
  endforeach; ?>
</div>
