<?php
/** @var array $r, $data */
//$avatar56 = dgdb_disc_placeholder_svg_markup($r['manufacturer'], $r['name'], 56);
?>

<div class="dgdb-card"
     data-disc="<?php echo esc_attr( wp_json_encode( $data, JSON_UNESCAPED_UNICODE ) ); ?>">

  <div class="dgdb-card-head dgdb-card-head--split">
    <div class="dgdb-card-avatar">
      <?php if (!empty($data['image_thumb'])): ?>
        <img class="dgdb-thumb" src="<?php echo esc_url($data['image_thumb']); ?>"
             alt="<?php echo esc_attr($r['name']); ?>" loading="lazy" />
      <?php else: ?>
        <div class="dgdb-thumb dgdb-thumb--svg"><?php echo $avatar56; ?></div>
      <?php endif; ?>
    </div>

    <div class="dgdb-card-main">
      <div class="dgdb-card-title">
        <strong><?php echo esc_html($r['name']); ?></strong>
      </div>
      <div class="dgdb-chips">
        <?php if (!empty($data['disc_type_label'])): ?>
              <span class="dgdb-chip dgdb-chip--type <?php echo esc_attr($data['disc_type_class']); ?>">
                <?php echo esc_html($data['disc_type_label']); ?>
              </span>
            <?php endif; ?>
        <span class="dgdb-chip">Manufacturer: <?php echo esc_html($r['manufacturer']); ?></span>
        <?php if (!empty($r['approval_date'])): ?>
          <span class="dgdb-chip">Approved: <?php echo esc_html($r['approval_date']); ?></span>
        <?php endif; ?>
        
      </div>
    </div>
  </div>
</div>
