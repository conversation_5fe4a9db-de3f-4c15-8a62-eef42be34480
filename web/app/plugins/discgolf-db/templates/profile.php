<?php
/**
 * Profile page template (self-contained).
 * - Reads current user's profile meta
 * - Loads their collection from the Collection CPT and resolves disc rows from DB
 * - Renders collection cards inline (avoids stale $disc from partials)
 */

require_once __DIR__ . '/_helpers.php';

use DiscGolfDB\Collection;

if (!is_user_logged_in()) {
    echo '<div class="dgdb-profile-wrap"><p>Log in to view your profile.</p></div>';
    return;
}

$user_id = get_current_user_id();
$user    = get_userdata($user_id);

/** ---------------------------------------------------------
 *  Profile meta
 * --------------------------------------------------------- */
$avatar_id = (int) get_user_meta($user_id, 'dgdb_profile_photo_id', true);
$banner_id = (int) get_user_meta($user_id, 'dgdb_banner_id', true);

$avatar = $avatar_id ? wp_get_attachment_image_url($avatar_id, 'thumbnail') : '';
$banner = $banner_id ? wp_get_attachment_image_url($banner_id, 'large') : '';

$pdga        = get_user_meta($user_id, 'dgdb_pdga_number', true);
$fav_id      = (int) get_user_meta($user_id, 'dgdb_favorite_disc_id', true);
$fav_tx      = (string) get_user_meta($user_id, 'dgdb_favorite_disc_text', true);
$fav_course  = (string) get_user_meta($user_id, 'dgdb_favorite_course', true);

$bag_name    = (string) get_user_meta($user_id, 'dgdb_bag_name', true);
$mini_disc   = (string) get_user_meta($user_id, 'dgdb_mini_disc', true);
$accessories = (string) get_user_meta($user_id, 'dgdb_accessories', true);

$bag_json = get_user_meta($user_id, 'dgdb_bag_json', true);
if (!is_array($bag_json)) $bag_json = [];

/** ---------------------------------------------------------
 *  XP Stats
 * --------------------------------------------------------- */

use DiscGolfDB\Gamification\XP;

// XP state for badge
$xp_state   = XP::get_user_state($user_id);
$rank_label = (string)($xp_state['rank']['label'] ?? 'Rookie');
$level      = (int)($xp_state['level'] ?? 1);
$total_xp   = (int)($xp_state['total_xp'] ?? 0);
$curr_xp    = (int)($xp_state['curr_xp'] ?? 0);          // XP into current level
$next_xp    = max(1, (int)($xp_state['next_xp'] ?? 100)); // XP needed to level up
$bar_pct    = (int)max(0, min(100, round($curr_xp * 100 / $next_xp)));

/** ---------------------------------------------------------
 *  Load collection using new Collection system with personal data
 * --------------------------------------------------------- */
$collection = [];
try {
    $collection_items = Collection::get_user_collection($user_id, 100, 0);

    foreach ($collection_items as $item) {
        // Get default disc image
        global $wpdb;
        $table_imgs = $wpdb->prefix . 'discgolf_images';
        $default_image = $wpdb->get_var($wpdb->prepare(
            "SELECT image_url FROM {$table_imgs} WHERE disc_id = %d LIMIT 1",
            $item['disc_id']
        ));

        // Get custom image if exists
        $custom_image = '';
        if ($item['custom_image_id']) {
            $custom_image = wp_get_attachment_image_url($item['custom_image_id'], 'medium');
            if (!$custom_image) {
                $custom_image = wp_get_attachment_url($item['custom_image_id']);
            }
        }

        $collection[] = [
            'collection_id'     => (int) $item['collection_id'],
            'disc_id'           => (int) $item['disc_id'],
            'manufacturer'      => (string) $item['manufacturer'],
            'name'              => (string) $item['name'],
            'approval_date'     => (string) ($item['approval_date'] ?? ''),
            'image'             => $custom_image ?: $default_image ?: '',
            'image_thumb'       => $custom_image ?: $default_image ?: '',
            'plastic_type'      => (string) ($item['plastic_type'] ?? ''),
            'personal_notes'    => (string) ($item['personal_notes'] ?? ''),
            'custom_fields'     => is_array($item['custom_fields']) ? $item['custom_fields'] : [],
            'has_personal_data' => !empty($item['custom_image_id']) || !empty($item['plastic_type']) ||
                                  !empty($item['personal_notes']) || !empty($item['custom_fields']),
        ];
    }
} catch (\Throwable $e) {
    error_log('DGDB Profile collection load error: ' . $e->getMessage());
}

?>
<div class="dgdb-profile-wrap dgdb-profile-compact">

  <!-- Compact Profile Card -->
  <div class="dgdb-profile-contact-card">

    <!-- Profile Header -->
    <div class="dgdb-profile-header">
      <div class="dgdb-profile-hero">
        <div class="dgdb-profile-avatar-section">
          <div class="dgdb-profile-avatar">
            <img id="dgdb-avatar-img" src="<?php echo esc_url($avatar ?: DGDB_URL.'assets/placeholder-avatar.png'); ?>" alt="Profile Avatar" />
          </div>
        </div>

        <div class="dgdb-profile-info">
          <h1 class="dgdb-profile-name"><?php echo esc_html($user->display_name ?: $user->user_login); ?></h1>
          <div class="dgdb-profile-handle">@<?php echo esc_html($user->user_nicename); ?></div>

          <!-- Quick Stats -->
          <div class="dgdb-profile-stats">
            <?php if ($pdga): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">PDGA</span>
                <span class="dgdb-stat-value">#<?php echo esc_html($pdga); ?></span>
              </div>
            <?php endif; ?>
            <div class="dgdb-stat-item">
              <span class="dgdb-stat-label">Collection</span>
              <span class="dgdb-stat-value"><?php echo count($collection); ?> discs</span>
            </div>
            <?php if ($fav_tx): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">Disc</span>
                <span class="dgdb-stat-value"><?php echo esc_html($fav_tx); ?></span>
              </div>
            <?php endif; ?>
            <?php if ($fav_course): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">Course</span>
                <span class="dgdb-stat-value"><?php echo esc_html($fav_course); ?></span>
              </div>
            <?php endif; ?>
             <?php if ($rank_label): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">Rank</span>
                <span class="dgdb-stat-value"><?php echo esc_html($rank_label); ?></span>
              </div>
            <?php endif; ?>
            <?php if ($level): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">Level</span>
                <span class="dgdb-stat-value"><?php echo esc_html($level); ?></span>
              </div>
            <?php endif; ?>
            <?php if ($total_xp): ?>
              <div class="dgdb-stat-item">
                <span class="dgdb-stat-label">XP</span>
                <span class="dgdb-stat-value"><?php echo esc_html($total_xp); ?></span>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>

      <!-- Banner (smaller, as background) -->
      <?php if ($banner): ?>
        <div class="dgdb-profile-banner" style="background-image: url('<?php echo esc_url($banner); ?>')"></div>
      <?php endif; ?>
    </div>

    <!-- Profile Content Sections -->
    <div class="dgdb-profile-content">

      <!-- Bag Contents Display -->
      <div class="dgdb-bag-display">
        
        <div class="dgdb-bag-summary">
          <h3 class="dgdb-section-title">What's in my bag</h3>
          <?php if (!empty($bag_name) || !empty($mini_disc) || !empty($accessories)): ?>
            <div class="dgdb-bag-basics-display">
              <?php if (!empty($bag_name)): ?>
                <span class="dgdb-bag-item"><strong>Bag:</strong> <?php echo esc_html($bag_name); ?></span>
              <?php endif; ?>
              <?php if (!empty($mini_disc)): ?>
                <span class="dgdb-bag-item"><strong>Mini:</strong> <?php echo esc_html($mini_disc); ?></span>
              <?php endif; ?>
              <?php if (!empty($accessories)): ?>
                <span class="dgdb-bag-item"><strong>Accessories:</strong> <?php echo esc_html($accessories); ?></span>
              <?php endif; ?>
            </div>
          <?php endif; ?>

          <div class="dgdb-bag-contents-table">
            <table class="dgdb-bag-display-table">
              <thead>
                <tr>
                  <th>Type</th>
                  <th>Disc</th>
                  <th>Notes</th>
                  <th>Qty</th>
                </tr>
              </thead>
              <tbody id="dgdb-bag-display-tbody">
                <!-- Content will be populated by JavaScript -->
              </tbody>
            </table>
            <div id="dgdb-bag-empty-state" class="dgdb-bag-empty" style="display: none;">
              <p>No discs in bag yet. Use the editor below to add discs to your bag.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Settings (now collapsible) -->
      <details class="dgdb-profile-section-details" id="dgdb-profile-settings">
        <summary class="dgdb-profile-section-summary">
          <span>Profile Settings</span>
          <span class="dgdb-chevron">›</span>
        </summary>
        <div class="dgdb-profile-section-content">
          <div class="dgdb-profile-card">
            <div class="dgdb-profile-form-grid">
              <div class="dgdb-form-group">
                <label class="dgdb-label">Favorite disc (search or enter)</label>
                <div class="dgdb-suggest">
                  <input id="dgdb-favorite-disc-input" class="dgdb-input" value="<?php echo esc_attr($fav_tx); ?>" placeholder="ex. Destroyer">
                  <input id="dgdb-favorite-disc-id" type="hidden" value="<?php echo (int) $fav_id; ?>">
                  <div id="dgdb-fav-suggest" class="dgdb-suggest-list"></div>
                </div>
              </div>

              <div class="dgdb-form-group">
                <label class="dgdb-label">PDGA number</label>
                <input id="dgdb-pdga" class="dgdb-input" inputmode="numeric" value="<?php echo esc_attr($pdga); ?>">
              </div>

              <div class="dgdb-form-group">
                <label class="dgdb-label">Favorite course</label>
                <input id="dgdb-fav-course" class="dgdb-input" value="<?php echo esc_attr($fav_course); ?>" placeholder="Course name">
              </div>

              <div class="dgdb-form-group">
                <label class="dgdb-label">Avatar</label>
                <input id="dgdb-avatar-input" type="file" accept="image/*" class="dgdb-file-input">
              </div>

              <div class="dgdb-form-group">
                <label class="dgdb-label">Banner</label>
                <input id="dgdb-banner-input" type="file" accept="image/*" class="dgdb-file-input">
              </div>
            </div>
          </div>
        </div>
      </details>

      <!-- Bag Editor -->
      <details class="dgdb-profile-section-details">
        <summary class="dgdb-profile-section-summary">
          <span>Edit what's in my bag</span>
          <span class="dgdb-chevron">›</span>
        </summary>
        <div class="dgdb-profile-section-content">
          <div class="dgdb-bag-basics">
            <div class="dgdb-form-group">
              <label class="dgdb-label">Bag (model)</label>
              <input id="dgdb-bag-name" class="dgdb-input" value="<?php echo esc_attr($bag_name); ?>" placeholder="ex. Grip AX5">
            </div>
            <div class="dgdb-form-group">
              <label class="dgdb-label">Mini disc</label>
              <input id="dgdb-mini" class="dgdb-input" value="<?php echo esc_attr($mini_disc); ?>" placeholder="Mini disc">
            </div>
            <div class="dgdb-form-group">
              <label class="dgdb-label">Accessories</label>
              <input id="dgdb-accessories" class="dgdb-input" value="<?php echo esc_attr($accessories); ?>" placeholder="Towels, rangefinder, chalk bag…">
            </div>
          </div>

          <input id="dgdb-bag-json" type="hidden" value="<?php echo esc_attr(wp_json_encode($bag_json)); ?>">
          <div class="dgdb-bag">
            <?php
            $sections = [
              'distance_driver' => 'Distance Driver',
              'fairway_driver'  => 'Fairway Driver',
              'midrange'        => 'Midrange',
              'approach'        => 'Approach',
              'putter'          => 'Putters',
              'specialty'       => 'Specialty',
            ];
            foreach ($sections as $key => $title): ?>
              <section class="dgdb-bag-sec" data-key="<?php echo esc_attr($key); ?>">
                <h4><?php echo esc_html($title); ?></h4>
                <table class="dgdb-bag-table">
                  <thead>
                    <tr>
                      <th class="dgdb-bag-col-disc">Disc</th>
                      <th>Notes</th>
                      <th class="dgdb-bag-col-qty">Qty</th>
                      <th class="dgdb-bag-col-actions"></th>
                    </tr>
                  </thead>
                  <tbody></tbody>
                </table>
                <div class="dgdb-bag-add-section">
                  <button type="button" class="dgdb-btn dgdb-add">Add disc</button>
                </div>
              </section>
            <?php endforeach; ?>
          </div>
        </div>
      </details>

    </div>

    <!-- Save Actions -->
    <div class="dgdb-profile-actions">
      <button id="dgdb-save-profile" class="dgdb-btn dgdb-btn-primary" type="button">Save Profile</button>
      <span id="dgdb-save-status" class="dgdb-subtle"></span>
    </div>
  </div>

  <!-- Collection Card -->
  <div class="dgdb-profile-card dgdb-collection-card-wrapper">
    <div class="dgdb-collection-header">
      <h3 class="dgdb-card-title">My Collection <span class="dgdb-collection-count">(<?php echo count($collection); ?> discs)</span></h3>

      <!-- Collection Filters -->
      <div class="dgdb-collection-filters">
        <div class="dgdb-filter-group">
          <input type="text" id="dgdb-collection-search" class="dgdb-filter-input" placeholder="Search discs...">
          <svg class="dgdb-search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
        </div>

        <select id="dgdb-collection-manufacturer" class="dgdb-filter-select">
          <option value="">All Brands</option>
          <?php
          $manufacturers = array_unique(array_column($collection, 'manufacturer'));
          sort($manufacturers);
          foreach ($manufacturers as $manufacturer):
          ?>
            <option value="<?php echo esc_attr($manufacturer); ?>"><?php echo esc_html($manufacturer); ?></option>
          <?php endforeach; ?>
        </select>

        <select id="dgdb-collection-sort" class="dgdb-filter-select">
          <option value="newest">Newest First</option>
          <option value="oldest">Oldest First</option>
          <option value="az">A → Z</option>
          <option value="za">Z → A</option>
          <option value="manufacturer">By Brand</option>
          <option value="favorites">Favorites First</option>
        </select>

        <button type="button" id="dgdb-toggle-favorites" class="dgdb-filter-btn" title="Show only favorites">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
          </svg>
        </button>
      </div>
    </div>
    <?php if (!empty($collection)) : ?>
      <div class="dgdb-grid dgdb-collection-grid">
        <?php foreach ($collection as $disc_row):
          $collection_id = (int) $disc_row['collection_id'];
          $disc_json = [
            'collection_id' => $collection_id,
            'disc_id'       => (int) $disc_row['disc_id'],
            'manufacturer'  => (string) $disc_row['manufacturer'],
            'name'          => (string) $disc_row['name'],
            'approval_date' => (string) ($disc_row['approval_date'] ?? ''),
            'image'         => (string) ($disc_row['image'] ?? ''),
            'image_thumb'   => (string) ($disc_row['image_thumb'] ?? ''),
            'plastic_type'  => (string) ($disc_row['plastic_type'] ?? ''),
            'weight'        => (int) ($disc_row['weight'] ?? 0),
            'personal_notes' => (string) ($disc_row['personal_notes'] ?? ''),
            'custom_fields' => $disc_row['custom_fields'],
            'has_personal_data' => (bool) $disc_row['has_personal_data'],
          ];
          $data_attr = esc_attr(wp_json_encode($disc_json));
          $title     = esc_html($disc_row['name']);
          $maker     = esc_html($disc_row['manufacturer']);
          $dateTx    = $disc_row['approval_date'] ? 'Approved: ' . esc_html($disc_row['approval_date']) : '';
          $thumb     = $disc_row['image_thumb'] ?: $disc_row['image'];
          $has_personal = $disc_row['has_personal_data'];
        ?>
          <div class="dgdb-card dgdb-collection-card <?php echo $has_personal ? 'dgdb-card--personalized' : ''; ?>"
               data-disc="<?php echo $data_attr; ?>"
               data-collection-id="<?php echo $collection_id; ?>">

            <!-- Favorite Indicator -->
            <div class="dgdb-favorite-indicator" style="display: none;">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
              </svg>
            </div>

            <!-- Collection Management Controls -->
            <div class="dgdb-collection-controls">
              <button type="button" class="dgdb-btn-icon dgdb-favorite-btn" title="Add to favorites" data-collection-id="<?php echo $collection_id; ?>">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
                </svg>
              </button>
              <button type="button" class="dgdb-btn-icon dgdb-duplicate-btn" title="Duplicate disc" data-collection-id="<?php echo $collection_id; ?>">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                </svg>
              </button>
              <button type="button" class="dgdb-btn-icon dgdb-delete-btn" title="Delete disc" data-collection-id="<?php echo $collection_id; ?>">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"></polyline>
                  <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
                </svg>
              </button>
            </div>

            <div class="dgdb-card-head dgdb-card-head--split">
              <div class="dgdb-card-avatar">
                <?php if ($thumb): ?>
                  <img class="dgdb-thumb" src="<?php echo esc_url($thumb); ?>" alt="" />
                <?php else: ?>
                  <div class="dgdb-thumb dgdb-thumb--svg dgdb-thumb--centered" aria-hidden="true">
                    <?php echo dgdb_disc_placeholder_svg_markup($maker, $title, 56); ?>
                  </div>
                <?php endif; ?>
              </div>
              <div class="dgdb-card-main">
                <div class="dgdb-card-title">
                  <strong><?php echo $title; ?></strong>
                  <span class="muted dgdb-card-subtitle"><?php echo $maker; ?></span>
                </div>

                <div class="dgdb-chips">
                  <?php if ($disc_row['plastic_type']): ?>
                    <span class="dgdb-chip dgdb-chip--plastic"><?php echo esc_html($disc_row['plastic_type']); ?></span>
                  <?php endif; ?>
                  <?php if (!empty($disc_row['weight']) && $disc_row['weight'] > 0): ?>
                    <span class="dgdb-chip dgdb-chip--weight"><?php echo esc_html($disc_row['weight']); ?>g</span>
                  <?php endif; ?>
                  <?php if ($dateTx): ?>
                    <span class="dgdb-chip"><?php echo esc_html($dateTx); ?></span>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          </div>
        <?php endforeach; ?>
      </div>
    <?php else: ?>
      <div class="dgdb-empty-state">
        <p>No discs in your collection yet.</p>
        <p class="dgdb-subtle">Start building your collection by browsing discs and adding them!</p>
      </div>
    <?php endif; ?>
  </div>

  <!-- Personal Disc Information Modal -->
<div id="dgdb-personal-disc-modal" class="dgdb-modal" style="display: none;">
  <div class="dgdb-modal-backdrop"></div>
  <div class="dgdb-modal-content">
    <div class="dgdb-modal-header">
      <h3 id="dgdb-modal-title">Personal Disc Information</h3>
      <button type="button" class="dgdb-modal-close" aria-label="Close">&times;</button>
    </div>

    <div class="dgdb-modal-body">
      <!-- View Mode -->
      <div id="dgdb-personal-view" class="dgdb-personal-mode">
        <div class="dgdb-personal-display">
          <div class="dgdb-personal-image-display">
            <img id="dgdb-display-image" src="" alt="Personal disc image" style="display: none;">
            <div id="dgdb-display-placeholder" class="dgdb-image-placeholder">No custom image</div>
          </div>
          <div class="dgdb-personal-info-display">
            <div class="dgdb-info-row">
              <strong>Plastic Type:</strong>
              <span id="dgdb-display-plastic">-</span>
            </div>
            <div class="dgdb-info-row">
              <strong>Weight:</strong>
              <span id="dgdb-display-weight">-</span>
            </div>
            <div class="dgdb-info-row">
              <strong>Notes:</strong>
              <div id="dgdb-display-notes">-</div>
            </div>
            <div class="dgdb-info-row" id="dgdb-display-custom-field" style="display: none;">
              <strong id="dgdb-display-custom-name">Custom Field:</strong>
              <span id="dgdb-display-custom-value">-</span>
            </div>
          </div>
        </div>
        <div class="dgdb-modal-actions">
          <button type="button" id="dgdb-edit-personal-btn" class="dgdb-btn dgdb-btn-primary">Edit Information</button>
        </div>
      </div>

      <!-- Edit Mode -->
      <div id="dgdb-personal-edit" class="dgdb-personal-mode" style="display: none;">
        <form id="dgdb-personal-form">
          <input type="hidden" id="dgdb-form-collection-id" name="collection_id" value="">

          <div class="dgdb-form-group">
            <label class="dgdb-label">Custom Image</label>
            <div class="dgdb-image-upload-area">
              <div id="dgdb-current-image" class="dgdb-current-image" style="display: none;">
                <img id="dgdb-form-image-preview" src="" alt="Current image">
                <button type="button" id="dgdb-remove-image" class="dgdb-btn-icon dgdb-remove-image-btn" title="Remove image">×</button>
              </div>
              <input type="file" id="dgdb-image-upload" accept="image/*" class="dgdb-file-input">
              <input type="hidden" id="dgdb-custom-image-id" name="custom_image_id" value="">
              <div class="dgdb-upload-hint">Upload a custom image for this disc</div>
            </div>
          </div>

          <div class="dgdb-form-group">
            <label class="dgdb-label" for="dgdb-plastic-type">Plastic Type</label>
            <input type="text" id="dgdb-plastic-type" name="plastic_type" class="dgdb-input" placeholder="e.g., Champion, Star, DX">
          </div>

          <div class="dgdb-form-group">
            <label class="dgdb-label" for="dgdb-weight">Weight (grams)</label>
            <input type="number" id="dgdb-weight" name="weight" class="dgdb-input" placeholder="e.g., 175" min="100" max="200" step="1">
          </div>

          <div class="dgdb-form-group">
            <label class="dgdb-label" for="dgdb-personal-notes">Personal Notes</label>
            <textarea id="dgdb-personal-notes" name="personal_notes" class="dgdb-textarea" rows="3" placeholder="Your personal notes about this disc..."></textarea>
          </div>

          <div class="dgdb-form-group">
            <div class="dgdb-custom-fields-header">
              <label class="dgdb-label">Custom Fields</label>
              <button type="button" id="dgdb-add-custom-field" class="dgdb-btn-icon dgdb-add-field-btn" title="Add custom field">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
            </div>
            <div id="dgdb-custom-fields-container"></div>
          </div>
        </form>

        <div class="dgdb-modal-actions">
          <button type="button" id="dgdb-save-personal-btn" class="dgdb-btn dgdb-btn-primary">Save Information</button>
          <button type="button" id="dgdb-cancel-edit-btn" class="dgdb-btn">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>

</div>

<script>
// Include collection management functionality
<?php echo file_get_contents(__DIR__ . '/../assets/collection-management.js'); ?>
</script>

<script>
// Initialize DGDB globals for AJAX
window.dgdb_nonce = '<?php echo wp_create_nonce('dgdb'); ?>';
window.ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
</script>
