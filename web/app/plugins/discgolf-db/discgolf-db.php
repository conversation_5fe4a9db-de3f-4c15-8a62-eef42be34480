<?php
/**
 * Plugin Name: DiscGolf Discs DB
 * Description: Show and manage disc golf discs with images, reviews, filters, sorting, pagination & a wiki-style single disc page.
 * Version: 1.7.0
 * Author: <PERSON>
 * Requires PHP: 7.4
 * Text Domain: discgolf-db
 */

defined('ABSPATH') || exit;

define('DGDB_VERSION', '1.7.0');
define('DGDB_PATH', plugin_dir_path(__FILE__));
define('DGDB_URL',  plugin_dir_url(__FILE__));

/** Core includes */
require_once DGDB_PATH . 'includes/DB.php';
require_once DGDB_PATH . 'includes/Utils.php';
require_once DGDB_PATH . 'includes/Importer.php';
require_once DGDB_PATH . 'includes/Collection.php';

/** New Core System */
require_once DGDB_PATH . 'includes/Core/ErrorHandler.php';
require_once DGDB_PATH . 'includes/Core/Cache.php';
require_once DGDB_PATH . 'includes/Core/AssetManager.php';
require_once DGDB_PATH . 'includes/Core/FileUpload.php';
require_once DGDB_PATH . 'includes/Core/FileUploadValidator.php';
require_once DGDB_PATH . 'includes/Core/InputValidator.php';
require_once DGDB_PATH . 'includes/Core/Loader.php';
require_once DGDB_PATH . 'includes/Core/Security.php';
require_once DGDB_PATH . 'includes/Core/QueryCache.php';
require_once DGDB_PATH . 'includes/Core/BaseAjaxHandler.php';
require_once DGDB_PATH . 'includes/Core/ValidationTest.php';

/** AJAX Handlers (after core system) */
require_once DGDB_PATH . 'includes/Ajax/Collection.php';
require_once DGDB_PATH . 'includes/Ajax/Reviews.php';

/** Legacy Assets (for backwards compatibility) */
require_once DGDB_PATH . 'includes/Assets.php';

/** Admin & Ajax */
require_once DGDB_PATH . 'includes/Admin/Page.php';
require_once DGDB_PATH . 'includes/Ajax/Images.php';
require_once DGDB_PATH . 'includes/Admin/BrandPlastics.php';

/** Shortcode + Single page */
require_once DGDB_PATH . 'includes/Shortcodes/Discs.php';
require_once DGDB_PATH . 'includes/SingleDiscRouter.php';

require_once DGDB_PATH . 'includes/Ajax/Debug.php';

require_once DGDB_PATH . 'includes/WikiAjax.php';
require_once DGDB_PATH . 'includes/Admin/WikiModeration.php';

require_once DGDB_PATH . 'includes/CollectionCPT.php';
require_once DGDB_PATH . 'includes/ProfileRouter.php';
require_once DGDB_PATH . 'includes/Shortcodes/Register.php';
require_once DGDB_PATH . 'includes/Ajax/Plastics.php';

require_once DGDB_PATH . 'includes/Gamification/XP.php';
require_once DGDB_PATH . 'includes/Admin/Gamification.php';
require_once DGDB_PATH . 'includes/Ajax/Gamification.php';
require_once DGDB_PATH . 'includes/Frontend/GamificationHUD.php';
require_once DGDB_PATH . 'includes/Frontend/Onboarding.php';
require_once DGDB_PATH . 'includes/Ajax/Tutorial.php';

require_once DGDB_PATH . 'includes/Gamification/Referrals.php';



/** Activation / deactivation */
register_activation_hook(__FILE__, function () {
    // DB & cron
    \DiscGolfDB\DB::activate();
    // Add our rewrite slugs and flush rules so /disc/{id} works immediately
    \DiscGolfDB\SingleDiscRouter::add_rewrite();
    flush_rewrite_rules();
});
register_deactivation_hook(__FILE__, function () {
    \DiscGolfDB\DB::deactivate();
    flush_rewrite_rules();
});

/** Register shortcodes immediately when plugin loads */
\DiscGolfDB\Shortcodes\Discs::register();
\DiscGolfDB\Shortcodes\Register::register();

// Ensure shortcodes are processed in content
add_filter('the_content', 'do_shortcode', 11);

/** Bootstrap with new lazy loading system */
add_action('plugins_loaded', function () {
    \DiscGolfDB\DB::heal();
    \DiscGolfDB\Core\ErrorHandler::init();
    \DiscGolfDB\Core\AssetManager::init();
    \DiscGolfDB\Core\Loader::init();
    \DiscGolfDB\SingleDiscRouter::register();
    \DiscGolfDB\ProfileRouter::register();
    \DiscGolfDB\WikiAjax::register();
    \DiscGolfDB\CollectionCPT::register();
    \DiscGolfDB\Ajax\CollectionAjax::register();
    \DiscGolfDB\Ajax\Reviews::register();
    \DiscGolfDB\Ajax\Images::register();
    \DiscGolfDB\Ajax\Debug::register();
    \DiscGolfDB\Admin\BrandPlastics::register();
    \DiscGolfDB\Ajax\Plastics::register();
    \DiscGolfDB\Gamification\XP::register();
    \DiscGolfDB\Admin\Gamification::register();
    \DiscGolfDB\Ajax\Gamification::register();
    \DiscGolfDB\Frontend\GamificationHUD::register();
    \DiscGolfDB\Gamification\Referrals::register();
    \DiscGolfDB\Frontend\Onboarding::register();
    \DiscGolfDB\Ajax\Tutorial::register();
});

/** Register admin pages */
add_action('admin_menu', function() {
    add_menu_page(
        'Discs DB',
        'Discs DB',
        'manage_options',
        'discgolf-db',
        [\DiscGolfDB\Admin\Page::class, 'render'],
        'dashicons-database',
        58
    );

    add_submenu_page(
        'discgolf-db',
        'Disc Wiki – Moderation',
        'Disc Wiki – Moderation',
        'manage_options',
        'dgdb-wiki-moderation',
        [\DiscGolfDB\Admin\WikiModeration::class, 'render']
    );
});
