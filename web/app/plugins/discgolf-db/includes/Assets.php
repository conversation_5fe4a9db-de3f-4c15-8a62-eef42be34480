<?php
namespace DiscGolfDB;

use DiscGolfDB\Core\AssetManager;

defined('ABSPATH') || exit;

/**
 * Legacy Assets class - now delegates to AssetManager
 * Kept for backwards compatibility
 */
class Assets {
    public static function register(): void {
        AssetManager::init();
    }

    /** @deprecated Use AssetManager::register_assets() */
    public static function register_front(): void {
        // Delegate to new AssetManager
        AssetManager::register_assets();
    }

    /** @deprecated Use AssetManager methods */
    public static function enqueue_for_shortcode(): void {
        AssetManager::load_for_shortcode();
    }

    /** @deprecated Use AssetManager::load_for_single_disc() or load_for_profile() */
    public static function enqueue_for_single_or_profile(array $overrides = []): void {
        // Try to determine context and delegate appropriately
        if (self::is_single_disc_context()) {
            AssetManager::load_for_single_disc($overrides);
        } else {
            AssetManager::load_for_profile($overrides);
        }
    }

    /** @deprecated Use AssetManager::load_for_profile() */
    public static function enqueue_profile_js(array $overrides = []): void {
        AssetManager::load_for_profile($overrides);
    }

    private static function is_single_disc_context(): bool {
        return get_query_var('dgdb_disc_id') || is_page('disc');
    }
}
