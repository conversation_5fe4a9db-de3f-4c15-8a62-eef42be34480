<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

final class DB {
    /** Option key to store current DB version */
    public const OPT_DB_VER = 'dgdb_db_version';
    /** Bump to force heal() on update */
    public const DB_VER     = 14;

    /** In-request caches to reduce SHOW/INFORMATION_SCHEMA I/O */
    private static array $colCache  = []; // [table => [col => true]]
    private static array $idxCache  = []; // [table => [index => true]]
    private static bool  $upgraderLoaded = false;

    /* -----------------------------
     * Public lifecycle methods
     * ----------------------------- */

    public static function activate(): void {
        try {
            self::install();
            self::heal();
            update_option(self::OPT_DB_VER, self::DB_VER, false);
        } catch (\Throwable $e) {
            error_log('DGDB Activation Error: ' . $e->getMessage());
        }
    }

    public static function deactivate(): void {
        $ts = wp_next_scheduled('dgdb_fetch_images_cron');
        if ($ts) {
            wp_unschedule_event($ts, 'dgdb_fetch_images_cron');
        }
    }

    /* -----------------------------
     * Schema (create/upgrade)
     * ----------------------------- */

    public static function install(): void {
        global $wpdb;

        // Load upgrader functions (safe even if we don't use dbDelta directly)
        if (!self::$upgraderLoaded) {
            if (!function_exists('maybe_convert_table_to_utf8mb4')) {
                require_once ABSPATH . 'wp-admin/includes/upgrade.php';
            }
            self::$upgraderLoaded = true;
        }

        $charset = $wpdb->get_charset_collate();

        // Discs
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_discs` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                manufacturer VARCHAR(191),
                name VARCHAR(191),
                type VARCHAR(50),
                approval_year SMALLINT NULL,
                approval_date DATE NULL,
                max_weight DECIMAL(5,1) NULL,
                speed DECIMAL(6,2) NULL,
                glide DECIMAL(6,2) NULL,
                turn  DECIMAL(6,2) NULL,
                fade  DECIMAL(6,2) NULL,
                disc_type ENUM('distance','fairway','midrange','putter') NULL,
                meta_json LONGTEXT NULL,
                info_html LONGTEXT NULL,
                history_html LONGTEXT NULL,
                UNIQUE KEY `uniq_disc` (`manufacturer`, `name`),
                KEY `idx_manu` (`manufacturer`),
                KEY `idx_name` (`name`),
                KEY `idx_year` (`approval_year`),
                KEY `idx_speed` (`speed`),
                KEY `idx_glide` (`glide`),
                KEY `idx_turn`  (`turn`),
                KEY `idx_fade`  (`fade`),
                KEY `idx_disc_type` (`disc_type`)
            ) $charset
        ");

        // Reviews
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_reviews` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT UNSIGNED NOT NULL,
                disc_id INT NOT NULL,
                rating TINYINT NOT NULL,
                title VARCHAR(120) NULL,
                review TEXT,
                speed DECIMAL(6,2) NULL,
                glide DECIMAL(6,2) NULL,
                turn  DECIMAL(6,2) NULL,
                fade  DECIMAL(6,2) NULL,
                plastic_type VARCHAR(120) NULL,
                weight SMALLINT UNSIGNED NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                KEY `idx_disc` (`disc_id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_created_at` (`created_at`),
                KEY `idx_user_disc` (`user_id`, `disc_id`),
                KEY `idx_disc_created` (`disc_id`, `created_at`)
            ) $charset
        ");

        // Images
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_images` (
                id INT AUTO_INCREMENT PRIMARY KEY,
                disc_id INT NOT NULL,
                user_id BIGINT UNSIGNED NULL,
                attachment_id BIGINT UNSIGNED NULL,
                image_url TEXT NOT NULL,
                source_url TEXT NULL,
                license VARCHAR(80) NOT NULL DEFAULT 'Unknown',
                attribution_text VARCHAR(255) NULL,
                attribution_url  VARCHAR(500) NULL,
                status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'approved',
                sort_order TINYINT UNSIGNED NOT NULL DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                KEY `idx_disc` (`disc_id`),
                KEY `idx_user` (`user_id`),
                KEY `idx_disc_status` (`disc_id`, `status`),
                KEY `idx_sort` (`disc_id`, `sort_order`),
                KEY `idx_created_at` (`created_at`)
            ) $charset
        ");

        // Community: Flight properties (proposals)
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_flight_props` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                disc_id INT NOT NULL,
                speed DECIMAL(4,1) NULL,
                glide DECIMAL(4,1) NULL,
                turn DECIMAL(4,1) NULL,
                fade DECIMAL(4,1) NULL,
                source VARCHAR(255) NULL,
                status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending',
                created_by BIGINT UNSIGNED NULL,
                approved_by BIGINT UNSIGNED NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME NULL,
                KEY `idx_disc_status` (`disc_id`, `status`),
                KEY `idx_created` (`created_at`)
            ) $charset
        ");

        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_flight_props_active` (
                disc_id INT NOT NULL PRIMARY KEY,
                flight_props_id BIGINT UNSIGNED NOT NULL
            ) $charset
        ");

        // Plastics (wiki-style)
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_plastics` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                disc_id INT NOT NULL,
                plastic_name VARCHAR(120) NOT NULL,
                notes VARCHAR(255) NULL,
                source VARCHAR(255) NULL,
                status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending',
                created_by BIGINT UNSIGNED NULL,
                approved_by BIGINT UNSIGNED NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME NULL,
                KEY `idx_disc_status` (`disc_id`, `status`),
                KEY `idx_plastic` (`plastic_name`)
            ) $charset
        ");

        // Optional: votes on plastics
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_plastic_votes` (
                id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                disc_id BIGINT UNSIGNED NOT NULL,
                plastic_id BIGINT UNSIGNED NOT NULL,
                user_id BIGINT UNSIGNED NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `uniq_vote` (`disc_id`,`plastic_id`,`user_id`),
                KEY `disc_idx` (`disc_id`),
                KEY `plastic_idx` (`plastic_id`),
                KEY `user_idx` (`user_id`)
            ) $charset
        ");

        // Lightweight disc -> plastic names
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_disc_plastics` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                disc_id BIGINT UNSIGNED NOT NULL,
                name VARCHAR(191) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                source VARCHAR(20) NOT NULL DEFAULT 'user',
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                KEY `disc_id` (`disc_id`),
                KEY `status` (`status`),
                KEY `name` (`name`)
            ) $charset
        ");

        // Text suggestions
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_text_suggestions` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                disc_id INT NOT NULL,
                type ENUM('info','history') NOT NULL,
                content MEDIUMTEXT NOT NULL,
                status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending',
                created_by BIGINT UNSIGNED NULL,
                approved_by BIGINT UNSIGNED NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                approved_at DATETIME NULL,
                KEY `idx_disc_type` (`disc_id`, `type`),
                KEY `idx_disc_status` (`disc_id`, `status`),
                KEY `idx_created` (`created_at`)
            ) $charset
        ");

        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_text_active` (
                disc_id INT NOT NULL,
                type ENUM('info','history') NOT NULL,
                suggestion_id BIGINT UNSIGNED NOT NULL,
                PRIMARY KEY (`disc_id`, `type`)
            ) $charset
        ");

        // User collections
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_user_collection` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                user_id BIGINT UNSIGNED NOT NULL,
                disc_id INT NOT NULL,
                notes TEXT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                KEY `idx_user` (`user_id`),
                KEY `idx_disc` (`disc_id`),
                KEY `idx_user_disc` (`user_id`, `disc_id`)
            ) $charset
        ");

        // Personal disc info
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_personal_discs` (
                id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                collection_id BIGINT UNSIGNED NOT NULL,
                custom_image_id BIGINT UNSIGNED NULL,
                plastic_type VARCHAR(120) NULL,
                weight SMALLINT UNSIGNED NULL,
                personal_notes TEXT NULL,
                custom_fields JSON NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY `uniq_collection` (`collection_id`),
                KEY `idx_collection` (`collection_id`)
            ) $charset
        ");

        // Gamification (XP) — NOTE: source is VARCHAR(32) (future-proof)
        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_xp_ledger` (
                id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                user_id BIGINT UNSIGNED NOT NULL,
                action VARCHAR(64) NOT NULL,
                source VARCHAR(32) NOT NULL,
                source_ref BIGINT UNSIGNED NULL,
                delta INT NOT NULL,
                meta LONGTEXT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_idx` (`user_id`),
                KEY `src_idx` (`source`),
                KEY `created_idx` (`created_at`),
                KEY `user_created_idx` (`user_id`, `created_at`),
                KEY `user_source_idx` (`user_id`, `source`)
            ) $charset
        ");

        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_user_xp` (
                user_id BIGINT UNSIGNED NOT NULL PRIMARY KEY,
                total_xp BIGINT NOT NULL DEFAULT 0,
                level INT NOT NULL DEFAULT 1,
                next_level_xp INT NOT NULL DEFAULT 100,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) $charset
        ");

        self::run("
            CREATE TABLE IF NOT EXISTS `{$wpdb->prefix}discgolf_ranks` (
                id INT NOT NULL PRIMARY KEY,
                slug VARCHAR(64) NOT NULL,
                label VARCHAR(64) NOT NULL,
                min_xp INT NOT NULL,
                perks JSON NULL,
                sort INT NOT NULL
            ) $charset
        ");
    }

    public static function heal(): void {
        global $wpdb;

        /* ---- discs ---- */
        $t = $wpdb->prefix . 'discgolf_discs';
        if (self::table_exists($t)) {
            $cols = self::get_columns($t);

            if (!isset($cols['approval_year'])) self::run("ALTER TABLE `{$t}` ADD COLUMN `approval_year` SMALLINT NULL AFTER `type`");
            if (!isset($cols['approval_date'])) self::run("ALTER TABLE `{$t}` ADD COLUMN `approval_date` DATE NULL AFTER `approval_year`");
            if (!isset($cols['max_weight']))   self::run("ALTER TABLE `{$t}` ADD COLUMN `max_weight` DECIMAL(5,1) NULL AFTER `approval_date`");

            if (!isset($cols['speed'])) self::run("ALTER TABLE `{$t}` ADD COLUMN `speed` DECIMAL(6,2) NULL AFTER `max_weight`");
            if (!isset($cols['glide'])) self::run("ALTER TABLE `{$t}` ADD COLUMN `glide` DECIMAL(6,2) NULL AFTER `speed`");
            if (!isset($cols['turn']))  self::run("ALTER TABLE `{$t}` ADD COLUMN `turn`  DECIMAL(6,2) NULL AFTER `glide`");
            if (!isset($cols['fade']))  self::run("ALTER TABLE `{$t}` ADD COLUMN `fade`  DECIMAL(6,2) NULL AFTER `turn`");

            if (!isset($cols['disc_type']))   self::run("ALTER TABLE `{$t}` ADD COLUMN `disc_type` ENUM('distance','fairway','midrange','putter') NULL AFTER `fade`");
            if (!isset($cols['meta_json']))   self::run("ALTER TABLE `{$t}` ADD COLUMN `meta_json` LONGTEXT NULL AFTER `disc_type`");
            if (!isset($cols['info_html']))   self::run("ALTER TABLE `{$t}` ADD COLUMN `info_html` LONGTEXT NULL AFTER `meta_json`");
            if (!isset($cols['history_html'])) self::run("ALTER TABLE `{$t}` ADD COLUMN `history_html` LONGTEXT NULL AFTER `info_html`");

            self::ensure_index($t, 'idx_manu',      'manufacturer');
            self::ensure_index($t, 'idx_name',      'name');
            self::ensure_index($t, 'idx_year',      'approval_year');
            self::ensure_index($t, 'idx_speed',     'speed');
            self::ensure_index($t, 'idx_glide',     'glide');
            self::ensure_index($t, 'idx_turn',      'turn');
            self::ensure_index($t, 'idx_fade',      'fade');
            self::ensure_index($t, 'idx_disc_type', 'disc_type');

            // Add missing performance indexes
            self::ensure_index($t, 'idx_created_at', 'created_at');
            self::ensure_index($t, 'idx_manu_name', 'manufacturer, name');

            if (!get_option('dgdb_flight_backfilled')) {
                self::run("
                    UPDATE `{$t}`
                    SET
                      speed = CAST(JSON_UNQUOTE(JSON_EXTRACT(meta_json,'$.fields.speed')) AS DECIMAL(6,2)),
                      glide = CAST(JSON_UNQUOTE(JSON_EXTRACT(meta_json,'$.fields.glide')) AS DECIMAL(6,2)),
                      turn  = CAST(JSON_UNQUOTE(JSON_EXTRACT(meta_json,'$.fields.turn'))  AS DECIMAL(6,2)),
                      fade  = CAST(JSON_UNQUOTE(JSON_EXTRACT(meta_json,'$.fields.fade'))  AS DECIMAL(6,2))
                    WHERE (speed IS NULL OR glide IS NULL OR turn IS NULL OR fade IS NULL)
                ");
                update_option('dgdb_flight_backfilled', 1, false);
            }

            // backfill disc_type from speed
            self::run("
                UPDATE `{$t}`
                SET disc_type = CASE
                    WHEN speed IS NULL THEN NULL
                    WHEN speed >= 10 THEN 'distance'
                    WHEN speed >= 6  THEN 'fairway'
                    WHEN speed >= 4  THEN 'midrange'
                    WHEN speed >= 0  THEN 'putter'
                    ELSE NULL
                END
                WHERE disc_type IS NULL
                   OR (
                        (speed IS NULL AND disc_type IS NOT NULL) OR
                        (speed >= 10 AND disc_type <> 'distance') OR
                        (speed >= 6 AND speed < 10 AND disc_type <> 'fairway') OR
                        (speed >= 4 AND speed < 6  AND disc_type <> 'midrange') OR
                        (speed >= 0 AND speed < 4  AND disc_type <> 'putter')
                      )
            ");
        }

        /* ---- images ---- */
        $ti = $wpdb->prefix . 'discgolf_images';
        if (self::table_exists($ti)) {
            $icols = self::get_columns($ti);

            // Drop legacy unique if present
            $idx = self::get_indexes($ti);
            if (!empty($idx['uniq_disc_image'])) self::run("ALTER TABLE `{$ti}` DROP INDEX `uniq_disc_image`");

            if (!isset($icols['user_id']))          self::run("ALTER TABLE `{$ti}` ADD COLUMN `user_id` BIGINT UNSIGNED NULL AFTER `disc_id`");
            // Standardize existing user_id column if it exists but has wrong type
            self::standardize_user_id_column($ti, 'user_id');

            if (!isset($icols['attachment_id']))    self::run("ALTER TABLE `{$ti}` ADD COLUMN `attachment_id` BIGINT UNSIGNED NULL AFTER `user_id`");
            if (!isset($icols['source_url']))       self::run("ALTER TABLE `{$ti}` ADD COLUMN `source_url` TEXT NULL AFTER `image_url`");
            if (!isset($icols['license']))          self::run("ALTER TABLE `{$ti}` ADD COLUMN `license` VARCHAR(80) NOT NULL DEFAULT 'Unknown' AFTER `source_url`");
            if (!isset($icols['attribution_text'])) self::run("ALTER TABLE `{$ti}` ADD COLUMN `attribution_text` VARCHAR(255) NULL AFTER `license`");
            if (!isset($icols['attribution_url']))  self::run("ALTER TABLE `{$ti}` ADD COLUMN `attribution_url` VARCHAR(500) NULL AFTER `attribution_text`");
            if (!isset($icols['status']))           self::run("ALTER TABLE `{$ti}` ADD COLUMN `status` ENUM('pending','approved','rejected') NOT NULL DEFAULT 'approved' AFTER `attribution_url`");
            if (!isset($icols['sort_order']))       self::run("ALTER TABLE `{$ti}` ADD COLUMN `sort_order` TINYINT UNSIGNED NOT NULL DEFAULT 0 AFTER `status`");
            if (!isset($icols['created_at']))       self::run("ALTER TABLE `{$ti}` ADD COLUMN `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP AFTER `sort_order`");

            self::ensure_index($ti, 'idx_disc',        'disc_id');
            self::ensure_index($ti, 'idx_disc_status', 'disc_id, status');
            self::ensure_index($ti, 'idx_sort',        'disc_id, sort_order');
            self::ensure_index($ti, 'idx_user',        'user_id');
            self::ensure_index($ti, 'idx_created_at',  'created_at');
        }

        /* ---- reviews ---- */
        $tr = $wpdb->prefix . 'discgolf_reviews';
        if (self::table_exists($tr)) {
            $rcols = self::get_columns($tr);

            // Standardize user_id to BIGINT UNSIGNED for consistency
            self::standardize_user_id_column($tr, 'user_id');

            if (!isset($rcols['title']))        self::run("ALTER TABLE `{$tr}` ADD COLUMN `title` VARCHAR(120) NULL AFTER `rating`");
            if (!isset($rcols['speed']))        self::run("ALTER TABLE `{$tr}` ADD COLUMN `speed` DECIMAL(6,2) NULL AFTER `review`");
            if (!isset($rcols['glide']))        self::run("ALTER TABLE `{$tr}` ADD COLUMN `glide` DECIMAL(6,2) NULL AFTER `speed`");
            if (!isset($rcols['turn']))         self::run("ALTER TABLE `{$tr}` ADD COLUMN `turn`  DECIMAL(6,2) NULL AFTER `glide`");
            if (!isset($rcols['fade']))         self::run("ALTER TABLE `{$tr}` ADD COLUMN `fade`  DECIMAL(6,2) NULL AFTER `turn`");
            if (!isset($rcols['plastic_type'])) self::run("ALTER TABLE `{$tr}` ADD COLUMN `plastic_type` VARCHAR(120) NULL AFTER `fade`");
            if (!isset($rcols['weight']))       self::run("ALTER TABLE `{$tr}` ADD COLUMN `weight` SMALLINT UNSIGNED NULL AFTER `plastic_type`");
            if (!isset($rcols['created_at']))   self::run("ALTER TABLE `{$tr}` ADD COLUMN `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP AFTER `weight`");

            self::ensure_index($tr, 'idx_disc',       'disc_id');
            self::ensure_index($tr, 'idx_user',       'user_id');
            self::ensure_index($tr, 'idx_created_at', 'created_at');
            // Add composite indexes for better query performance
            self::ensure_index($tr, 'idx_disc_created', 'disc_id, created_at');
            self::ensure_index($tr, 'idx_user_created', 'user_id, created_at');

            // Drop any legacy unique indexes (keep behavior)
            foreach (self::get_index_rows($tr) as $row) {
                $key  = (string)($row['Key_name'] ?? '');
                $uniq = ((int)($row['Non_unique'] ?? 1) === 0);
                if ($key && $key !== 'PRIMARY' && $uniq) {
                    self::run("ALTER TABLE `{$tr}` DROP INDEX `{$key}`");
                }
            }
        }

        /* ---- flight_props ---- */
        $tf = $wpdb->prefix . 'discgolf_flight_props';
        if (!self::table_exists($tf)) {
            self::install();
        } else {
            $fcols = self::get_columns($tf);
            if (!isset($fcols['status']))      self::run("ALTER TABLE `{$tf}` ADD COLUMN `status` ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending' AFTER `source`");
            if (!isset($fcols['created_by']))  self::run("ALTER TABLE `{$tf}` ADD COLUMN `created_by` BIGINT UNSIGNED NULL AFTER `status`");
            if (!isset($fcols['approved_by'])) self::run("ALTER TABLE `{$tf}` ADD COLUMN `approved_by` BIGINT UNSIGNED NULL AFTER `created_by`");
            if (!isset($fcols['created_at']))  self::run("ALTER TABLE `{$tf}` ADD COLUMN `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `approved_by`");
            if (!isset($fcols['approved_at'])) self::run("ALTER TABLE `{$tf}` ADD COLUMN `approved_at` DATETIME NULL AFTER `created_at`");

            // Standardize user_id columns
            self::standardize_user_id_column($tf, 'created_by');
            self::standardize_user_id_column($tf, 'approved_by');

            self::ensure_index($tf, 'idx_disc_status', 'disc_id, status');
            self::ensure_index($tf, 'idx_created',     'created_at');
            self::ensure_index($tf, 'idx_created_by',  'created_by');
        }

        $tfa = $wpdb->prefix . 'discgolf_flight_props_active';
        if (!self::table_exists($tfa)) self::install();

        /* ---- plastics (wiki) ---- */
        $tp = $wpdb->prefix . 'discgolf_plastics';
        if (!self::table_exists($tp)) {
            self::install();
        } else {
            $pcols = self::get_columns($tp);
            if (!isset($pcols['status']))      self::run("ALTER TABLE `{$tp}` ADD COLUMN `status` ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending' AFTER `source`");
            if (!isset($pcols['created_by']))  self::run("ALTER TABLE `{$tp}` ADD COLUMN `created_by` BIGINT UNSIGNED NULL AFTER `status`");
            if (!isset($pcols['approved_by'])) self::run("ALTER TABLE `{$tp}` ADD COLUMN `approved_by` BIGINT UNSIGNED NULL AFTER `created_by`");
            if (!isset($pcols['created_at']))  self::run("ALTER TABLE `{$tp}` ADD COLUMN `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `approved_by`");
            if (!isset($pcols['approved_at'])) self::run("ALTER TABLE `{$tp}` ADD COLUMN `approved_at` DATETIME NULL AFTER `created_at`");

            self::ensure_index($tp, 'idx_disc_status', 'disc_id, status');
            self::ensure_index($tp, 'idx_plastic',     'plastic_name');

            // Normalize previously seeded-as-approved items to pending
            self::run("
                UPDATE `{$tp}`
                   SET `status` = 'pending',
                       `approved_by` = NULL,
                       `approved_at` = NULL
                 WHERE `status` = 'approved'
                   AND (`source` LIKE 'seed:%' OR `source` = 'seed' OR `source` IS NULL)
            ");
        }

        /* ---- lightweight disc->plastic table ---- */
        $tdp = $wpdb->prefix . 'discgolf_disc_plastics';
        if (self::table_exists($tdp)) {
            self::run("ALTER TABLE `{$tdp}` MODIFY `status` VARCHAR(20) NOT NULL DEFAULT 'pending'");
            self::ensure_index($tdp, 'disc_id', 'disc_id');
            self::ensure_index($tdp, 'status',  'status');
            self::ensure_index($tdp, 'name',    'name');
        } else {
            self::install();
        }

        /* ---- text suggestions ---- */
        $tt = $wpdb->prefix . 'discgolf_text_suggestions';
        if (!self::table_exists($tt)) {
            self::install();
        } else {
            $tcols = self::get_columns($tt);
            if (!isset($tcols['type']))        self::run("ALTER TABLE `{$tt}` ADD COLUMN `type` ENUM('info','history') NOT NULL AFTER `disc_id`");
            if (!isset($tcols['content']))     self::run("ALTER TABLE `{$tt}` ADD COLUMN `content` MEDIUMTEXT NOT NULL AFTER `type`");
            if (!isset($tcols['status']))      self::run("ALTER TABLE `{$tt}` ADD COLUMN `status` ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending' AFTER `content`");
            if (!isset($tcols['created_by']))  self::run("ALTER TABLE `{$tt}` ADD COLUMN `created_by` BIGINT UNSIGNED NULL AFTER `status`");
            if (!isset($tcols['approved_by'])) self::run("ALTER TABLE `{$tt}` ADD COLUMN `approved_by` BIGINT UNSIGNED NULL AFTER `created_by`");
            if (!isset($tcols['created_at']))  self::run("ALTER TABLE `{$tt}` ADD COLUMN `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `approved_by`");
            if (!isset($tcols['approved_at'])) self::run("ALTER TABLE `{$tt}` ADD COLUMN `approved_at` DATETIME NULL AFTER `created_at`");
            self::ensure_index($tt, 'idx_disc_type',   'disc_id, type');
            self::ensure_index($tt, 'idx_disc_status', 'disc_id, status');
            self::ensure_index($tt, 'idx_created',     'created_at');
        }

        $tta = $wpdb->prefix . 'discgolf_text_active';
        if (!self::table_exists($tta)) self::install();

        /* ---- user collections ---- */
        $tc = $wpdb->prefix . 'discgolf_user_collection';
        if (!self::table_exists($tc)) {
            self::install();
            self::migrate_collection_cpt_to_table();
        } else {
            $indexes = self::get_indexes($tc);
            if (isset($indexes['uniq_user_disc'])) self::run("ALTER TABLE `{$tc}` DROP INDEX `uniq_user_disc`");
            self::ensure_index($tc, 'idx_user',      'user_id');
            self::ensure_index($tc, 'idx_disc',      'disc_id');
            self::ensure_index($tc, 'idx_user_disc', 'user_id, disc_id');
        }

        /* ---- personal discs ---- */
        $tpd = $wpdb->prefix . 'discgolf_personal_discs';
        if (!self::table_exists($tpd)) {
            self::install();
        } else {
            $pcols = self::get_columns($tpd);
            if (!isset($pcols['custom_fields'])) self::run("ALTER TABLE `{$tpd}` ADD COLUMN `custom_fields` JSON NULL AFTER `personal_notes`");
            if (!isset($pcols['weight']))        self::run("ALTER TABLE `{$tpd}` ADD COLUMN `weight` SMALLINT UNSIGNED NULL AFTER `plastic_type`");
        }

        /* ---------------- Gamification (XP) self-heal ---------------- */

        // Ledger (MIGRATE source ENUM -> VARCHAR(32))
        $txl = $wpdb->prefix . 'discgolf_xp_ledger';
        if (!self::table_exists($txl)) {
            self::install();
        } else {
            $xlrows = self::get_column_rows($txl);
            $xlcols = array_change_key_case(array_column($xlrows, null, 'Field'));

            // Rename legacy columns if present
            if (isset($xlcols['points']) && !isset($xlcols['delta'])) {
                self::run("ALTER TABLE `{$txl}` CHANGE `points` `delta` INT NOT NULL");
                $xlrows = self::get_column_rows($txl);
                $xlcols = array_change_key_case(array_column($xlrows, null, 'Field'));
            }
            if (isset($xlcols['meta_json']) && !isset($xlcols['meta'])) {
                self::run("ALTER TABLE `{$txl}` CHANGE `meta_json` `meta` LONGTEXT NULL");
                $xlrows = self::get_column_rows($txl);
                $xlcols = array_change_key_case(array_column($xlrows, null, 'Field'));
            }

            // Ensure required columns
            if (!isset($xlcols['action']))     self::run("ALTER TABLE `{$txl}` ADD COLUMN `action` VARCHAR(64) NOT NULL AFTER `user_id`");
            if (!isset($xlcols['source']))     self::run("ALTER TABLE `{$txl}` ADD COLUMN `source` VARCHAR(32) NOT NULL AFTER `action`");
            if (!isset($xlcols['source_ref'])) self::run("ALTER TABLE `{$txl}` ADD COLUMN `source_ref` BIGINT UNSIGNED NULL AFTER `source`");
            if (!isset($xlcols['delta']))      self::run("ALTER TABLE `{$txl}` ADD COLUMN `delta` INT NOT NULL AFTER `source_ref`");
            if (!isset($xlcols['meta']))       self::run("ALTER TABLE `{$txl}` ADD COLUMN `meta` LONGTEXT NULL AFTER `delta`");
            if (!isset($xlcols['created_at'])) self::run("ALTER TABLE `{$txl}` ADD COLUMN `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP");

            // If source is an ENUM, convert to VARCHAR(32)
            $sourceType = '';
            foreach ($xlrows as $r) {
                if (($r['Field'] ?? '') === 'source') { $sourceType = strtolower((string)($r['Type'] ?? '')); break; }
            }
            if (strpos($sourceType, 'enum(') !== false) {
                self::run("ALTER TABLE `{$txl}` MODIFY `source` VARCHAR(32) NOT NULL");
            }

            // Indexes for better performance
            self::ensure_index($txl, 'idx_user_time', 'user_id, created_at');
            self::ensure_index($txl, 'idx_action',    'action');
            self::ensure_index($txl, 'idx_source',    'source');
            self::ensure_index($txl, 'idx_source_ref','source_ref');
            self::ensure_index($txl, 'idx_user_action', 'user_id, action');
            self::ensure_index($txl, 'idx_source_ref_user', 'source, source_ref, user_id');
        }

        // Totals (legacy compatibility)
        $txt = $wpdb->prefix . 'discgolf_xp_totals';
        if (!self::table_exists($txt)) {
            self::install();
        } else {
            $xtcols = self::get_columns($txt);
            if (!isset($xtcols['user_id']))    self::run("ALTER TABLE `{$txt}` ADD COLUMN `user_id` BIGINT UNSIGNED NOT NULL PRIMARY KEY");
            if (!isset($xtcols['xp_total']))   self::run("ALTER TABLE `{$txt}` ADD COLUMN `xp_total` BIGINT NOT NULL DEFAULT 0");
            if (!isset($xtcols['level']))      self::run("ALTER TABLE `{$txt}` ADD COLUMN `level` INT NOT NULL DEFAULT 1");
            if (!isset($xtcols['updated_at'])) self::run("ALTER TABLE `{$txt}` ADD COLUMN `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        }
    }

    /* -----------------------------
     * Admin utilities
     * ----------------------------- */

    public static function truncateDiscsAndImages(): void {
        global $wpdb;
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_discs`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_images`");
    }

    public static function wipeAll(): void {
        global $wpdb;
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_images`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_reviews`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_discs`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_flight_props`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_flight_props_active`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_plastics`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_text_suggestions`");
        self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_text_active`");
        // Intentionally do NOT wipe XP tables unless you want a full reset.
        // self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_xp_ledger`");
        // self::run("TRUNCATE TABLE `{$wpdb->prefix}discgolf_xp_totals`");
    }

    public static function backupReviewsByKey(): void {
        global $wpdb;
        $sql = "
            SELECT r.user_id, r.rating, r.review, d.manufacturer, d.name
            FROM `{$wpdb->prefix}discgolf_reviews` r
            JOIN `{$wpdb->prefix}discgolf_discs`   d ON d.id = r.disc_id
        ";
        $rows = $wpdb->get_results($sql, ARRAY_A);
        if ($rows) {
            update_option('dgdb_review_backup', ['ts' => time(), 'rows' => $rows], false);
        }
    }

    public static function relinkReviewsFromBackup(): void {
        $backup = get_option('dgdb_review_backup');
        if (empty($backup['rows']) || !is_array($backup['rows'])) return;

        global $wpdb;
        $td = "{$wpdb->prefix}discgolf_discs";
        $tr = "{$wpdb->prefix}discgolf_reviews";

        foreach ($backup['rows'] as $r) {
            $disc_id = $wpdb->get_var(
                $wpdb->prepare("SELECT id FROM `{$td}` WHERE `manufacturer`=%s AND `name`=%s", $r['manufacturer'], $r['name'])
            );
            if ($disc_id) {
                $wpdb->replace($tr, [
                    'user_id' => (int) $r['user_id'],
                    'disc_id' => (int) $disc_id,
                    'rating'  => (int) $r['rating'],
                    'review'  => (string) $r['review'],
                ], ['%d','%d','%d','%s']);

                if (!empty($wpdb->last_error)) {
                    error_log('DGDB relinkReviewsFromBackup error: ' . $wpdb->last_error);
                }
            }
        }
        delete_option('dgdb_review_backup');
    }

    public static function migrate_collection_cpt_to_table(): int {
        // Preserve original integration point
        return \DiscGolfDB\Collection::migrate_from_cpt();
    }

    public static function cleanup_collection_cpt(): void {
        global $wpdb;
        self::run("DELETE FROM `{$wpdb->posts}` WHERE `post_type` = 'dgdb_collect'");
        self::run("
            DELETE pm FROM `{$wpdb->postmeta}` pm
            LEFT JOIN `{$wpdb->posts}` p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ");
        error_log('DGDB: Cleaned up old collection CPT data');
    }

    /* -----------------------------
     * Internal helpers
     * ----------------------------- */

    /** Execute a query; on error, log query & error once. */
    private static function run(string $sql): void {
        global $wpdb;

        // Security: Log potentially dangerous operations in development
        if (defined('WP_DEBUG') && WP_DEBUG && (stripos($sql, 'DROP') !== false || stripos($sql, 'TRUNCATE') !== false)) {
            error_log('DGDB: Executing potentially dangerous SQL: ' . substr($sql, 0, 200) . '...');
        }

        $result = $wpdb->query($sql);
        if ($result === false || !empty($wpdb->last_error)) {
            $snippet = (strlen($sql) > 900) ? substr($sql, 0, 900) . '…' : $sql;
            error_log('DGDB SQL error: ' . $wpdb->last_error . ' | SQL: ' . $snippet);

            // For column additions that fail, check if it's because column already exists
            if (stripos($sql, 'ADD COLUMN') !== false && stripos($wpdb->last_error, 'Duplicate column name') !== false) {
                // This is expected - column already exists, not a real error
                return;
            }

            // For index additions that fail, check if it's because index already exists
            if (stripos($sql, 'ADD INDEX') !== false && stripos($wpdb->last_error, 'Duplicate key name') !== false) {
                // This is expected - index already exists, not a real error
                return;
            }
        }
    }

    /** Check if table exists */
    private static function table_exists(string $table): bool {
        global $wpdb;
        $like = $wpdb->prepare('SHOW TABLES LIKE %s', $table);
        $found = $wpdb->get_var($like);
        return ($found === $table);
    }

    /** Get columns for a table (cached) */
    private static function get_columns(string $table): array {
        if (isset(self::$colCache[$table])) {
            return self::$colCache[$table];
        }
        global $wpdb;
        $rows = $wpdb->get_results("SHOW COLUMNS FROM `{$table}`", ARRAY_A) ?: [];
        $out = [];
        foreach ($rows as $r) {
            if (!empty($r['Field'])) $out[$r['Field']] = true;
        }
        self::$colCache[$table] = $out;
        return $out;
    }

    /** Raw column rows (for type checks like ENUM->VARCHAR migration) */
    private static function get_column_rows(string $table): array {
        global $wpdb;
        return $wpdb->get_results("SHOW COLUMNS FROM `{$table}`", ARRAY_A) ?: [];
    }

    /** Retrieve index names (cached) */
    private static function get_indexes(string $table): array {
        if (isset(self::$idxCache[$table])) {
            return self::$idxCache[$table];
        }
        global $wpdb;
        $rows = $wpdb->get_results("SHOW INDEX FROM `{$table}`", ARRAY_A) ?: [];
        $out  = [];
        foreach ($rows as $r) {
            if (!empty($r['Key_name'])) $out[$r['Key_name']] = true;
        }
        self::$idxCache[$table] = $out;
        return $out;
    }

    /** Raw index rows (for uniqueness inspection) */
    private static function get_index_rows(string $table): array {
        global $wpdb;
        return $wpdb->get_results("SHOW INDEX FROM `{$table}`", ARRAY_A) ?: [];
    }

    /** Ensure a named index exists on given columns (comma list ok) */
    private static function ensure_index(string $table, string $index, string $cols): void {
        // First check if table exists
        if (!self::table_exists($table)) {
            error_log("DGDB: Cannot create index on non-existent table: {$table}");
            return;
        }

        // Check if all columns in the index exist
        $column_list = array_map('trim', explode(',', $cols));
        $existing_columns = self::get_columns($table);

        foreach ($column_list as $col) {
            // Remove any backticks and extra spaces
            $clean_col = trim(str_replace('`', '', $col));
            if (!isset($existing_columns[$clean_col])) {
                error_log("DGDB: Cannot create index {$index} on {$table}: column {$clean_col} does not exist");
                return;
            }
        }

        $have = self::get_indexes($table);
        if (empty($have[$index])) {
            $colsSql = implode(', ', array_map(
                static fn($c) => '`' . trim($c, " `") . '`',
                explode(',', $cols)
            ));
            self::run("ALTER TABLE `{$table}` ADD INDEX `{$index}` ({$colsSql})");
            unset(self::$idxCache[$table]);
            self::get_indexes($table);
        }
    }

    /**
     * Security: Validate table name to prevent injection
     * Only allows our known plugin tables
     */
    public static function validate_table_name(string $table): bool {
        global $wpdb;
        $allowed_tables = [
            $wpdb->prefix . 'discgolf_discs',
            $wpdb->prefix . 'discgolf_reviews',
            $wpdb->prefix . 'discgolf_images',
            $wpdb->prefix . 'discgolf_user_collection',
            $wpdb->prefix . 'discgolf_personal_discs',
            $wpdb->prefix . 'discgolf_plastics',
            $wpdb->prefix . 'discgolf_plastic_votes',
            $wpdb->prefix . 'discgolf_disc_plastics',
            $wpdb->prefix . 'discgolf_flight_props',
            $wpdb->prefix . 'discgolf_flight_props_active',
            $wpdb->prefix . 'discgolf_text_suggestions',
            $wpdb->prefix . 'discgolf_text_active',
            $wpdb->prefix . 'discgolf_xp_totals',
            $wpdb->prefix . 'discgolf_xp_ledger',
            $wpdb->prefix . 'discgolf_user_xp',
            $wpdb->prefix . 'discgolf_ranks',
        ];

        return in_array($table, $allowed_tables, true);
    }

    /**
     * Standardize user_id column to BIGINT UNSIGNED for consistency
     * This ensures all user_id columns use the same data type as WordPress users table
     */
    private static function standardize_user_id_column(string $table, string $column): void {
        if (!self::validate_table_name($table)) {
            error_log("DGDB: Invalid table name for user_id standardization: {$table}");
            return;
        }

        $column_rows = self::get_column_rows($table);
        foreach ($column_rows as $row) {
            if (($row['Field'] ?? '') === $column) {
                $current_type = strtolower((string)($row['Type'] ?? ''));

                // Only modify if it's not already BIGINT UNSIGNED
                if ($current_type !== 'bigint unsigned' && $current_type !== 'bigint(20) unsigned') {
                    $nullable = (($row['Null'] ?? 'NO') === 'YES') ? 'NULL' : 'NOT NULL';
                    $default = '';
                    if (!empty($row['Default'])) {
                        $default = "DEFAULT '{$row['Default']}'";
                    }

                    self::run("ALTER TABLE `{$table}` MODIFY `{$column}` BIGINT UNSIGNED {$nullable} {$default}");
                    error_log("DGDB: Standardized {$table}.{$column} to BIGINT UNSIGNED");
                }
                break;
            }
        }
    }

    /**
     * Add foreign key constraints safely (only if they don't exist)
     * This improves data integrity while maintaining backward compatibility
     */
    private static function add_foreign_key_constraint(string $table, string $constraint_name, string $column, string $ref_table, string $ref_column, string $on_delete = 'CASCADE'): void {
        if (!self::validate_table_name($table)) {
            error_log("DGDB: Invalid table name for foreign key: {$table}");
            return;
        }

        global $wpdb;

        // Check if constraint already exists
        $existing = $wpdb->get_var($wpdb->prepare("
            SELECT CONSTRAINT_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = %s
            AND TABLE_NAME = %s
            AND CONSTRAINT_NAME = %s
        ", DB_NAME, $table, $constraint_name));

        if (!$existing) {
            self::run("ALTER TABLE `{$table}` ADD CONSTRAINT `{$constraint_name}` FOREIGN KEY (`{$column}`) REFERENCES `{$ref_table}`(`{$ref_column}`) ON DELETE {$on_delete}");
            error_log("DGDB: Added foreign key constraint {$constraint_name} to {$table}");
        }
    }
}
