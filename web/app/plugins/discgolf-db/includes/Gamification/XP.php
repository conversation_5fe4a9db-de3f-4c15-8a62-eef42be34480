<?php
namespace DiscGolfDB\Gamification;

defined('ABSPATH') || exit;

class XP {
    const OPT_RULES = 'dgdb_xp_rules';
    const OPT_RANKS = 'dgdb_rank_thresholds';

    // Usermeta flags for one-time awards
    private const UM_SIGNUP             = 'dgdb_xp_awarded_signup';
    private const UM_PROFILE_EDIT       = 'dgdb_xp_awarded_profile_edit';
    private const UM_FIRST_COLLECTION   = 'dgdb_xp_awarded_first_collection_add';
    private const UM_FIRST_DISC_EDIT    = 'dgdb_xp_awarded_first_disc_edit';

    /** Register hooks */
    public static function register(): void {
        add_action('init', [self::class, 'ensure_defaults'], 1);

        // Award on WP user creation (once)
        add_action('user_register', [self::class, 'awardSignup'], 10, 1);
    }

    /** Default rules (editable in admin) */
    public static function default_rules(): array {
        return [
            'review' => [
                'base'          => 10,
                'title'         => 5,
                'text_per_50'   => 1,
                'text_cap'      => 40,
                'min_text'      => 60,
                'fn_all'        => 15,
                'fn_each'       => 4,
                'plastic'       => 6,
                'weight'        => 4,
                'image'         => 12,
                'first_on_disc' => 10,
                'max_per_review'=> 70,
                'daily_cap'     => 250,
            ],
            'streak' => [
                'enabled'          => true,
                'week_min_reviews' => 3,
                'weekly_bonus'     => 25,
            ],
            'referral' => [
                'enabled' => true,
                'bonus'   => 250, // bypasses daily cap by design
            ],
            // New fixed-value sources (kept here for reference; not capped)
            'fixed' => [
                'signup'               => 250,
                'profile_edit'         => 250,
                'first_collection_add' => 250,
                'first_disc_edit'      => 250,
                'plastic_approved'     => 20,
                'wiki_info'            => 30,
                'wiki_history'         => 40,
                'image_approved'       => 25,
            ],
        ];
    }

    /** Make 12 evenly spaced ranks from 0 -> 50,000 (inclusive). */
    public static function default_ranks(): array {
        $totalRanks = 12;
        $maxXP = 50000;
        $out = [];
        for ($i = 1; $i <= $totalRanks; $i++) {
            // Even spacing across 11 intervals (rank1=0 ... rank12=50000)
            $min = (int)round(($maxXP * ($i - 1)) / ($totalRanks - 1));
            $out[$i] = [
                'slug'   => 'rank-' . $i,
                'label'  => self::rank_label($i),
                'min_xp' => $min,
            ];
        }
        return $out;
    }

    protected static function rank_label(int $i): string {
        $labels = [
            1 => 'Rookie',
            2 => 'Cadet',
            3 => 'Scout',
            4 => 'Thrower',
            5 => 'Field Tester',
            6 => 'Pathfinder',
            7 => 'Technician',
            8 => 'Mentor',
            9 => 'Specialist',
            10 => 'Ace',
            11 => 'Legend',
            12 => 'Mythic',
        ];
        return $labels[$i] ?? ('Rank ' . $i);
    }

    /** Ensure rules + 12-rank thresholds (0..50000). */
    public static function ensure_defaults(): void {
        $rules = get_option(self::OPT_RULES);
        if (!is_array($rules)) {
            update_option(self::OPT_RULES, self::default_rules(), false);
        }

        global $wpdb;
        $tranks = $wpdb->prefix . 'discgolf_ranks';

        // If rank table is empty or doesn't match 12 ranks up to 50,000, reseed.
        $rows = $wpdb->get_results("SELECT id, min_xp FROM $tranks ORDER BY sort ASC", ARRAY_A);
        $needReseed = false;
        if (!$rows || count($rows) !== 12) {
            $needReseed = true;
        } else {
            $maxRow = end($rows);
            if ((int)$maxRow['min_xp'] !== 50000) $needReseed = true;
        }

        if ($needReseed) {
            $wpdb->query("TRUNCATE TABLE $tranks");
            $defs = self::default_ranks();
            foreach ($defs as $id => $row) {
                $wpdb->insert($tranks, [
                    'id'      => (int)$id,
                    'slug'    => $row['slug'],
                    'label'   => $row['label'],
                    'min_xp'  => (int)$row['min_xp'],
                    'perks'   => null,
                    'sort'    => (int)$id,
                ], ['%d','%s','%s','%d','%s','%d']);
            }
            update_option(self::OPT_RANKS, wp_list_pluck($defs, 'min_xp'), false);
        } else {
            // cache thresholds from DB
            $thr = [];
            foreach ($rows as $r) $thr[(int)$r['id']] = (int)$r['min_xp'];
            update_option(self::OPT_RANKS, $thr, false);
        }
    }

    /** Calculate XP for a review payload */
    public static function calc_review_xp(array $payload): int {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $r = $R['review'];

        $xp = 0;
        $xp += (int)$r['base'];

        if (!empty($payload['title'])) $xp += (int)$r['title'];

        $text = trim((string)($payload['text'] ?? ''));
        $len  = strlen($text);
        if ($len >= (int)$r['min_text']) {
            $xp += min((int)$r['text_cap'], (int)floor($len / 50) * (int)$r['text_per_50']);
        }

        // flight numbers
        $have = 0;
        foreach (['speed','glide','turn','fade'] as $k) {
            if ($payload[$k] !== null && $payload[$k] !== '' && is_numeric($payload[$k])) $have++;
        }
        if ($have === 4) $xp += (int)$r['fn_all'];
        elseif ($have > 0) $xp += (int)$r['fn_each'] * $have;

        if (!empty($payload['plastic'])) $xp += (int)$r['plastic'];
        if (!empty($payload['weight']))  $xp += (int)$r['weight'];
        if (!empty($payload['image']))   $xp += (int)$r['image'];
        if (!empty($payload['first_on_disc'])) $xp += (int)$r['first_on_disc'];

        $xp = min((int)$r['max_per_review'], max(0, (int)$xp));
        return $xp;
    }

    /**
     * Core add XP (no caps applied here). Action is optional but helps HUD copy.
     * Returns the updated state.
     */
    public static function add_xp(int $user_id, int $delta, string $source, ?int $source_ref = null, array $meta = [], string $action = ''): array {
        if ($delta <= 0) return self::get_user_state($user_id);

        global $wpdb;
        $ledger = $wpdb->prefix . 'discgolf_xp_ledger';
        $totals = $wpdb->prefix . 'discgolf_user_xp';
        $ranks  = get_option(self::OPT_RANKS, []);

        // Insert ledger row
        $wpdb->insert($ledger, [
            'user_id'    => $user_id,
            'action'     => (string)$action, // can be '', 'first_time', 'approved', etc.
            'source'     => $source,
            'source_ref' => $source_ref,
            'delta'      => $delta,
            'meta'       => wp_json_encode($meta),
            'created_at' => current_time('mysql'),
        ], ['%d','%s','%s','%d','%d','%s','%s']);

        // Upsert totals with CURRENT thresholds (not cached in table!)
        $row   = $wpdb->get_row($wpdb->prepare("SELECT total_xp, level FROM $totals WHERE user_id=%d", $user_id), ARRAY_A);
        $total = ($row ? (int)$row['total_xp'] : 0) + $delta;

        [$level, $nextXP] = self::derive_level_and_next($total, $ranks);

        if ($row) {
            $wpdb->update($totals, [
                'total_xp'      => $total,
                'level'         => $level,
                'next_level_xp' => $nextXP,
                'updated_at'    => current_time('mysql'),
            ], ['user_id' => $user_id], ['%d','%d','%d','%s'], ['%d']);
        } else {
            $wpdb->insert($totals, [
                'user_id'       => $user_id,
                'total_xp'      => $total,
                'level'         => $level,
                'next_level_xp' => $nextXP,
                'updated_at'    => current_time('mysql'),
            ], ['%d','%d','%d','%d','%s']);
        }

        // Notify HUD/modal listeners
        do_action('dgdb_xp_changed', $user_id);

        $state = self::get_user_state($user_id);
        $state['last_delta'] = $delta;
        return $state;
    }

    /** Public “award” path for reviews (applies daily cap) */
    public static function awardForReview(int $review_id, int $user_id, array $payload): array {
        $rules = get_option(self::OPT_RULES, self::default_rules());
        $cap   = (int)($rules['review']['daily_cap'] ?? 250);

        global $wpdb;
        $ledger = $wpdb->prefix . 'discgolf_xp_ledger';
        $today  = gmdate('Y-m-d');

        $sum = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT COALESCE(SUM(delta),0) FROM $ledger WHERE user_id=%d AND source='review' AND DATE(created_at)=%s",
            $user_id, $today
        ));

        if ($sum >= $cap) {
            self::maybe_award_weekly_streak($user_id);
            $state = self::get_user_state($user_id);
            $state['last_delta'] = 0;
            return $state;
        }

        $xp = self::calc_review_xp($payload);
        $xp = max(0, min($xp, $cap - $sum));

        $state = self::add_xp($user_id, $xp, 'review', $review_id, [
            'review_len' => strlen((string)($payload['text'] ?? '')),
        ]);

        self::maybe_award_weekly_streak($user_id);

        return $state;
    }

    /** If user hit the weekly streak requirement and hasn’t received this week’s bonus, award it. */
    public static function maybe_award_weekly_streak(int $user_id): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $cfg = $R['streak'] ?? ['enabled'=>false];
        if (empty($cfg['enabled'])) return;

        $need = (int)($cfg['week_min_reviews'] ?? 3);
        $bonus = (int)($cfg['weekly_bonus'] ?? 25);
        if ($need <= 0 || $bonus <= 0) return;

        // Start of current ISO week (Monday)
        $weekStart = (new \DateTimeImmutable('monday this week', wp_timezone()))->format('Y-m-d');

        global $wpdb;
        $trev = $wpdb->prefix . 'discgolf_reviews';
        $tled = $wpdb->prefix . 'discgolf_xp_ledger';

        // Count distinct reviews this week
        $cnt = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $trev WHERE user_id=%d AND created_at >= %s",
            $user_id, $weekStart . ' 00:00:00'
        ));

        if ($cnt < $need) return;

        // Already awarded this week's streak?
        $exists = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $tled WHERE user_id=%d AND source='streak' AND JSON_EXTRACT(meta,'$.week_start')=%s",
            $user_id, wp_json_encode($weekStart)
        ));
        if ($exists > 0) return;

        self::add_xp($user_id, $bonus, 'streak', null, ['week_start' => $weekStart, 'count' => $cnt]);
    }

    /** Return user state for HUD + modal (now self-heals after threshold edits) */
    public static function get_user_state(int $user_id): array {
        global $wpdb;
        $totals = $wpdb->prefix . 'discgolf_user_xp';
        $ranksT = $wpdb->prefix . 'discgolf_ranks';

        $row   = $wpdb->get_row($wpdb->prepare("SELECT total_xp, level, next_level_xp FROM $totals WHERE user_id=%d", $user_id), ARRAY_A);
        $total = (int)($row['total_xp'] ?? 0);
        $storedLevel = (int)($row['level'] ?? 1);
        $storedNext  = (int)($row['next_level_xp'] ?? 100);

        // Always derive from CURRENT thresholds
        $thr = get_option(self::OPT_RANKS, []);
        [$level, $next] = self::derive_level_and_next($total, $thr);

        // Self-heal the cached totals row if stale
        if ($row && ($storedLevel !== $level || $storedNext !== $next)) {
            $wpdb->update($totals, [
                'level'         => $level,
                'next_level_xp' => $next,
                'updated_at'    => current_time('mysql'),
            ], ['user_id' => $user_id], ['%d','%d','%s'], ['%d']);
        }

        // Current level's threshold for progress bar
        $curThr = 0;
        if ($level > 0) {
            // Prefer option cache; fall back to DB if needed
            if (!empty($thr[$level])) {
                $curThr = (int)$thr[$level];
            } else {
                $curThr = (int)$wpdb->get_var($wpdb->prepare("SELECT min_xp FROM $ranksT WHERE id=%d", $level));
            }
        }

        // Fetch label/slug for current rank (from DB to reflect admin edits)
        $rankRow = $wpdb->get_row($wpdb->prepare("SELECT id, label, slug FROM $ranksT WHERE id=%d", $level), ARRAY_A);
        $rank = $rankRow ? ['id'=>(int)$rankRow['id'], 'label'=>$rankRow['label'], 'slug'=>$rankRow['slug']] : ['id'=>1,'label'=>'Rookie','slug'=>'rookie'];

        // Progress in current level
        $currInto = max(0, $total - $curThr);
        $need     = max(1, $next - $curThr); // avoid /0

        return [
            'total_xp' => $total,
            'level'    => $level,
            'rank'     => $rank,
            'curr_xp'  => $currInto,
            'next_xp'  => $next, // absolute XP needed for next level
        ];
    }

    /**
     * Compute level and next threshold from total XP and thresholds map [level => min_xp].
     * Returns [level, next_threshold].
     */
    private static function derive_level_and_next(int $total, array $thresholds): array {
        if (empty($thresholds)) {
            // Fallback: level 1 at 0, next 100
            return [1, 100];
        }
        // Ensure thresholds are numeric and sorted by level (id)
        $levels = array_keys($thresholds);
        sort($levels, SORT_NUMERIC);

        $level = 1;
        foreach ($levels as $lvl) {
            $min = (int)$thresholds[$lvl];
            if ($total >= $min) {
                $level = (int)$lvl;
            } else {
                break;
            }
        }

        // Compute next threshold (the first > total; otherwise max)
        $next = (int)max($thresholds); // default to max (top rank)
        foreach ($levels as $lvl) {
            $min = (int)$thresholds[$lvl];
            if ($min > $total) {
                $next = $min;
                break;
            }
        }
        return [$level, $next];
    }

    /**
     * Bulk recompute all users’ level/next_level_xp to match current thresholds.
     * Safe to call after saving ranks in admin.
     * Returns number of rows updated.
     */
    public static function recompute_all_levels(): int {
        global $wpdb;
        $totals = $wpdb->prefix . 'discgolf_user_xp';
        $thr    = get_option(self::OPT_RANKS, []);
        if (empty($thr)) return 0;

        $rows = $wpdb->get_results("SELECT user_id, total_xp, level, next_level_xp FROM $totals", ARRAY_A) ?: [];
        $updated = 0;

        foreach ($rows as $r) {
            $total = (int)$r['total_xp'];
            [$level, $next] = self::derive_level_and_next($total, $thr);
            if ($level != (int)$r['level'] || $next != (int)$r['next_level_xp']) {
                $wpdb->update($totals, [
                    'level'         => $level,
                    'next_level_xp' => $next,
                    'updated_at'    => current_time('mysql'),
                ], ['user_id' => (int)$r['user_id']], ['%d','%d','%s'], ['%d']);
                $updated++;
            }
        }
        return $updated;
    }

    /* ==========================================================
     * New helpers & awarders (uncapped sources)
     * ========================================================== */

    /** One-time award using a usermeta flag; returns true if granted now. */
    public static function award_once(int $user_id, string $meta_key, int $delta, string $source, ?int $source_ref = null, array $meta = [], string $action = 'first_time'): bool {
        if ($user_id <= 0) return false;
        if (get_user_meta($user_id, $meta_key, true)) {
            return false;
        }
        // Set flag first to avoid races
        update_user_meta($user_id, $meta_key, 1);
        self::add_xp($user_id, $delta, $source, $source_ref, $meta, $action);
        return true;
    }

    /** Guard to avoid double-awarding for an approval by (user, source, source_ref). */
    private static function award_unique_by_source_ref(int $user_id, int $delta, string $source, int $source_ref, array $meta = [], string $action = 'approved'): bool {
        if ($user_id <= 0 || $source_ref <= 0) return false;
        global $wpdb;
        $ledger = $wpdb->prefix . 'discgolf_xp_ledger';
        $exists = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $ledger WHERE user_id=%d AND source=%s AND source_ref=%d",
            $user_id, $source, $source_ref
        ));
        if ($exists > 0) return false;
        self::add_xp($user_id, $delta, $source, $source_ref, $meta, $action);
        return true;
    }

    /* -------- One-time events -------- */

    public static function awardSignup(int $user_id): void {
        // Hooked to user_register
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['signup'] ?? 250);
        self::award_once($user_id, self::UM_SIGNUP, $delta, 'signup', null, []);
    }

    public static function awardProfileFirstEdit(int $user_id): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['profile_edit'] ?? 250);
        self::award_once($user_id, self::UM_PROFILE_EDIT, $delta, 'profile_edit', null, []);
    }

    public static function awardFirstCollectionAdd(int $user_id, int $disc_id): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['first_collection_add'] ?? 250);
        self::award_once($user_id, self::UM_FIRST_COLLECTION, $delta, 'first_collection_add', $disc_id, ['disc_id' => $disc_id]);
    }

    public static function awardFirstDiscEdit(int $user_id, int $collection_id): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['first_disc_edit'] ?? 250);
        self::award_once($user_id, self::UM_FIRST_DISC_EDIT, $delta, 'first_disc_edit', $collection_id, ['collection_id' => $collection_id]);
    }

    /* -------- Approval-based (per item) -------- */

    public static function awardPlasticApproved(int $plastic_id, int $created_by): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['plastic_approved'] ?? 20);
        self::award_unique_by_source_ref($created_by, $delta, 'plastic_approved', (int)$plastic_id, []);
    }

    public static function awardWikiInfoApproved(int $suggestion_id, int $created_by): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['wiki_info'] ?? 30);
        self::award_unique_by_source_ref($created_by, $delta, 'wiki_info', (int)$suggestion_id, []);
    }

    public static function awardWikiHistoryApproved(int $suggestion_id, int $created_by): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['wiki_history'] ?? 40);
        self::award_unique_by_source_ref($created_by, $delta, 'wiki_history', (int)$suggestion_id, []);
    }

    public static function awardImageApproved(int $image_id, int $user_id): void {
        $R = get_option(self::OPT_RULES, self::default_rules());
        $delta = (int)($R['fixed']['image_approved'] ?? 25);
        self::award_unique_by_source_ref($user_id, $delta, 'image_approved', (int)$image_id, []);
    }
}
