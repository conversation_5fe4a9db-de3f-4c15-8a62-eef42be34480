<?php
namespace DiscGolfDB\Gamification;

defined('ABSPATH') || exit;

/**
 * Lightweight referral system:
 * - Generates a stable token per user (stored in user_meta).
 * - Captures ?ref=TOKEN to a cookie (30 days).
 * - On user_register, if cookie present → award referrer +250 XP (no daily cap).
 * - Provides invite URL for HUD/details.
 */
class Referrals {
    const META_TOKEN   = 'dgdb_ref_token';
    const COOKIE_NAME  = 'dgdb_ref';
    const COOKIE_DAYS  = 30;
    const BONUS_XP     = 250;

    public static function register(): void {
        // Capture ref token in cookie
        add_action('init', [self::class, 'capture_ref_param'], 1);

        // Award on registration
        add_action('user_register', [self::class, 'maybe_award_on_signup'], 10, 1);
    }

    /** Make (or fetch) a per-user token */
    public static function get_or_create_token(int $user_id): string {
        $tok = get_user_meta($user_id, self::META_TOKEN, true);
        if ($tok && is_string($tok)) return $tok;

        // short + stable-ish token
        $raw = wp_generate_uuid4() . '|' . $user_id . '|' . wp_rand();
        $tok = substr(wp_hash($raw, 'auth'), 0, 12);
        update_user_meta($user_id, self::META_TOKEN, $tok);
        return $tok;
    }

    /** Build the public invite URL */
    public static function get_invite_url(int $user_id): string {
        $token = self::get_or_create_token($user_id);
        // Use home_url so it works on ddev and prod the same
        return add_query_arg('ref', $token, home_url('/'));
    }

    /** If ?ref=TOKEN present, store to cookie for 30 days */
    public static function capture_ref_param(): void {
        if (!empty($_GET['ref'])) {
            $val = sanitize_text_field(wp_unslash($_GET['ref']));
            // Set cookie for the current domain
            setcookie(self::COOKIE_NAME, $val, time() + (self::COOKIE_DAYS * DAY_IN_SECONDS), COOKIEPATH ?: '/', COOKIE_DOMAIN ?: '', is_ssl(), true);
            // also in $_COOKIE for this request
            $_COOKIE[self::COOKIE_NAME] = $val;
        }
    }

    /** On signup, look up referrer by token and award XP (not capped) */
    public static function maybe_award_on_signup(int $new_user_id): void {
        $tok = $_COOKIE[self::COOKIE_NAME] ?? '';
        if (!$tok) return;

        global $wpdb;
        $usermeta = $wpdb->usermeta;
        $meta_key = self::META_TOKEN;

        // find referrer by token in usermeta
        $referrer = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$usermeta} WHERE meta_key=%s AND meta_value=%s LIMIT 1",
            $meta_key, $tok
        ));
        if ($referrer > 0 && $referrer !== $new_user_id) {
            // Award referral bonus – bypass daily caps by calling add_xp directly
            XP::add_xp($referrer, self::BONUS_XP, 'referral', $new_user_id, [
                'token' => $tok,
                'new_user' => $new_user_id,
            ]);
        }

        // clear cookie
        setcookie(self::COOKIE_NAME, '', time() - DAY_IN_SECONDS, COOKIEPATH ?: '/', COOKIE_DOMAIN ?: '', is_ssl(), true);
        unset($_COOKIE[self::COOKIE_NAME]);
    }
}
