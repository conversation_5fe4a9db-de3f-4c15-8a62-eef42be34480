<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

/**
 * Direct table-based collection system (replaces CPT)
 * Provides better performance and simpler queries
 */
class Collection {

    /**
     * Add a collection item (allows multiple instances of same disc)
     * @param int   $user_id
     * @param int   $disc_id
     * @param array $args ['notes' => string]
     * @return int|false Collection ID on success, false on failure
     */
    public static function add_item(int $user_id, int $disc_id, array $args = []) {
        if ($user_id <= 0 || $disc_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $notes = $args['notes'] ?? '';

        $result = $wpdb->insert(
            $table,
            [
                'user_id' => $user_id,
                'disc_id' => $disc_id,
                'notes' => $notes,
                'created_at' => current_time('mysql')
            ],
            ['%d', '%d', '%s', '%s']
        );

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Legacy method for backwards compatibility - adds item if not exists
     * @param int   $user_id
     * @param int   $disc_id
     * @param array $args ['notes' => string]
     * @return bool Success
     */
    public static function upsert_item(int $user_id, int $disc_id, array $args = []): bool {
        // Check if user already has this disc
        if (!self::has_item($user_id, $disc_id)) {
            return self::add_item($user_id, $disc_id, $args) !== false;
        }
        return true; // Already exists
    }

    /**
     * Delete collection item by collection ID
     */
    public static function delete_item_by_id(int $collection_id): bool {
        if ($collection_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $personal_table = $wpdb->prefix . 'discgolf_personal_discs';

        // Delete personal disc data first (if exists)
        $wpdb->delete($personal_table, ['collection_id' => $collection_id], ['%d']);

        // Delete collection item
        $result = $wpdb->delete($table, ['id' => $collection_id], ['%d']);

        return $result !== false;
    }

    /**
     * Delete collection item for (user_id, disc_id) - removes first instance
     */
    public static function delete_item(int $user_id, int $disc_id): bool {
        if ($user_id <= 0 || $disc_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        // Get the first collection ID for this user/disc combination
        $collection_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM `{$table}` WHERE user_id = %d AND disc_id = %d ORDER BY created_at ASC LIMIT 1",
            $user_id,
            $disc_id
        ));

        if ($collection_id) {
            return self::delete_item_by_id($collection_id);
        }

        return false;
    }

    /**
     * Duplicate a collection item by collection ID
     */
    public static function duplicate_item(int $collection_id): int|false {
        if ($collection_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        // Get the original item
        $original = $wpdb->get_row($wpdb->prepare(
            "SELECT user_id, disc_id, notes FROM `{$table}` WHERE id = %d",
            $collection_id
        ), ARRAY_A);

        if (!$original) {
            return false;
        }

        // Create duplicate
        $new_id = self::add_item(
            (int)$original['user_id'],
            (int)$original['disc_id'],
            ['notes' => $original['notes']]
        );

        return $new_id;
    }

    /**
     * Check if user has disc in collection
     */
    public static function has_item(int $user_id, int $disc_id): bool {
        if ($user_id <= 0 || $disc_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM `{$table}` WHERE user_id = %d AND disc_id = %d",
            $user_id,
            $disc_id
        ));

        return (int)$count > 0;
    }

    /**
     * Get user's collection with disc details and personal information
     */
    public static function get_user_collection(int $user_id, int $limit = 50, int $offset = 0): array {
        if ($user_id <= 0) {
            return [];
        }

        global $wpdb;
        $collection_table = $wpdb->prefix . 'discgolf_user_collection';
        $discs_table = $wpdb->prefix . 'discgolf_discs';
        $personal_table = $wpdb->prefix . 'discgolf_personal_discs';

        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT c.id as collection_id, c.disc_id, c.notes, c.created_at,
                    d.manufacturer, d.name, d.type,
                    p.custom_image_id, p.plastic_type, p.weight, p.personal_notes,
                    p.custom_fields
             FROM `{$collection_table}` c
             LEFT JOIN `{$discs_table}` d ON d.id = c.disc_id
             LEFT JOIN `{$personal_table}` p ON p.collection_id = c.id
             WHERE c.user_id = %d
             ORDER BY c.created_at DESC
             LIMIT %d OFFSET %d",
            $user_id,
            $limit,
            $offset
        ), ARRAY_A);

        // Parse JSON custom fields
        foreach ($results as &$result) {
            if ($result['custom_fields']) {
                $result['custom_fields'] = json_decode($result['custom_fields'], true);
            } else {
                $result['custom_fields'] = [];
            }
        }

        return $results ?: [];
    }

    /**
     * Get collection count for user
     */
    public static function get_user_collection_count(int $user_id): int {
        if ($user_id <= 0) {
            return 0;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM `{$table}` WHERE user_id = %d",
            $user_id
        ));

        return (int)$count;
    }

    /**
     * Get collection item with notes
     */
    public static function get_item(int $user_id, int $disc_id): ?array {
        if ($user_id <= 0 || $disc_id <= 0) {
            return null;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM `{$table}` WHERE user_id = %d AND disc_id = %d",
            $user_id,
            $disc_id
        ), ARRAY_A);

        return $result ?: null;
    }

    /**
     * Get all disc IDs for a user's collection
     */
    public static function get_user_disc_ids(int $user_id): array {
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';

        $results = $wpdb->get_col($wpdb->prepare(
            "SELECT disc_id FROM {$table} WHERE user_id = %d ORDER BY created_at DESC",
            $user_id
        ));

        return array_map('intval', $results ?: []);
    }

    /**
     * Toggle collection item (add if not exists, remove if exists).
     * On the very first ever "add" for this user (collection count 0 -> 1), award +250 XP (uncapped).
     */
    public static function toggle_item(int $user_id, int $disc_id): string {
        // Was the collection empty before?
        $hadAny = self::get_user_collection_count($user_id) > 0;

        if (self::has_item($user_id, $disc_id)) {
            self::delete_item($user_id, $disc_id);
            return 'removed';
        } else {
            self::upsert_item($user_id, $disc_id);

            // If user had no items prior to this operation, this is their first add.
            if (!$hadAny && class_exists('\DiscGolfDB\Gamification\XP')) {
                \DiscGolfDB\Gamification\XP::awardFirstCollectionAdd($user_id, $disc_id);
            }

            return 'added';
        }
    }

    /**
     * Save personal disc information.
     * On the user's first successful personal disc save, award +250 XP (uncapped).
     */
    public static function save_personal_info(int $collection_id, array $data): bool {
        if ($collection_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_personal_discs';

        // Handle custom fields as JSON
        $custom_fields = null;
        if (isset($data['custom_fields']) && is_array($data['custom_fields'])) {
            $custom_fields = wp_json_encode($data['custom_fields']);
        }

        $personal_data = [
            'collection_id'   => $collection_id,
            'custom_image_id' => isset($data['custom_image_id']) ? (int)$data['custom_image_id'] : null,
            'plastic_type'    => isset($data['plastic_type']) ? sanitize_text_field($data['plastic_type']) : null,
            'weight'          => isset($data['weight']) ? (int)$data['weight'] : null,
            'personal_notes'  => isset($data['personal_notes']) ? sanitize_textarea_field($data['personal_notes']) : null,
            'custom_fields'   => $custom_fields,
        ];

        // Use REPLACE to handle both insert and update
        $result = $wpdb->replace($table, $personal_data, ['%d', '%d', '%s', '%d', '%s', '%s']);

        // Award once per user after a successful save
        if ($result !== false && class_exists('\DiscGolfDB\Gamification\XP')) {
            // Resolve user_id owning this collection_id
            $ucTable = $wpdb->prefix . 'discgolf_user_collection';
            $user_id = (int)$wpdb->get_var($wpdb->prepare(
                "SELECT user_id FROM `{$ucTable}` WHERE id=%d",
                $collection_id
            ));
            if ($user_id > 0) {
                \DiscGolfDB\Gamification\XP::awardFirstDiscEdit($user_id, $collection_id);
            }
        }

        return $result !== false;
    }

    /**
     * Get personal disc information by collection ID
     */
    public static function get_personal_info(int $collection_id): ?array {
        if ($collection_id <= 0) {
            return null;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_personal_discs';

        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM `{$table}` WHERE collection_id = %d",
            $collection_id
        ), ARRAY_A);

        return $result ?: null;
    }

    /**
     * Delete personal disc information
     */
    public static function delete_personal_info(int $collection_id): bool {
        if ($collection_id <= 0) {
            return false;
        }

        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_personal_discs';

        $result = $wpdb->delete($table, ['collection_id' => $collection_id], ['%d']);

        return $result !== false;
    }

    /**
     * Migrate existing CPT data to new table (run once during upgrade)
     */
    public static function migrate_from_cpt(): int {
        global $wpdb;

        $migrated = 0;
        $posts = get_posts([
            'post_type' => 'dgdb_collect',
            'post_status' => 'any',
            'posts_per_page' => -1,
            'fields' => 'ids'
        ]);

        foreach ($posts as $post_id) {
            $post = get_post($post_id);
            if (!$post) continue;

            $disc_id = (int)get_post_meta($post_id, 'disc_id', true);
            $notes = (string)get_post_meta($post_id, 'notes', true);

            if ($disc_id > 0 && $post->post_author > 0) {
                if (self::add_item((int)$post->post_author, $disc_id, ['notes' => $notes])) {
                    $migrated++;
                }
            }
        }

        return $migrated;
    }
}
