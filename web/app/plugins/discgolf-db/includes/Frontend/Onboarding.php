<?php
namespace DiscGolfDB\Frontend;

use DiscGolfDB\Gamification\XP;

defined('ABSPATH') || exit;

/**
 * XP Onboarding Wizard (step-by-step overlay).
 * Shortcode: [dgdb_xp_onboarding]
 */
class Onboarding {
    private const SC = 'dgdb_xp_onboarding';

    public static function register(): void {
        add_shortcode(self::SC, [self::class, 'render']);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue']);
    }

    public static function render(): string {
        if (!is_user_logged_in()) {
            $login = wp_login_url(get_permalink());
            $reg   = get_option('users_can_register') ? wp_registration_url() : '';
            ob_start(); ?>
            <div class="dgdb-onb-auth">
              <h2>Welcome!</h2>
              <p>You need an account to start the XP tutorial.</p>
              <p>
                <a class="dgdb-btn" href="<?php echo esc_url($login); ?>">Log in</a>
                <?php if ($reg): ?>
                  <a class="dgdb-btn dgdb-btn-alt" href="<?php echo esc_url($reg); ?>">Register</a>
                <?php endif; ?>
              </p>
            </div>
            <?php return (string)ob_get_clean();
        }

        // Render a minimal mount button; JS opens the overlay wizard.
        ob_start(); ?>
        <div class="dgdb-onb-launch" style="display: flex;
                                            justify-content: center;
                                            align-items: center;
                                            margin: 400px;">
          <button type="button" style="font-size: 32px;padding: 10px 20px;" class="dgdb-btn dgdb-onb-open" data-dgdb-onb-open>
            Start XP Tutorial
          </button>
        </div>
        <div class="dgdb-onb-root" id="dgdb-onb-root" hidden aria-hidden="true"></div>
        <?php
        return (string)ob_get_clean();
    }

    public static function enqueue(): void {
        if (!is_singular()) return;
        global $post;
        if (!isset($post->post_content) || !has_shortcode((string)$post->post_content, self::SC)) return;

        $version = defined('DGDB_VERSION') ? DGDB_VERSION : '1.0';

        wp_register_style('dgdb-xp-onboarding', DGDB_URL.'assets/xp-onboarding.css', [], $version);
        wp_enqueue_style('dgdb-xp-onboarding');

        wp_register_script('dgdb-xp-onboarding', DGDB_URL.'assets/xp-onboarding.js', [], $version, true);
        wp_enqueue_script('dgdb-xp-onboarding');

        // Dynamic values from rules so copy is always correct
        $R = get_option(XP::OPT_RULES, XP::default_rules());
        $review = $R['review'] ?? [];
        $fixed  = $R['fixed'] ?? [];
        $refBon = (int)($R['referral']['bonus'] ?? 250);

        $get = function($arr, $k, $d){ return isset($arr[$k]) ? (int)$arr[$k] : $d; };

        // Suggested deep-links (adjust if you have dedicated routes)
        $profileUrl    = home_url('/profile');
        $browseUrl     = home_url('/');           // Discs directory if you have one
        $collectionUrl = home_url('/profile');    // User collection lives on profile
        $experienceUrl = home_url('/experience'); // Modal entry point, if applicable
        $wikiUrl       = home_url('/wiki');       // Wiki landing

        $steps = [
            [
                'key'   => 'profile_edit',
                'title' => 'Save your profile',
                'desc'  => 'Add a display name and a short bio.',
                'xp'    => $get($fixed, 'profile_edit', 250),
                'cta'   => ['href'=>$profileUrl, 'label'=>'Go to profile'],
                'how'   => 'Open “Profile settings”, edit a field, and click Save.',
            ],
            [
                'key'   => 'first_collection_add',
                'title' => 'Add your first disc',
                'desc'  => 'Pick any disc and add it to your collection.',
                'xp'    => $get($fixed, 'first_collection_add', 250),
                'cta'   => ['href'=>$browseUrl, 'label'=>'Browse discs'],
                'how'   => 'On a disc page, click “Add to collection”.',
            ],
            [
                'key'   => 'first_disc_edit',
                'title' => 'Edit a personal disc',
                'desc'  => 'Set plastic/weight or add personal notes.',
                'xp'    => $get($fixed, 'first_disc_edit', 250),
                'cta'   => ['href'=>$collectionUrl, 'label'=>'Open collection'],
                'how'   => 'Open a collection item and save your changes.',
            ],
            [
                'key'   => 'review1',
                'title' => 'Write your first review',
                'desc'  => 'Earn XP based on quality & details.',
                'xp'    => $get($review, 'max_per_review', 70),
                'cta'   => ['href'=>$browseUrl, 'label'=>'Find a disc to review'],
                'how'   => 'Add a title, write at least '.$get($review,'min_text',60).' characters, and include flight numbers for max XP.',
            ],
            [
                'key'   => 'referral',
                'title' => 'Invite a friend',
                'desc'  => 'Share your referral link.',
                'xp'    => $refBon,
                'cta'   => ['href'=>$experienceUrl, 'label'=>'Open Experience'],
                'how'   => 'Copy your unique link and share it. XP is awarded when they sign up.',
            ],
            [
                'key'   => 'wiki_submit',
                'title' => 'Contribute to the Wiki',
                'desc'  => 'Submit an image, plastic, or text.',
                'xp'    => max(
                    $get($fixed,'plastic_approved',20),
                    $get($fixed,'wiki_info',30),
                    $get($fixed,'wiki_history',40),
                    $get($fixed,'image_approved',25)
                ),
                'cta'   => ['href'=>$wikiUrl, 'label'=>'Open Disc Wiki'],
                'how'   => 'Pick a disc and submit a helpful improvement. XP grants on approval.',
            ],
        ];

        wp_localize_script('dgdb-xp-onboarding', 'DGDB_ONB', [
            'ajax'   => admin_url('admin-ajax.php'),
            'nonce'  => wp_create_nonce('dgdb_tutorial'),
            'steps'  => $steps,
            'copy'   => [
                'start'   => 'Start XP Tutorial',
                'next'    => 'Next',
                'back'    => 'Back',
                'check'   => 'Check',
                'done'    => 'Completed',
                'close'   => 'Close',
                'skip'    => 'Skip for now',
            ],
        ]);
    }
}
