<?php
namespace DiscGolfDB\Frontend;

defined('ABSPATH') || exit;

/**
 * Lightweight HUD showing avatar, rank/level and an XP progress bar.
 * JS: assets/gamify-hud.js reads DGDB_HUD for initial state and mounts the UI.
 */
class GamificationHUD
{
    public static function register(): void
    {
        // Front assets
        add_action('wp_enqueue_scripts', [self::class, 'enqueue']);

        // Render a lightweight mount node early so we can relocate it into the header
        if (function_exists('wp_body_open')) {
            add_action('wp_body_open', [self::class, 'render_mount']);
        } else {
            add_action('wp_footer', [self::class, 'render_mount']);
        }
    }

    public static function enqueue(): void
    {
        $version = defined('DGDB_VERSION') ? DGDB_VERSION : '1.0';

        // CSS
        wp_enqueue_style(
            'dgdb-gamify-hud',
            DGDB_URL . 'assets/gamify-hud.css',
            [],
            $version
        );

        // JS (no hard deps; your hud script is vanilla)
        wp_enqueue_script(
            'dgdb-gamify-hud',
            DGDB_URL . 'assets/gamify-hud.js',
            [],
            $version,
            true
        );

        // ----- Build initial state for the HUD -----
        $state = [
            'loggedIn' => is_user_logged_in(),
            'mountSelectorCandidates' => [
                '.site-header',
                '#site-header',
                '#masthead',
                'header.site-header',
                'header.header',
                'header#header',
                'header',
                '.header',
            ],
            'user' => null,
        ];

        if ($state['loggedIn'] && class_exists('\DiscGolfDB\Gamification\XP')) {
            $uid  = (int) get_current_user_id();
            $xp   = (array) \DiscGolfDB\Gamification\XP::get_user_state($uid);
            $user = get_userdata($uid);

            // get_user_state returns:
            // total_xp, level, rank[label,slug,id], curr_xp (progress within current level),
            // next_xp (amount required within current level)
            $totalXP = (int) ($xp['total_xp'] ?? 0);
            $level   = (int) ($xp['level'] ?? 1);
            $curr    = (int) ($xp['curr_xp'] ?? 0);     // <-- FIX: use 'curr_xp' (not curr_level_xp)
            $need    = (int) ($xp['next_xp'] ?? 100);   // amount to reach next level from current level threshold

            // Defensive progress percent for ARIA
            $percent = ($need > 0) ? max(0, min(100, (int) floor(($curr / $need) * 100))) : 0;

            $state['user'] = [
                'id'      => $uid,
                'name'    => $user ? esc_html($user->display_name) : 'You',
                'avatar'  => \DiscGolfDB\Utils::get_user_avatar_url($uid, ['size' => 48]),
                'level'   => $level,
                'rank'    => (string) ($xp['rank']['label'] ?? 'Rookie'),
                'xp'      => $totalXP,
                'curr_xp' => $curr,   // progress within current level
                'next_xp' => $need,   // required to hit next level from current level threshold
                'pct'     => $percent // convenience for JS/ARIA
            ];
        }

        // Make state available to hud script
        wp_localize_script('dgdb-gamify-hud', 'DGDB_HUD', $state);
    }

    /**
     * Render a mount container early in the body.
     * JS will relocate this into the header or fall back to fixed top-right.
     */
    public static function render_mount(): void
    {
        ?>
        <div id="dgdb-hud-mount" class="dgdb-hud-mount" aria-live="polite" aria-atomic="true">
            <div class="dgdb-hud" hidden>
                <div class="dgdb-hud__avatar">
                    <img alt="" />
                </div>

                <div class="dgdb-hud__meta">
                    <div class="dgdb-hud__line">
                        <span class="dgdb-hud__name"></span>
                        <span class="dgdb-hud__rank"></span>
                        <span class="dgdb-hud__level"></span>
                    </div>

                    <!-- Progress: JS sets width + aria-valuenow from DGDB_HUD.user.pct -->
                    <div
                        class="dgdb-hud__bar dgdb-xp-progress__bar"
                        role="progressbar"
                        aria-valuemin="0"
                        aria-valuemax="100"
                        aria-valuenow="0"
                    >
                        <div class="dgdb-hud__bar-fill"></div>
                    </div>
                </div>

                <div class="dgdb-hud__toast" hidden></div>
            </div>

            <!-- Level-up burst container -->
            <div class="dgdb-hud__burst" aria-hidden="true"></div>
        </div>
        <?php
    }
}
