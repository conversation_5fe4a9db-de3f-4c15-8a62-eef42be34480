<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

class ProfileRouter {
    private static bool $is_profile = false;
    private static ?\WP_User $user = null;

    public static function register(): void {
        add_action('init', [self::class, 'add_rewrite']);
        add_filter('query_vars', [self::class, 'qv']);
        add_action('template_redirect', [self::class, 'bootstrap'], 1);
        add_filter('the_content', [self::class, 'render_content'], 999);
        add_filter('document_title_parts', [self::class, 'title'], 10);

        // AJAX: secure profile update endpoint (logged-in only)
        add_action('wp_ajax_dgdb_profile_update', [self::class, 'ajax_profile_update']);
    }

    public static function add_rewrite(): void {
        // /u/{username} -> load the page with slug "profile"
        add_rewrite_rule('^u/([^/]+)/?$', 'index.php?pagename=profile&dgdb_user=$matches[1]', 'top');
    }
    public static function qv(array $vars): array { $vars[] = 'dgdb_user'; return $vars; }

    public static function bootstrap(): void {
        if (is_admin()) return;

        // Detect via pretty route OR a direct visit to the "profile" page.
        $slug = (string) get_query_var('dgdb_user');
        $on_profile_page = is_page('profile'); // works even if builder template

        if (!$slug && !$on_profile_page) return;

        // Resolve user: /u/{slug} wins; otherwise use current user on /profile
        if ($slug !== '') {
            $u = get_user_by('slug', sanitize_title_for_query($slug));
        } else {
            $u = wp_get_current_user();
            if (empty($u) || !$u->exists()) return; // not logged in -> let the page show whatever it has
        }
        if (!$u) return;

        self::$is_profile = true;
        self::$user = $u;

        // Enqueue assets now (theme header/footer will load them)
        wp_enqueue_style('dgdb-style', DGDB_URL.'assets/style.css', [], DGDB_VERSION);
        wp_register_script('dgdb-profile-js', DGDB_URL.'assets/profile.js', [], DGDB_VERSION, true);
        wp_enqueue_script('dgdb-profile-js');
        $boot = [
            'ajax'        => admin_url('admin-ajax.php'),
            'nonce'       => wp_create_nonce('dgdb'),
            'viewing_own' => (is_user_logged_in() && get_current_user_id()===$u->ID),
            'user_id'     => $u->ID,
        ];
        wp_add_inline_script('dgdb-profile-js', 'window.DGDB_PROFILE='.wp_json_encode($boot).';', 'before');
        add_action('wp_enqueue_scripts', function () {
            if (is_page('profile')) {
                \DiscGolfDB\Assets::enqueue_for_single_or_profile(); // CSS + collect (safe if unused)
                \DiscGolfDB\Assets::enqueue_profile_js();             // injects window.DGDB + loads assets/profile.js
            }
        }, 20);

    }

    public static function render_content(string $content): string {
        if (!self::$is_profile) return $content;

        // Some themes don’t call the_content(); if they do, we replace it here.
        if (!in_the_loop() || !is_main_query()) return $content;

        $user = self::$user;
        ob_start();
        include DGDB_PATH.'templates/profile.php';
        return ob_get_clean();
    }

    public static function title(array $parts): array {
        if (self::$is_profile && self::$user) {
            $parts['title'] = self::$user->display_name.' – Profile';
        }
        return $parts;
    }

    /* --------------------------------------------------------------------
     * AJAX: Profile update (first successful save awards +250 XP once)
     * Endpoint: action=dgdb_profile_update
     * Expects: nonce (dgdb), user_id, and any of:
     *   - display_name, first_name, last_name, user_url, description
     * Returns: JSON { success, updated_fields:[], message }
     * -------------------------------------------------------------------- */
    public static function ajax_profile_update(): void {
        check_ajax_referer('dgdb', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'Not logged in'], 401);
        }

        $current_user = get_current_user_id();
        $target_user  = (int)($_POST['user_id'] ?? $current_user);

        // Only allow editing your own profile unless you can edit others
        if ($target_user !== $current_user && !current_user_can('edit_users')) {
            wp_send_json_error(['message' => 'Permission denied'], 403);
        }

        $u = get_user_by('ID', $target_user);
        if (!$u || !$u->exists()) {
            wp_send_json_error(['message' => 'User not found'], 404);
        }

        // Collect updates (apply only provided fields)
        $userdata = ['ID' => $target_user];
        $meta_updates = [];
        $updated_fields = [];

        // Core user fields
        if (isset($_POST['display_name'])) {
            $val = trim((string)$_POST['display_name']);
            if ($val !== '' && $val !== $u->display_name) {
                $userdata['display_name'] = sanitize_text_field($val);
                $updated_fields[] = 'display_name';
            }
        }
        if (isset($_POST['first_name'])) {
            $val = trim((string)$_POST['first_name']);
            if ($val !== get_user_meta($target_user, 'first_name', true)) {
                $meta_updates['first_name'] = sanitize_text_field($val);
                $updated_fields[] = 'first_name';
            }
        }
        if (isset($_POST['last_name'])) {
            $val = trim((string)$_POST['last_name']);
            if ($val !== get_user_meta($target_user, 'last_name', true)) {
                $meta_updates['last_name'] = sanitize_text_field($val);
                $updated_fields[] = 'last_name';
            }
        }
        if (isset($_POST['user_url'])) {
            $val = esc_url_raw((string)$_POST['user_url']);
            if ($val !== $u->user_url) {
                $userdata['user_url'] = $val;
                $updated_fields[] = 'user_url';
            }
        }
        // Public bio/description (allow safe HTML)
        if (isset($_POST['description'])) {
            $allowed = [
                'a'      => ['href'=>[], 'title'=>[], 'rel'=>[], 'target'=>[]],
                'em'     => [],
                'strong' => [],
                'br'     => [],
                'p'      => [],
                'ul'     => [],
                'ol'     => [],
                'li'     => [],
            ];
            $val = wp_kses((string)$_POST['description'], $allowed);
            if ($val !== get_user_meta($target_user, 'description', true)) {
                $meta_updates['description'] = $val;
                $updated_fields[] = 'description';
            }
        }

        // If nothing provided, bail early
        if (count($updated_fields) === 0) {
            wp_send_json_success(['updated_fields' => [], 'message' => 'No changes']);
        }

        // Apply core field updates
        $core_ok = true;
        if (count($userdata) > 1) { // beyond ID
            $res = wp_update_user($userdata);
            if (is_wp_error($res)) {
                wp_send_json_error(['message' => 'Update failed: '.$res->get_error_message()], 500);
            }
        }

        // Apply meta updates
        foreach ($meta_updates as $k => $v) {
            update_user_meta($target_user, $k, $v);
        }

        // One-time XP after a successful save
        if (class_exists('\DiscGolfDB\Gamification\XP')) {
            try {
                \DiscGolfDB\Gamification\XP::awardProfileFirstEdit($target_user);
            } catch (\Throwable $e) {
                // Don’t fail the save if XP errors; just log
                error_log('DGDB profile XP error: '.$e->getMessage());
            }
        }

        wp_send_json_success([
            'updated_fields' => $updated_fields,
            'message'        => 'Profile saved',
        ]);
    }
}
