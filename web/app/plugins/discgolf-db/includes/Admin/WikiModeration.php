<?php
namespace DiscGolfDB\Admin;

defined('ABSPATH') || exit;

/**
 * Disc Wiki – Moderation
 * - Lists pending contributions: images, flight, plastics, texts
 * - Approve / Reject
 * - On approval:
 *     * flight -> approved + set active in discgolf_flight_props_active
 *     * text   -> approved + set active in discgolf_text_active (+ award XP to submitter)
 *     * plastic-> approved (+ award XP to submitter)
 *     * image  -> approved (can set sort_order) (+ award XP to submitter)
 */
class WikiModeration {
    private static bool $registered = false;

    public static function register(): void {
        // Back-compat; real menu is added via Page::menu()
    }

    public static function render(): void {
        if (!current_user_can('manage_options')) return;

        global $wpdb;
        $msg = '';

        // ---- Actions ----
        if (!empty($_POST['dgdb_wiki_action']) && check_admin_referer('dgdb_wiki_mod')) {
            $action  = sanitize_text_field($_POST['dgdb_wiki_action']);
            $disc_id = max(1, intval($_POST['disc_id'] ?? 0));
            $row_id  = max(1, intval($_POST['row_id']  ?? 0));

            switch ($action) {
                case 'approve_flight': {
                    // Lookup submitter before state changes (not used for XP at this time)
                    $created_by = (int)$wpdb->get_var($wpdb->prepare(
                        "SELECT created_by FROM {$wpdb->prefix}discgolf_flight_props WHERE id=%d",
                        $row_id
                    ));

                    // Mark approved
                    $wpdb->update($wpdb->prefix.'discgolf_flight_props',
                        ['status'=>'approved'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );
                    // Set active (replace previous)
                    $wpdb->replace($wpdb->prefix.'discgolf_flight_props_active', [
                        'disc_id'         => $disc_id,
                        'flight_props_id' => $row_id,
                    ], ['%d','%d']);

                    $msg = 'Flight numbers approved and set as active.';
                    break;
                }

                case 'reject_flight':
                    $wpdb->update($wpdb->prefix.'discgolf_flight_props',
                        ['status'=>'rejected'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );
                    $msg = 'Flight numbers rejected.';
                    break;

                case 'approve_text': {
                    $type = in_array(($_POST['type'] ?? ''), ['info','history'], true) ? $_POST['type'] : '';
                    if ($type) {
                        // Capture submitter before updating
                        $created_by = (int)$wpdb->get_var($wpdb->prepare(
                            "SELECT created_by FROM {$wpdb->prefix}discgolf_text_suggestions WHERE id=%d",
                            $row_id
                        ));

                        $wpdb->update($wpdb->prefix.'discgolf_text_suggestions',
                            ['status'=>'approved'],
                            ['id'=>$row_id],
                            ['%s'], ['%d']
                        );
                        // Set active text for this type
                        $wpdb->replace($wpdb->prefix.'discgolf_text_active', [
                            'disc_id'       => $disc_id,
                            'type'          => $type,
                            'suggestion_id' => $row_id,
                        ], ['%d','%s','%d']);

                        // Award XP to submitter (once per approved suggestion)
                        if ($created_by > 0 && class_exists('\DiscGolfDB\Gamification\XP')) {
                            if ($type === 'info') {
                                \DiscGolfDB\Gamification\XP::awardWikiInfoApproved($row_id, $created_by);
                            } else { // history
                                \DiscGolfDB\Gamification\XP::awardWikiHistoryApproved($row_id, $created_by);
                            }
                        }

                        $msg = 'Text approved and set as active for ' . esc_html($type) . '.';
                    }
                    break;
                }

                case 'reject_text':
                    $wpdb->update($wpdb->prefix.'discgolf_text_suggestions',
                        ['status'=>'rejected'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );
                    $msg = 'Text suggestion rejected.';
                    break;

                case 'approve_plastic': {
                    $created_by = (int)$wpdb->get_var($wpdb->prepare(
                        "SELECT created_by FROM {$wpdb->prefix}discgolf_plastics WHERE id=%d",
                        $row_id
                    ));

                    $wpdb->update($wpdb->prefix.'discgolf_plastics',
                        ['status'=>'approved'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );

                    // Award XP
                    if ($created_by > 0 && class_exists('\DiscGolfDB\Gamification\XP')) {
                        \DiscGolfDB\Gamification\XP::awardPlasticApproved($row_id, $created_by);
                    }

                    $msg = 'Plastic approved.';
                    break;
                }

                case 'reject_plastic':
                    $wpdb->update($wpdb->prefix.'discgolf_plastics',
                        ['status'=>'rejected'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );
                    $msg = 'Plastic rejected.';
                    break;

                case 'approve_image': {
                    $sort = max(0, intval($_POST['sort_order'] ?? 0));

                    $submitter = (int)$wpdb->get_var($wpdb->prepare(
                        "SELECT user_id FROM {$wpdb->prefix}discgolf_images WHERE id=%d",
                        $row_id
                    ));

                    $wpdb->update($wpdb->prefix.'discgolf_images',
                        ['status'=>'approved','sort_order'=>$sort],
                        ['id'=>$row_id],
                        ['%s','%d'], ['%d']
                    );

                    // Award XP (images table uses user_id as submitter)
                    if ($submitter > 0 && class_exists('\DiscGolfDB\Gamification\XP')) {
                        \DiscGolfDB\Gamification\XP::awardImageApproved($row_id, $submitter);
                    }

                    $msg = 'Image approved.';
                    break;
                }

                case 'reject_image':
                    $wpdb->update($wpdb->prefix.'discgolf_images',
                        ['status'=>'rejected'],
                        ['id'=>$row_id],
                        ['%s'], ['%d']
                    );
                    $msg = 'Image rejected.';
                    break;
            }
        }

        // ---- Pending lists ----
        $pending_flight = $wpdb->get_results("
            SELECT f.*, d.manufacturer, d.name
            FROM {$wpdb->prefix}discgolf_flight_props f
            JOIN {$wpdb->prefix}discgolf_discs d ON d.id=f.disc_id
            WHERE f.status='pending'
            ORDER BY f.created_at ASC
            LIMIT 200
        ", ARRAY_A) ?: [];

        $pending_text = $wpdb->get_results("
            SELECT t.*, d.manufacturer, d.name
            FROM {$wpdb->prefix}discgolf_text_suggestions t
            JOIN {$wpdb->prefix}discgolf_discs d ON d.id=t.disc_id
            WHERE t.status='pending'
            ORDER BY t.created_at ASC
            LIMIT 200
        ", ARRAY_A) ?: [];

        $pending_plastics = $wpdb->get_results("
            SELECT p.*, d.manufacturer, d.name, p.plastic_name as name
            FROM {$wpdb->prefix}discgolf_plastics p
            JOIN {$wpdb->prefix}discgolf_discs d ON d.id=p.disc_id
            WHERE p.status='pending'
            ORDER BY p.created_at ASC
            LIMIT 200
        ", ARRAY_A) ?: [];

        $pending_images = $wpdb->get_results("
            SELECT i.*, d.manufacturer, d.name
            FROM {$wpdb->prefix}discgolf_images i
            JOIN {$wpdb->prefix}discgolf_discs d ON d.id=i.disc_id
            WHERE i.status='pending'
            ORDER BY i.created_at ASC
            LIMIT 200
        ", ARRAY_A) ?: [];

        ?>
        <div class="wrap">
          <h1>Disc Wiki – Moderation</h1>
          <?php if ($msg): ?>
            <div class="notice notice-success"><p><?php echo esc_html($msg); ?></p></div>
          <?php endif; ?>

          <h2>Flight numbers (pending)</h2>
          <?php if (!$pending_flight): ?>
            <p class="description">No pending suggestions.</p>
          <?php else: ?>
            <table class="widefat striped">
              <thead><tr>
                <th>Disc</th><th>Speed</th><th>Glide</th><th>Turn</th><th>Fade</th><th>Sender</th><th>Created</th><th>Action</th>
              </tr></thead>
              <tbody>
              <?php foreach ($pending_flight as $r): ?>
                <tr>
                  <td><strong><?php echo esc_html($r['manufacturer'].' '.$r['name']); ?></strong> (#<?php echo (int)$r['disc_id']; ?>)</td>
                  <td><?php echo esc_html($r['speed']); ?></td>
                  <td><?php echo esc_html($r['glide']); ?></td>
                  <td><?php echo esc_html($r['turn']); ?></td>
                  <td><?php echo esc_html($r['fade']); ?></td>
                  <td><?php echo (int)$r['created_by']; ?></td>
                  <td><?php echo esc_html($r['created_at']); ?></td>
                  <td>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id" value="<?php echo (int)$r['id']; ?>">
                      <button class="button button-primary" name="dgdb_wiki_action" value="approve_flight">Approve & activate</button>
                    </form>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <button class="button" name="dgdb_wiki_action" value="reject_flight">Reject</button>
                    </form>
                  </td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
          <?php endif; ?>

          <h2 style="margin-top:20px;">Texts (pending)</h2>
          <?php if (!$pending_text): ?>
            <p class="description">No pending texts.</p>
          <?php else: ?>
            <table class="widefat striped">
              <thead><tr>
                <th>Disc</th><th>Type</th><th>Text</th><th>Sender</th><th>Created</th><th>Action</th>
              </tr></thead>
              <tbody>
              <?php foreach ($pending_text as $r): ?>
                <tr>
                  <td><strong><?php echo esc_html($r['manufacturer'].' '.$r['name']); ?></strong> (#<?php echo (int)$r['disc_id']; ?>)</td>
                  <td><?php echo esc_html($r['type']); ?></td>
                  <td style="max-width:520px;"><?php echo wp_kses_post($r['content']); ?></td>
                  <td><?php echo (int)$r['created_by']; ?></td>
                  <td><?php echo esc_html($r['created_at']); ?></td>
                  <td>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <input type="hidden" name="type"    value="<?php echo esc_attr($r['type']); ?>">
                      <button class="button button-primary" name="dgdb_wiki_action" value="approve_text">Approve & activate</button>
                    </form>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <button class="button" name="dgdb_wiki_action" value="reject_text">Reject</button>
                    </form>
                  </td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
          <?php endif; ?>

          <h2 style="margin-top:20px;">Plastics (pending)</h2>
          <?php if (!$pending_plastics): ?>
            <p class="description">No pending plastics.</p>
          <?php else: ?>
            <table class="widefat striped">
              <thead><tr>
                <th>Disc</th><th>Name</th><th>Notes</th><th>Sender</th><th>Created</th><th>Action</th>
              </tr></thead>
              <tbody>
              <?php foreach ($pending_plastics as $r): ?>
                <tr>
                  <td><strong><?php echo esc_html($r['manufacturer'].' '.$r['name']); ?></strong> (#<?php echo (int)$r['disc_id']; ?>)</td>
                  <td><?php echo esc_html($r['plastic_name']); ?></td>
                  <td><?php echo esc_html($r['notes']); ?></td>
                  <td><?php echo (int)$r['created_by']; ?></td>
                  <td><?php echo esc_html($r['created_at']); ?></td>
                  <td>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <button class="button button-primary" name="dgdb_wiki_action" value="approve_plastic">Approve</button>
                    </form>
                    <form method="post" style="display:inline">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <button class="button" name="dgdb_wiki_action" value="reject_plastic">Reject</button>
                    </form>
                  </td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
          <?php endif; ?>

          <h2 style="margin-top:20px;">Images (pending)</h2>
          <?php if (!$pending_images): ?>
            <p class="description">No pending images.</p>
          <?php else: ?>
            <table class="widefat striped">
              <thead><tr>
                <th>Disc</th><th>Image</th><th>Attribution</th><th>License</th><th>Sort</th><th>Action</th>
              </tr></thead>
              <tbody>
              <?php foreach ($pending_images as $r): ?>
                <tr>
                  <td><strong><?php echo esc_html($r['manufacturer'].' '.$r['name']); ?></strong> (#<?php echo (int)$r['disc_id']; ?>)</td>
                  <td><img src="<?php echo esc_url($r['image_url']); ?>" alt="" style="max-width:140px;height:auto;border:1px solid #ddd;padding:2px;border-radius:4px;"></td>
                  <td>
                    <?php echo esc_html($r['attribution_text']); ?>
                    <?php if (!empty($r['attribution_url'])): ?>
                      <div><a href="<?php echo esc_url($r['attribution_url']); ?>" target="_blank" rel="noopener">source</a></div>
                    <?php endif; ?>
                  </td>
                  <td><?php echo esc_html($r['license']); ?></td>
                  <td>
                    <form method="post" style="display:flex;gap:6px;align-items:center;">
                      <?php wp_nonce_field('dgdb_wiki_mod'); ?>
                      <input type="hidden" name="disc_id" value="<?php echo (int)$r['disc_id']; ?>">
                      <input type="hidden" name="row_id"  value="<?php echo (int)$r['id']; ?>">
                      <input type="number" name="sort_order" value="<?php echo (int)$r['sort_order']; ?>" style="width:80px;">
                      <button class="button button-primary" name="dgdb_wiki_action" value="approve_image">Approve</button>
                      <button class="button" name="dgdb_wiki_action" value="reject_image">Reject</button>
                    </form>
                  </td>
                  <td></td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
          <?php endif; ?>
        </div>
        <?php
    }
}
