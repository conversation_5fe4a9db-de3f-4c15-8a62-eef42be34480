<?php
namespace DiscGolfDB\Admin;

defined('ABSPATH') || exit;

class BrandPlastics {
    const OPTION = 'dgdb_brand_plastics_map';

    public static function register(): void {
        add_action('admin_menu', [self::class, 'menu']);
    }

    public static function menu(): void {
        add_submenu_page(
            'discgolf-db',
            'Brand Plastics',
            'Brand Plastics',
            'manage_options',
            'dgdb-brand-plastics',
            [self::class, 'render']
        );
    }

    /** Render admin page + handle CSV upload + seeding */
    public static function render(): void {
        if (!current_user_can('manage_options')) return;

        $notice = '';

        // Handle CSV upload/URL
        if (isset($_POST['dgdb_upload_brand_plastics']) && check_admin_referer('dgdb_brand_plastics_upload')) {
            try {
                $csv = '';
                if (!empty($_FILES['csv_file']['name'])) {
                    require_once ABSPATH . 'wp-admin/includes/file.php';
                    $upload = wp_handle_upload($_FILES['csv_file'], ['test_form' => false]);
                    if (!empty($upload['file'])) {
                        $csv = file_get_contents($upload['file']);
                    }
                } elseif (!empty($_POST['csv_url'])) {
                    $url  = esc_url_raw(trim((string)$_POST['csv_url']));
                    $resp = wp_remote_get($url, ['timeout' => 45, 'redirection' => 5]);
                    $csv  = wp_remote_retrieve_body($resp);
                }

                if (!$csv) {
                    $notice = 'No CSV provided.';
                } else {
                    $map = self::parse_csv($csv);
                    update_option(self::OPTION, $map, false);
                    $notice = 'Brand → plastics mapping updated (' . count($map) . ' brands).';
                }
            } catch (\Throwable $e) {
                $notice = 'Upload error: ' . $e->getMessage();
            }
        }

        // Handle seeding
        if (isset($_POST['dgdb_seed_brand_plastics']) && check_admin_referer('dgdb_brand_plastics_seed')) {
            $res = self::seed_into_discs();
            $notice = sprintf(
                'Seeding done. Brands matched: %d · Discs touched: %d · Plastics inserted as pending: %d (skipped duplicates: %d).',
                $res['brands'], $res['discs'], $res['inserted'], $res['skipped']
            );
        }

        $map = get_option(self::OPTION, []);

        ?>
        <div class="wrap">
          <h1>Brand Plastics</h1>

          <?php if ($notice): ?>
            <div class="notice notice-info"><p><?php echo esc_html($notice); ?></p></div>
          <?php endif; ?>

          <h2>Upload / Replace Mapping</h2>
          <p>The CSV should have two columns: <code>brand</code> and <code>plastics</code>. Plastics can be a single value or a list separated by commas or pipes.</p>
          <form method="post" enctype="multipart/form-data" style="margin-bottom:16px;">
            <?php wp_nonce_field('dgdb_brand_plastics_upload'); ?>
            <table class="form-table" role="presentation">
              <tr>
                <th scope="row">CSV File</th>
                <td><input type="file" name="csv_file" accept=".csv" /></td>
              </tr>
              <tr>
                <th scope="row">…or URL</th>
                <td><input type="url" name="csv_url" style="width: 480px;" placeholder="https://…/brand_plastics.csv" /></td>
              </tr>
            </table>
            <?php submit_button('Upload / Replace', 'primary', 'dgdb_upload_brand_plastics'); ?>
          </form>

          <hr />

          <h2>Current Mapping</h2>
          <?php if (empty($map)): ?>
            <p>No mapping stored yet. Upload a CSV above.</p>
          <?php else: ?>
            <table class="widefat striped">
              <thead>
                <tr><th style="width:320px;">Brand</th><th>Plastics</th></tr>
              </thead>
              <tbody>
                <?php foreach ($map as $brand => $plastics): ?>
                  <tr>
                    <td><?php echo esc_html($brand); ?></td>
                    <td><?php echo esc_html(implode(', ', $plastics)); ?></td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>

            <p style="margin-top:12px;">
              Next step: Seed these plastic names onto each disc by brand <em>as pending wiki plastics</em>.
              They will appear in the moderation queue and only show as chips once approved.
            </p>
            <form method="post" onsubmit="return confirm('Seed plastics for matching discs? Existing identical entries are skipped.');">
              <?php wp_nonce_field('dgdb_brand_plastics_seed'); ?>
              <?php submit_button('Seed plastics into discs (pending)', 'secondary', 'dgdb_seed_brand_plastics', false); ?>
            </form>
          <?php endif; ?>
        </div>
        <?php
    }

    /** Parse CSV: returns [ brand => [plastic1, plastic2, ...] ] */
    protected static function parse_csv(string $csv): array {
        $out = [];
        $rows = array_map('rtrim', preg_split('/\R+/', $csv));
        if (!$rows) return $out;

        // detect header
        $has_header = false;
        if (!empty($rows[0])) {
            $first = str_getcsv($rows[0]);
            $h0 = strtolower(trim($first[0] ?? ''));
            $h1 = strtolower(trim($first[1] ?? ''));
            if (in_array($h0, ['brand','manufacturer']) && in_array($h1, ['plastics','plastic','plastic_line','plastic_line_or_variant'])) {
                $has_header = true;
                array_shift($rows);
            }
        }

        foreach ($rows as $line) {
            if ($line === '') continue;
            $cols = str_getcsv($line);
            if (!isset($cols[0], $cols[1])) continue;
            $brand = trim((string)$cols[0]);
            $plast = trim((string)$cols[1]);
            if ($brand === '' || $plast === '') continue;

            $parts = preg_split('/[|,;\/]/', $plast);
            $parts = array_values(array_filter(array_map(function($s){ return trim($s, " \t\n\r\0\x0B"); }, $parts), function($s){ return $s !== ''; }));

            if (!isset($out[$brand])) $out[$brand] = [];
            foreach ($parts as $p) {
                if (!in_array($p, $out[$brand], true)) $out[$brand][] = $p;
            }
        }
        return $out;
    }

    /** Seed mapping into discgolf_plastics table as PENDING */
    protected static function seed_into_discs(): array {
        global $wpdb;

        $map = get_option(self::OPTION, []);
        if (empty($map)) return ['brands'=>0,'discs'=>0,'inserted'=>0,'skipped'=>0];

        // Brand aliasing (normalize CSV brand → DB manufacturer)
        $aliases = [
            'Latitude64'            => 'Latitude 64',
            'Discmania Golf Discs'  => 'Discmania',
        ];

        $td = $wpdb->prefix . 'discgolf_discs';
        $tp = $wpdb->prefix . 'discgolf_plastics';

        $brands_matched = 0;
        $discs_touched  = 0;
        $inserted       = 0;
        $skipped        = 0;

        foreach ($map as $brand => $plastics) {
            $target_brand = $aliases[$brand] ?? $brand;

            // Find discs for this manufacturer
            $disc_ids = $wpdb->get_col($wpdb->prepare("SELECT id FROM $td WHERE manufacturer = %s", $target_brand));
            if (!$disc_ids) continue;

            $brands_matched++;
            $discs_touched += count($disc_ids);

            foreach ($disc_ids as $disc_id) {
                foreach ((array)$plastics as $pname) {
                    $pname = trim((string)$pname);
                    if ($pname === '') continue;

                    // Skip duplicates (case-insensitive) regardless of current status
                    $exists = (int)$wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM $tp WHERE disc_id=%d AND LOWER(plastic_name)=LOWER(%s)",
                        $disc_id, $pname
                    ));
                    if ($exists > 0) { $skipped++; continue; }

                    $ok = $wpdb->insert($tp, [
                        'disc_id'      => (int)$disc_id,
                        'plastic_name' => $pname,
                        'notes'        => null,
                        'source'       => 'seed:brand-map',
                        'status'       => 'pending', // << seed as pending (not approved)
                        'created_by'   => get_current_user_id() ?: null,
                        'approved_by'  => null,
                        'created_at'   => current_time('mysql'),
                        'approved_at'  => null,
                    ], [
                        '%d','%s','%s','%s','%s','%d','%d','%s','%s'
                    ]);

                    if ($ok !== false) $inserted++;
                }
            }
        }

        return [
            'brands'   => $brands_matched,
            'discs'    => $discs_touched,
            'inserted' => $inserted,
            'skipped'  => $skipped,
        ];
    }
}
