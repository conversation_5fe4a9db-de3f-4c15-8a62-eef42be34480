<?php
namespace DiscGolfDB\Admin;

use DiscGolfDB\DB;
use DiscGolfDB\Importer;

defined('ABSPATH') || exit;

class Page {
    private static bool $registered = false;

    public static function register(): void {
        if (self::$registered) {
            return;
        }

        self::$registered = true;
        add_action('admin_menu', [self::class, 'menu']);
    }

    public static function menu(): void {
        add_menu_page(
            'Discs DB',
            'Discs DB',
            'manage_options',
            'discgolf-db',
            [self::class, 'render'],
            'dashicons-database',
            58
        );

        // Add the wiki moderation submenu
        add_submenu_page(
            'discgolf-db',
            'Disc Wiki – Moderation',
            'Disc Wiki – Moderation',
            'manage_options',
            'dgdb-wiki-moderation',
            [\DiscGolfDB\Admin\WikiModeration::class, 'render']
        );

        // Add validation test submenu (only in debug mode)
        if (defined('WP_DEBUG') && WP_DEBUG) {
            add_submenu_page(
                'discgolf-db',
                'System Validation',
                'System Validation',
                'manage_options',
                'dgdb-validation',
                [self::class, 'render_validation_page']
            );
        }
    }

    public static function render(): void {
        if (!current_user_can('manage_options')) return;

        $msg = '';

        /* -----------------------------
         * Rensa
         * ----------------------------- */
        if (isset($_POST['dgdb_wipe_discs_keep_reviews']) && check_admin_referer('dgdb_wipe_discs_keep_reviews')) {
            DB::backupReviewsByKey();
            DB::truncateDiscsAndImages();
            $msg = 'Discs & images cleaned. Reviews have been secured and will be restored after the next import.';
        }

        if (isset($_POST['dgdb_wipe_all']) && check_admin_referer('dgdb_wipe_all')) {
            DB::wipeAll();
            $msg = 'All tables have been cleared.';
        }

        /* -----------------------------
         * Import
         * ----------------------------- */
        if (isset($_POST['dgdb_import_submit']) && check_admin_referer('dgdb_import')) {
            try {
                if (!empty($_FILES['csv_file']['name'])) {
                    require_once ABSPATH . 'wp-admin/includes/file.php';
                    $upload = wp_handle_upload($_FILES['csv_file'], ['test_form' => false]);
                    if (!empty($upload['file'])) {
                        $msg = Importer::import_csv(file_get_contents($upload['file']));
                    } else {
                        $msg = 'Upload failed.';
                    }
                } elseif (!empty($_POST['csv_url'])) {
                    $url  = esc_url_raw(trim((string)$_POST['csv_url']));
                    $resp = wp_remote_get($url, ['timeout' => 45, 'redirection' => 5]);
                    $body = wp_remote_retrieve_body($resp);
                    $msg  = $body ? Importer::import_csv($body) : 'Failed to fetch from URL.';
                } else {
                    $msg = 'Select CSV or enter URL.';
                }
            } catch (\Throwable $e) {
                $msg = 'Import error: ' . $e->getMessage();
            }
        }

        ?>
        <div class="wrap">
          <h1>DiscGolf Discs – Import & Maintenance</h1>

          <?php if ($msg): ?>
            <div class="notice notice-info"><p><?php echo esc_html($msg); ?></p></div>
          <?php endif; ?>

          <!-- =======================
               Import
               ======================= -->
          <h2>Import</h2>
          <p>Upload your PDGA CSV or enter a URL to the CSV file. The import creates/updates discs and reconnects previous reviews.</p>

          <form method="post" enctype="multipart/form-data">
            <?php wp_nonce_field('dgdb_import'); ?>
            <table class="form-table" role="presentation">
              <tr>
                <th scope="row">CSV File</th>
                <td><input type="file" name="csv_file" accept=".csv" /></td>
              </tr>
              <tr>
                <th scope="row">…or URL</th>
                <td><input type="url" name="csv_url" style="width: 480px;" placeholder="https://…/pdga.csv" /></td>
              </tr>
            </table>
            <?php submit_button('Import', 'primary', 'dgdb_import_submit'); ?>
          </form>

          <hr />

          <!-- =======================
               Clean
               ======================= -->
          <h2>Clean</h2>
          <p>Tools to quickly start over. Use with caution.</p>

          <form method="post" onsubmit="return confirm('Are you sure? Discs & images will be deleted, reviews will be reconnected after the next import.');" style="display:inline-block;margin-right:12px;">
            <?php wp_nonce_field('dgdb_wipe_discs_keep_reviews'); ?>
            <?php submit_button('Clean discs (keep reviews)', 'secondary', 'dgdb_wipe_discs_keep_reviews', false); ?>
          </form>

          <form method="post" onsubmit="return confirm('Are you completely sure? This will delete EVERYTHING including reviews.');" style="display:inline-block;">
            <?php wp_nonce_field('dgdb_wipe_all'); ?>
            <?php submit_button('Clean EVERYTHING', 'delete', 'dgdb_wipe_all', false); ?>
          </form>
        </div>
        <?php
    }

    /**
     * Render validation test page
     */
    public static function render_validation_page(): void {
        if (!current_user_can('manage_options')) return;

        $test_results = [];
        $run_tests = false;

        // Handle test execution
        if (isset($_POST['run_tests']) && check_admin_referer('dgdb_run_tests')) {
            $test_results = \DiscGolfDB\Core\ValidationTest::run_all_tests();
            $run_tests = true;
        }

        ?>
        <div class="wrap">
            <h1>DiscGolf DB - System Validation</h1>

            <div class="notice notice-info">
                <p><strong>Note:</strong> This validation page is only available in debug mode and helps ensure that recent changes haven't broken core functionality.</p>
            </div>

            <form method="post" action="">
                <?php wp_nonce_field('dgdb_run_tests'); ?>
                <p>
                    <input type="submit" name="run_tests" class="button button-primary" value="Run Validation Tests" />
                </p>
            </form>

            <?php if ($run_tests && !empty($test_results)): ?>
                <?php if (isset($test_results['error'])): ?>
                    <div class="notice notice-error">
                        <p><?php echo esc_html($test_results['error']); ?></p>
                    </div>
                <?php else: ?>
                    <?php $summary = \DiscGolfDB\Core\ValidationTest::get_test_summary($test_results); ?>

                    <div class="notice notice-<?php echo $summary['failed'] > 0 ? 'error' : 'success'; ?>">
                        <p><strong>Test Summary:</strong>
                            <?php echo $summary['passed']; ?> passed,
                            <?php echo $summary['failed']; ?> failed,
                            <?php echo $summary['warnings']; ?> warnings
                            (<?php echo $summary['success_rate']; ?>% success rate)
                        </p>
                    </div>

                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th>Test</th>
                                <th>Status</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($test_results as $result): ?>
                                <tr>
                                    <td><?php echo esc_html($result['test']); ?></td>
                                    <td>
                                        <span class="status-<?php echo strtolower($result['status']); ?>">
                                            <?php echo esc_html($result['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html($result['message']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <style>
                        .status-pass { color: #46b450; font-weight: bold; }
                        .status-fail { color: #dc3232; font-weight: bold; }
                        .status-warning { color: #ffb900; font-weight: bold; }
                    </style>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        <?php
    }
}
