<?php
namespace DiscGolfDB\Admin;

use DiscGolfDB\Gamification\XP;

defined('ABSPATH') || exit;

class Gamification {
    public static function register(): void {
        add_action('admin_menu', [self::class, 'menu']);
    }

    public static function menu(): void {
        add_submenu_page(
            'discgolf-db',
            'Gamification',
            'Gamification',
            'manage_options',
            'dgdb-gamification',
            [self::class, 'render']
        );
    }

    public static function render(): void {
        if (!current_user_can('manage_options')) return;

        $notice = '';
        $rules  = get_option(XP::OPT_RULES, XP::default_rules());
        $thr    = get_option(XP::OPT_RANKS, []);

        // Save rules
        if (isset($_POST['dgdb_save_rules']) && check_admin_referer('dgdb_save_rules')) {
            $new = $rules;
            $new['review']['base']          = max(0, (int)($_POST['r_base'] ?? 10));
            $new['review']['title']         = max(0, (int)($_POST['r_title'] ?? 5));
            $new['review']['text_per_50']   = max(0, (int)($_POST['r_text50'] ?? 1));
            $new['review']['text_cap']      = max(0, (int)($_POST['r_textcap'] ?? 40));
            $new['review']['min_text']      = max(0, (int)($_POST['r_mintxt'] ?? 60));
            $new['review']['fn_all']        = max(0, (int)($_POST['r_fnall'] ?? 15));
            $new['review']['fn_each']       = max(0, (int)($_POST['r_fneach'] ?? 4));
            $new['review']['plastic']       = max(0, (int)($_POST['r_plastic'] ?? 6));
            $new['review']['weight']        = max(0, (int)($_POST['r_weight'] ?? 4));
            $new['review']['image']         = max(0, (int)($_POST['r_image'] ?? 12));
            $new['review']['first_on_disc'] = max(0, (int)($_POST['r_first'] ?? 10));
            $new['review']['max_per_review']= max(0, (int)($_POST['r_maxrev'] ?? 70));
            $new['review']['daily_cap']     = max(0, (int)($_POST['r_daily'] ?? 250));
            update_option(XP::OPT_RULES, $new, false);
            $rules = $new;
            $notice = 'Scoring rules saved.';
        }

        // Save ranks
        if (isset($_POST['dgdb_save_ranks']) && check_admin_referer('dgdb_save_ranks')) {
            global $wpdb;
            $t = $wpdb->prefix . 'discgolf_ranks';
            foreach ($_POST['rank'] ?? [] as $id => $row) {
                $id   = (int)$id;
                $slug = sanitize_title($row['slug'] ?? '');
                $lbl  = sanitize_text_field($row['label'] ?? '');
                $min  = max(0, (int)($row['min'] ?? 0));
                $sort = $id;
                $wpdb->replace($t, [
                    'id'     => $id, 'slug'=>$slug, 'label'=>$lbl, 'min_xp'=>$min, 'perks'=>null, 'sort'=>$sort
                ], ['%d','%s','%s','%d','%s','%d']);
            }
            // refresh thresholds cache
            $rows = $wpdb->get_results("SELECT id, min_xp FROM $t ORDER BY sort ASC", ARRAY_A);
            $thr  = [];
            foreach ($rows as $r) $thr[(int)$r['id']] = (int)$r['min_xp'];
            update_option(XP::OPT_RANKS, $thr, false);

            // NEW: recompute all user levels to reflect new thresholds immediately
            if (class_exists('\DiscGolfDB\Gamification\XP')) {
                $updated = \DiscGolfDB\Gamification\XP::recompute_all_levels();
                $notice = 'Ranks saved. Recalculated levels for '.$updated.' user(s).';
            } else {
                $notice = 'Ranks saved.';
            }
        }

        // Fetch current ranks to display
        global $wpdb;
        $ranks = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}discgolf_ranks ORDER BY sort ASC", ARRAY_A);

        ?>
        <div class="wrap">
          <h1>Gamification</h1>
          <?php if ($notice): ?>
            <div class="notice notice-success"><p><?php echo esc_html($notice); ?></p></div>
          <?php endif; ?>

          <h2 class="title">Scoring Rules</h2>
          <form method="post">
            <?php wp_nonce_field('dgdb_save_rules'); ?>
            <table class="form-table">
              <tr><th>Base (review)</th><td><input type="number" name="r_base" value="<?php echo (int)$rules['review']['base']; ?>"></td></tr>
              <tr><th>Title</th><td><input type="number" name="r_title" value="<?php echo (int)$rules['review']['title']; ?>"></td></tr>
              <tr><th>Text / 50 chars</th><td><input type="number" name="r_text50" value="<?php echo (int)$rules['review']['text_per_50']; ?>"></td></tr>
              <tr><th>Text cap</th><td><input type="number" name="r_textcap" value="<?php echo (int)$rules['review']['text_cap']; ?>"></td></tr>
              <tr><th>Min text chars</th><td><input type="number" name="r_mintxt" value="<?php echo (int)$rules['review']['min_text']; ?>"></td></tr>
              <tr><th>Flight numbers (all 4)</th><td><input type="number" name="r_fnall" value="<?php echo (int)$rules['review']['fn_all']; ?>"></td></tr>
              <tr><th>Flight numbers (each)</th><td><input type="number" name="r_fneach" value="<?php echo (int)$rules['review']['fn_each']; ?>"></td></tr>
              <tr><th>Plastic</th><td><input type="number" name="r_plastic" value="<?php echo (int)$rules['review']['plastic']; ?>"></td></tr>
              <tr><th>Weight</th><td><input type="number" name="r_weight" value="<?php echo (int)$rules['review']['weight']; ?>"></td></tr>
              <tr><th>Image</th><td><input type="number" name="r_image" value="<?php echo (int)$rules['review']['image']; ?>"></td></tr>
              <tr><th>First review on disc</th><td><input type="number" name="r_first" value="<?php echo (int)$rules['review']['first_on_disc']; ?>"></td></tr>
              <tr><th>Max per review</th><td><input type="number" name="r_maxrev" value="<?php echo (int)$rules['review']['max_per_review']; ?>"></td></tr>
              <tr><th>Daily cap (per user)</th><td><input type="number" name="r_daily" value="<?php echo (int)$rules['review']['daily_cap']; ?>"></td></tr>
            </table>
            <?php submit_button('Save Rules', 'primary', 'dgdb_save_rules'); ?>
          </form>

          <hr />

          <h2 class="title">Ranks</h2>
          <form method="post">
            <?php wp_nonce_field('dgdb_save_ranks'); ?>
            <table class="widefat striped">
              <thead><tr><th>#</th><th>Slug</th><th>Label</th><th>Min XP</th></tr></thead>
              <tbody>
              <?php foreach ($ranks as $row): ?>
                <tr>
                  <td><?php echo (int)$row['id']; ?></td>
                  <td><input type="text" name="rank[<?php echo (int)$row['id']; ?>][slug]" value="<?php echo esc_attr($row['slug']); ?>"></td>
                  <td><input type="text" name="rank[<?php echo (int)$row['id']; ?>][label]" value="<?php echo esc_attr($row['label']); ?>"></td>
                  <td><input type="number" name="rank[<?php echo (int)$row['id']; ?>][min]" value="<?php echo (int)$row['min_xp']; ?>"></td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
            <?php submit_button('Save Ranks', 'secondary', 'dgdb_save_ranks'); ?>
          </form>
        </div>
        <?php
    }
}
