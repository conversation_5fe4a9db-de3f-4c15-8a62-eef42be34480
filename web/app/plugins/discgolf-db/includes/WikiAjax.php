<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

/**
 * AJAX endpoints för Disc Wiki:
 *  - dgdb_disc_wiki           (GET: hämta galleri/flight/plast/info/historia)
 *  - dgdb_submit_wiki_image   (POST: ladda upp bild → pending)
 *  - dgdb_submit_wiki_flight  (POST: flight numbers → pending)
 *  - dgdb_submit_wiki_plastic (POST: plast → pending)
 *  - dgdb_submit_wiki_text    (POST: info/historia → pending)
 */
class WikiAjax {

    public static function register(): void {
        add_action('wp_ajax_dgdb_disc_wiki',        [self::class, 'get_disc_wiki']);
        add_action('wp_ajax_nopriv_dgdb_disc_wiki', [self::class, 'get_disc_wiki']);

        add_action('wp_ajax_dgdb_submit_wiki_image',  [self::class, 'submit_image']);
        add_action('wp_ajax_dgdb_submit_wiki_flight', [self::class, 'submit_flight']);
        add_action('wp_ajax_dgdb_submit_wiki_plastic',[self::class, 'submit_plastic']);
        add_action('wp_ajax_dgdb_submit_wiki_text',   [self::class, 'submit_text']);
    }

    /* ----------------------------------------------------------
     * GET: disc wiki-data
     * ---------------------------------------------------------- */
    public static function get_disc_wiki(): void {
        $disc_id = (int)($_GET['disc_id'] ?? 0);
        if ($disc_id <= 0) wp_send_json_error(['message' => 'Invalid disc_id'], 400);

        global $wpdb;

        $disc = $wpdb->get_row($wpdb->prepare(
            "SELECT id, manufacturer, name, type FROM {$wpdb->prefix}discgolf_discs WHERE id=%d",
            $disc_id
        ), ARRAY_A);
        if (!$disc) wp_send_json_error(['message' => 'Disc not found'], 404);

        // Galleri (approved, max 10)
        $gallery = $wpdb->get_results($wpdb->prepare(
            "SELECT id, image_url, attribution_text, attribution_url, license
             FROM {$wpdb->prefix}discgolf_images
             WHERE disc_id=%d AND status='approved'
             ORDER BY sort_order ASC, created_at ASC
             LIMIT 10",
            $disc_id
        ), ARRAY_A) ?: [];

        // Aktiva flight numbers
        $flight = $wpdb->get_row($wpdb->prepare(
            "SELECT f.speed, f.glide, f.turn, f.fade
               FROM {$wpdb->prefix}discgolf_flight_props_active a
               JOIN {$wpdb->prefix}discgolf_flight_props f ON f.id=a.flight_props_id
              WHERE a.disc_id=%d",
            $disc_id
        ), ARRAY_A);

        // Plaster (approved)
        $plastics = $wpdb->get_results($wpdb->prepare(
            "SELECT id, plastic_name as name, notes
               FROM {$wpdb->prefix}discgolf_plastics
              WHERE disc_id=%d AND status='approved'
              ORDER BY plastic_name ASC",
            $disc_id
        ), ARRAY_A) ?: [];

        // Aktiv info/historia
        $info = $wpdb->get_var($wpdb->prepare(
            "SELECT s.content
               FROM {$wpdb->prefix}discgolf_text_active a
               JOIN {$wpdb->prefix}discgolf_text_suggestions s ON s.id=a.suggestion_id
              WHERE a.disc_id=%d AND a.type='info'",
            $disc_id
        ));
        $history = $wpdb->get_var($wpdb->prepare(
            "SELECT s.content
               FROM {$wpdb->prefix}discgolf_text_active a
               JOIN {$wpdb->prefix}discgolf_text_suggestions s ON s.id=a.suggestion_id
              WHERE a.disc_id=%d AND a.type='history'",
            $disc_id
        ));

        wp_send_json_success([
            'disc'          => $disc,
            'gallery'       => $gallery,
            'flight'        => $flight,
            'plastics'      => $plastics,
            'info'          => $info,
            'history'       => $history,
            'can_contribute'=> is_user_logged_in(),
        ]);
    }

    /* ----------------------------------------------------------
     * POST: bild (pending)
     * ---------------------------------------------------------- */
    public static function submit_image(): void {
        self::require_login_and_nonce('dgdb_wiki_nonce');

        // Rate limiting - prevent spam image uploads
        if (!\DiscGolfDB\Core\Security::check_rate_limit('wiki_image_upload', 30)) {
            wp_send_json_error(['message' => 'För många uppladdningar. Vänta 30 sekunder.'], 429);
        }

        $disc_id = (int)($_POST['disc_id'] ?? 0);
        if ($disc_id <= 0) wp_send_json_error(['message' => 'Invalid disc_id'], 400);

        global $wpdb;

        if (empty($_FILES['image']['name'])) {
            wp_send_json_error(['message' => 'Ingen bild vald'], 400);
        }

        // Use secure file upload handler
        $upload_result = \DiscGolfDB\Core\FileUpload::handle_image_upload($_FILES['image']);

        if (!$upload_result['success']) {
            wp_send_json_error(['message' => $upload_result['error']], 400);
        }

        $license  = sanitize_text_field($_POST['license'] ?? '');
        $attr_tx  = sanitize_text_field($_POST['attribution_text'] ?? '');
        $attr_url = esc_url_raw($_POST['attribution_url'] ?? '');

        $ok = $wpdb->insert($wpdb->prefix.'discgolf_images', [
            'disc_id'          => $disc_id,
            'user_id'          => get_current_user_id(),
            'image_url'        => esc_url_raw($upload_result['url']),
            'source_url'       => '',
            'license'          => $license,
            'attribution_text' => $attr_tx,
            'attribution_url'  => $attr_url,
            'status'           => 'pending',
            'sort_order'       => 0,
            'created_at'       => current_time('mysql'),
        ], ['%d','%d','%s','%s','%s','%s','%s','%s','%d','%s']);

        if ($ok === false) {
            error_log('DGDB submit_image DB error: '.$wpdb->last_error);
            wp_send_json_error(['message'=>'DB-fel vid sparande (image).'], 500);
        }

        wp_send_json_success(['message' => 'Tack! Bilden väntar på granskning.']);
    }

    /* ----------------------------------------------------------
     * POST: flight numbers (pending)
     * ---------------------------------------------------------- */
    public static function submit_flight(): void {
        self::require_login_and_nonce('dgdb_wiki_nonce');

        $disc_id = (int)($_POST['disc_id'] ?? 0);
        $speed = isset($_POST['speed']) ? floatval($_POST['speed']) : null;
        $glide = isset($_POST['glide']) ? floatval($_POST['glide']) : null;
        $turn  = isset($_POST['turn'])  ? floatval($_POST['turn'])  : null;
        $fade  = isset($_POST['fade'])  ? floatval($_POST['fade'])  : null;
        if ($disc_id<=0 || $speed===null || $glide===null || $turn===null || $fade===null) {
            wp_send_json_error(['message'=>'Ogiltiga värden'], 400);
        }

        global $wpdb;
        $ok = $wpdb->insert($wpdb->prefix.'discgolf_flight_props', [
            'disc_id'    => $disc_id,
            'created_by' => get_current_user_id(),
            'speed'      => $speed,
            'glide'      => $glide,
            'turn'       => $turn,
            'fade'       => $fade,
            'status'     => 'pending',
            'created_at' => current_time('mysql'),
        ], ['%d','%d','%f','%f','%f','%f','%s','%s']);

        if ($ok === false) {
            error_log('DGDB submit_flight DB error: '.$wpdb->last_error);
            wp_send_json_error(['message'=>'DB-fel vid sparande (flight).'], 500);
        }

        wp_send_json_success(['message'=>'Tack! Flight numbers väntar på granskning.']);
    }

    /* ----------------------------------------------------------
     * POST: plast (pending)
     * ---------------------------------------------------------- */
    public static function submit_plastic(): void {
        self::require_login_and_nonce('dgdb_wiki_nonce');

        $disc_id = (int)($_POST['disc_id'] ?? 0);
        $name    = trim((string)($_POST['name'] ?? ''));
        $notes   = sanitize_text_field($_POST['notes'] ?? '');
        if ($disc_id<=0 || $name==='') wp_send_json_error(['message'=>'Ogiltiga värden'], 400);

        global $wpdb;
        $ok = $wpdb->insert($wpdb->prefix.'discgolf_plastics', [
            'disc_id'      => $disc_id,
            'created_by'   => get_current_user_id(),
            'plastic_name' => $name,
            'notes'        => $notes,
            'status'       => 'pending',
            'created_at'   => current_time('mysql'),
        ], ['%d','%d','%s','%s','%s','%s']);

        if ($ok === false) {
            error_log('DGDB submit_plastic DB error: '.$wpdb->last_error);
            wp_send_json_error(['message'=>'DB-fel vid sparande (plastic).'], 500);
        }

        wp_send_json_success(['message'=>'Tack! Plast-varianten väntar på granskning.']);
    }

    /* ----------------------------------------------------------
     * POST: info/historia (pending)
     * ---------------------------------------------------------- */
    public static function submit_text(): void {
        self::require_login_and_nonce('dgdb_wiki_nonce');

        // Rate limiting - prevent spam text submissions
        if (!\DiscGolfDB\Core\Security::check_rate_limit('wiki_text_submit', 60)) {
            wp_send_json_error(['message' => 'För många inlämningar. Vänta 1 minut.'], 429);
        }

        $disc_id = (int)($_POST['disc_id'] ?? 0);
        $type    = ($_POST['type'] ?? '');
        $content = trim((string)($_POST['content'] ?? ''));
        if ($disc_id<=0 || !in_array($type, ['info','history'], true) || $content==='') {
            wp_send_json_error(['message'=>'Ogiltiga värden'], 400);
        }

        global $wpdb;
        $ok = $wpdb->insert($wpdb->prefix.'discgolf_text_suggestions', [
            'disc_id'    => $disc_id,
            'created_by' => get_current_user_id(),
            'type'       => $type,
            'content'    => wp_kses_post($content),
            'status'     => 'pending',
            'created_at' => current_time('mysql'),
        ], ['%d','%d','%s','%s','%s','%s']);

        if ($ok === false) {
            error_log('DGDB submit_text DB error: '.$wpdb->last_error);
            wp_send_json_error(['message'=>'DB-fel vid sparande (text).'], 500);
        }

        wp_send_json_success(['message'=>'Tack! Texten väntar på granskning.']);
    }

    /* ---------------------------------------------------------- */
    protected static function require_login_and_nonce(string $nonce_key): void {
        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'Du måste vara inloggad.'], 401);
        }
        $nonce = $_POST['nonce'] ?? '';
        if (!$nonce || !wp_verify_nonce($nonce, $nonce_key)) {
            wp_send_json_error(['message' => 'Ogiltig säkerhetsnyckel.'], 403);
        }
    }
}
