<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

/**
 * Minimal “collection” CPT that guarantees 1 row per (user_id, disc_id).
 * Meta:
 *  - disc_id (int)  REQUIRED
 *  - notes   (string)
 */
class CollectionCPT {
    public const SLUG = 'dgdb_collect';

    public static function register(): void {
        add_action('init', [self::class, 'register_cpt']);
    }

    public static function register_cpt(): void {
        register_post_type(self::SLUG, [
            'label'           => 'Collections',
            'public'          => false,
            'show_ui'         => true,
            'show_in_menu'    => 'edit.php?post_type=' . self::SLUG, // keep it tucked away
            'supports'        => ['title', 'author'],
            'capability_type' => 'post',
            'map_meta_cap'    => true,
        ]);
    }

    /**
     * Create or update a collection item for (user_id, disc_id).
     * @param int   $user_id
     * @param int   $disc_id
     * @param array $args ['notes' => string]
     * @return int|\WP_Error post ID
     */
    public static function upsert_item(int $user_id, int $disc_id, array $args = []) {
        if ($user_id <= 0 || $disc_id <= 0) {
            return new \WP_Error('bad_args', 'Missing user_id or disc_id');
        }

        // Find existing post for this (user,disc)
        $existing = get_posts([
            'post_type'      => self::SLUG,
            'author'         => $user_id,
            'post_status'    => 'any',
            'fields'         => 'ids',
            'posts_per_page' => 1,
            'meta_query'     => [[
                'key'   => 'disc_id',
                'value' => $disc_id,
                'compare' => '=',
                'type'  => 'NUMERIC',
            ]],
        ]);

        // Title from discs table (nice to have)
        $title = 'Disc ' . $disc_id;
        global $wpdb;
        $row = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT manufacturer, name FROM {$wpdb->prefix}discgolf_discs WHERE id=%d",
                $disc_id
            ),
            ARRAY_A
        );
        if ($row) {
            $title = trim(($row['manufacturer'] ? $row['manufacturer'] . ' ' : '') . $row['name']);
        }

        if ($existing) {
            $pid = (int) $existing[0];

            // Update title (keep it useful) and notes if provided
            wp_update_post([
                'ID'         => $pid,
                'post_title' => $title,
            ]);

            if (array_key_exists('notes', $args)) {
                update_post_meta($pid, 'notes', (string) $args['notes']);
            }
            // Ensure the disc_id meta is correct
            update_post_meta($pid, 'disc_id', $disc_id);

            // Defensive clean-up: if somehow multiples exist, remove extras
            $dupes = get_posts([
                'post_type'      => self::SLUG,
                'author'         => $user_id,
                'post_status'    => 'any',
                'fields'         => 'ids',
                'posts_per_page' => -1,
                'meta_query'     => [[
                    'key'   => 'disc_id',
                    'value' => $disc_id,
                    'compare' => '=',
                    'type'  => 'NUMERIC',
                ]],
            ]);
            if (count($dupes) > 1) {
                foreach ($dupes as $other_id) {
                    if ((int)$other_id !== $pid) {
                        wp_delete_post((int)$other_id, true);
                    }
                }
            }

            return $pid;
        }

        // Insert new
        $pid = wp_insert_post([
            'post_type'   => self::SLUG,
            'post_status' => 'publish',
            'post_title'  => $title,
            'post_author' => $user_id,
        ], true);

        if (is_wp_error($pid)) return $pid;

        update_post_meta($pid, 'disc_id', $disc_id);
        if (array_key_exists('notes', $args)) {
            update_post_meta($pid, 'notes', (string) $args['notes']);
        }

        return $pid;
    }

    /**
     * Delete all entries for (user_id, disc_id)
     */
    public static function delete_item(int $user_id, int $disc_id): void {
        if ($user_id <= 0 || $disc_id <= 0) return;

        $posts = get_posts([
            'post_type'      => self::SLUG,
            'author'         => $user_id,
            'post_status'    => 'any',
            'fields'         => 'ids',
            'posts_per_page' => -1,
            'meta_query'     => [[
                'key'   => 'disc_id',
                'value' => $disc_id,
                'compare' => '=',
                'type'  => 'NUMERIC',
            ]],
        ]);
        foreach ($posts as $pid) {
            wp_delete_post((int)$pid, true);
        }
    }
}
