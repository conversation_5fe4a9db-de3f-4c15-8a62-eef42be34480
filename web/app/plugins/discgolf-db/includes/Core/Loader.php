<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Plugin loader with lazy loading capabilities
 * Only loads components when they are actually needed
 */
class Loader {
    private static bool $initialized = false;
    private static array $loaded_components = [];

    public static function init(): void {
        if (self::$initialized) {
            return;
        }

        self::$initialized = true;

        // Always load core components
        self::load_core();

        // Load components based on context
        add_action('wp', [self::class, 'load_contextual_components'], 1);
        add_action('admin_init', [self::class, 'load_admin_components']);
        add_action('wp_ajax_nopriv_dg_list_reviews', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dg_add_review', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dg_get_my_review', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dg_list_reviews', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dg_upload_image', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_toggle_collect', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_collection_toggle', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_collection_update', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_profile_update', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_profile_upload', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_disc_wiki', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_nopriv_dgdb_disc_wiki', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_submit_wiki_image', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_submit_wiki_flight', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_submit_wiki_plastic', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dgdb_submit_wiki_text', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_dg_ping', [self::class, 'load_ajax_components']);
        add_action('wp_ajax_nopriv_dg_ping', [self::class, 'load_ajax_components']);
    }

    private static function load_core(): void {
        if (isset(self::$loaded_components['core'])) {
            return;
        }

        // Core components always needed
        \DiscGolfDB\DB::heal();
        \DiscGolfDB\Assets::register();

        self::$loaded_components['core'] = true;
    }

    public static function load_contextual_components(): void {
        if (is_admin()) {
            return; // Admin components loaded separately
        }

        // Check if we're on a disc-related page
        if (self::is_disc_page()) {
            self::load_disc_components();
        }

        if (self::is_profile_page()) {
            self::load_profile_components();
        }

        if (self::has_shortcode()) {
            self::load_shortcode_components();
        }
    }

    public static function load_admin_components(): void {
        if (isset(self::$loaded_components['admin'])) {
            return;
        }

        \DiscGolfDB\Admin\Page::register();
        // WikiModeration submenu is now handled by Page::menu() for proper timing

        self::$loaded_components['admin'] = true;
    }

    public static function load_ajax_components(): void {
        if (isset(self::$loaded_components['ajax'])) {
            return;
        }

        \DiscGolfDB\Ajax\Reviews::register();
        \DiscGolfDB\Ajax\Images::register();
        \DiscGolfDB\Ajax\Debug::register();
        \DiscGolfDB\Ajax\CollectionAjax::register();
        \DiscGolfDB\WikiAjax::register();

        self::$loaded_components['ajax'] = true;
    }

    private static function load_disc_components(): void {
        if (isset(self::$loaded_components['disc'])) {
            return;
        }

        \DiscGolfDB\SingleDiscRouter::register();

        self::$loaded_components['disc'] = true;
    }

    private static function load_profile_components(): void {
        if (isset(self::$loaded_components['profile'])) {
            return;
        }

        \DiscGolfDB\ProfileRouter::register();

        self::$loaded_components['profile'] = true;
    }

    private static function load_shortcode_components(): void {
        if (isset(self::$loaded_components['shortcode'])) {
            return;
        }

        // Shortcodes are now registered early in main plugin file
        // This method is kept for asset loading when shortcodes are detected

        self::$loaded_components['shortcode'] = true;
    }

    private static function is_disc_page(): bool {
        global $wp_query;
        
        // Check for disc URL pattern
        if (get_query_var('dgdb_disc_id')) {
            return true;
        }

        // Check if we're on the disc page
        if (is_page('disc')) {
            return true;
        }

        return false;
    }

    private static function is_profile_page(): bool {
        // Check for profile URL pattern
        if (get_query_var('dgdb_user')) {
            return true;
        }

        // Check if we're on the profile page
        if (is_page('profile')) {
            return true;
        }

        return false;
    }

    private static function has_shortcode(): bool {
        global $post;
        
        if (!$post) {
            return false;
        }

        // Check for our shortcodes in post content
        if (has_shortcode($post->post_content, 'discgolf_discs') ||
            has_shortcode($post->post_content, 'dgdb_register')) {
            return true;
        }

        return false;
    }

    /**
     * Force load all components (for backwards compatibility)
     */
    public static function load_all_components(): void {
        self::load_core();
        self::load_admin_components();
        self::load_ajax_components();
        self::load_disc_components();
        self::load_profile_components();
        self::load_shortcode_components();

        // Legacy CPT registration (kept for migration purposes)
        \DiscGolfDB\CollectionCPT::register();
    }
}
