<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Enhanced file upload validation and security
 * Provides comprehensive validation for image uploads with security checks
 */
class FileUploadValidator {
    
    // Maximum file sizes (in bytes)
    const MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 10MB
    const MAX_PROFILE_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
    
    // Allowed MIME types
    const ALLOWED_IMAGE_TYPES = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    
    // Allowed file extensions
    const ALLOWED_IMAGE_EXTENSIONS = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'webp'
    ];
    
    // Minimum image dimensions
    const MIN_WIDTH = 100;
    const MIN_HEIGHT = 100;
    
    // Maximum image dimensions
    const MAX_WIDTH = 4000;
    const MAX_HEIGHT = 4000;
    
    /**
     * Validate uploaded file for general image uploads
     */
    public static function validate_image_upload(array $file, string $context = 'general'): array {
        // Basic file validation
        $basic_validation = self::validate_basic_file($file);
        if (!$basic_validation['valid']) {
            return $basic_validation;
        }
        
        // Context-specific size limits
        $max_size = ($context === 'profile') ? self::MAX_PROFILE_IMAGE_SIZE : self::MAX_IMAGE_SIZE;
        
        // File size validation
        if ($file['size'] > $max_size) {
            return [
                'valid' => false,
                'error' => 'File size exceeds maximum allowed size (' . self::format_bytes($max_size) . ')',
                'code' => 'FILE_TOO_LARGE'
            ];
        }
        
        // MIME type validation
        $mime_validation = self::validate_mime_type($file);
        if (!$mime_validation['valid']) {
            return $mime_validation;
        }
        
        // File extension validation
        $extension_validation = self::validate_file_extension($file);
        if (!$extension_validation['valid']) {
            return $extension_validation;
        }
        
        // Image dimensions validation
        $dimension_validation = self::validate_image_dimensions($file['tmp_name']);
        if (!$dimension_validation['valid']) {
            return $dimension_validation;
        }
        
        // Security validation
        $security_validation = self::validate_file_security($file);
        if (!$security_validation['valid']) {
            return $security_validation;
        }
        
        return [
            'valid' => true,
            'file_info' => [
                'original_name' => sanitize_file_name($file['name']),
                'size' => $file['size'],
                'mime_type' => $file['type'],
                'dimensions' => $dimension_validation['dimensions']
            ]
        ];
    }
    
    /**
     * Basic file validation (existence, errors, etc.)
     */
    private static function validate_basic_file(array $file): array {
        if (empty($file['name'])) {
            return [
                'valid' => false,
                'error' => 'No file was uploaded',
                'code' => 'NO_FILE'
            ];
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => self::get_upload_error_message($file['error']),
                'code' => 'UPLOAD_ERROR'
            ];
        }
        
        if (!is_uploaded_file($file['tmp_name'])) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload',
                'code' => 'INVALID_UPLOAD'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Validate MIME type
     */
    private static function validate_mime_type(array $file): array {
        // Get MIME type from file content (more reliable than $_FILES)
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $detected_mime = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($detected_mime, self::ALLOWED_IMAGE_TYPES, true)) {
            return [
                'valid' => false,
                'error' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.',
                'code' => 'INVALID_MIME_TYPE'
            ];
        }
        
        // Also check the reported MIME type matches
        if ($file['type'] !== $detected_mime) {
            ErrorHandler::log_security_event('mime_type_mismatch', [
                'reported' => $file['type'],
                'detected' => $detected_mime,
                'filename' => $file['name']
            ]);
        }
        
        return ['valid' => true, 'mime_type' => $detected_mime];
    }
    
    /**
     * Validate file extension
     */
    private static function validate_file_extension(array $file): array {
        $filename = strtolower($file['name']);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        
        if (!in_array($extension, self::ALLOWED_IMAGE_EXTENSIONS, true)) {
            return [
                'valid' => false,
                'error' => 'Invalid file extension. Only ' . implode(', ', self::ALLOWED_IMAGE_EXTENSIONS) . ' files are allowed.',
                'code' => 'INVALID_EXTENSION'
            ];
        }
        
        return ['valid' => true, 'extension' => $extension];
    }
    
    /**
     * Validate image dimensions
     */
    private static function validate_image_dimensions(string $tmp_name): array {
        $image_info = getimagesize($tmp_name);
        
        if ($image_info === false) {
            return [
                'valid' => false,
                'error' => 'Invalid image file or corrupted image',
                'code' => 'INVALID_IMAGE'
            ];
        }
        
        [$width, $height] = $image_info;
        
        if ($width < self::MIN_WIDTH || $height < self::MIN_HEIGHT) {
            return [
                'valid' => false,
                'error' => "Image too small. Minimum dimensions: " . self::MIN_WIDTH . "x" . self::MIN_HEIGHT . " pixels",
                'code' => 'IMAGE_TOO_SMALL'
            ];
        }
        
        if ($width > self::MAX_WIDTH || $height > self::MAX_HEIGHT) {
            return [
                'valid' => false,
                'error' => "Image too large. Maximum dimensions: " . self::MAX_WIDTH . "x" . self::MAX_HEIGHT . " pixels",
                'code' => 'IMAGE_TOO_LARGE'
            ];
        }
        
        return [
            'valid' => true,
            'dimensions' => [
                'width' => $width,
                'height' => $height
            ]
        ];
    }
    
    /**
     * Security validation (malware detection, etc.)
     */
    private static function validate_file_security(array $file): array {
        // Check for PHP code in image files
        $content = file_get_contents($file['tmp_name']);
        
        if ($content === false) {
            return [
                'valid' => false,
                'error' => 'Unable to read uploaded file',
                'code' => 'READ_ERROR'
            ];
        }
        
        // Look for suspicious patterns
        $suspicious_patterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/base64_decode/i',
            '/shell_exec/i',
            '/system\s*\(/i',
            '/exec\s*\(/i'
        ];
        
        foreach ($suspicious_patterns as $pattern) {
            if (preg_match($pattern, $content)) {
                ErrorHandler::log_security_event('malicious_file_upload', [
                    'filename' => $file['name'],
                    'pattern' => $pattern,
                    'user_id' => get_current_user_id()
                ]);
                
                return [
                    'valid' => false,
                    'error' => 'File contains suspicious content and cannot be uploaded',
                    'code' => 'SECURITY_VIOLATION'
                ];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * Get human-readable upload error message
     */
    private static function get_upload_error_message(int $error_code): string {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds the maximum allowed size';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds the form maximum size';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private static function format_bytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Sanitize filename for safe storage
     */
    public static function sanitize_filename(string $filename): string {
        // Remove any path information
        $filename = basename($filename);
        
        // Sanitize using WordPress function
        $filename = sanitize_file_name($filename);
        
        // Additional security: remove any remaining suspicious characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Ensure filename is not empty and has reasonable length
        if (empty($filename) || strlen($filename) > 255) {
            $filename = 'upload_' . time() . '.jpg';
        }
        
        return $filename;
    }
}
