<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Secure file upload handler for the plugin
 * Provides consistent validation and security checks for all file uploads
 */
class FileUpload {
    
    /** Maximum file size in bytes (5MB) */
    private const MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    /** Allowed image MIME types */
    private const ALLOWED_IMAGE_TYPES = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/webp'
    ];
    
    /** Allowed image file extensions */
    private const ALLOWED_IMAGE_EXTENSIONS = [
        'jpg',
        'jpeg',
        'png',
        'gif',
        'webp'
    ];
    
    /**
     * Validate and handle image upload securely
     * 
     * @param array $file $_FILES array element
     * @param array $options Optional configuration
     * @return array Upload result with 'success', 'url', 'error' keys
     */
    public static function handle_image_upload(array $file, array $options = []): array {
        try {
            // Basic validation
            $validation = self::validate_image_file($file, $options);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }
            
            // Handle the upload using WordPress
            require_once ABSPATH . 'wp-admin/includes/file.php';

            $upload_overrides = [
                'test_form' => false,
                'mimes' => self::get_allowed_mimes()
            ];

            $upload_result = wp_handle_upload($file, $upload_overrides);

            if (isset($upload_result['error'])) {
                return [
                    'success' => false,
                    'error' => 'Upload failed: ' . $upload_result['error']
                ];
            }

            // Final validation of uploaded file
            if (empty($upload_result['url']) || empty($upload_result['type'])) {
                return [
                    'success' => false,
                    'error' => 'Upload completed but file information is missing'
                ];
            }

            // Verify MIME type after upload
            if (!in_array($upload_result['type'], self::ALLOWED_IMAGE_TYPES, true)) {
                // Clean up uploaded file
                if (!empty($upload_result['file']) && file_exists($upload_result['file'])) {
                    wp_delete_file($upload_result['file']);
                }
                return [
                    'success' => false,
                    'error' => 'Invalid file type detected after upload'
                ];
            }
            
            return [
                'success' => true,
                'url' => $upload_result['url'],
                'file' => $upload_result['file'],
                'type' => $upload_result['type']
            ];
            
        } catch (\Exception $e) {
            ErrorHandler::log('File upload error', [
                'error' => $e->getMessage(),
                'file' => $file['name'] ?? 'unknown'
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred during upload'
            ];
        }
    }
    
    /**
     * Validate image file before upload
     * 
     * @param array $file $_FILES array element
     * @param array $options Optional configuration
     * @return array Validation result with 'valid' and 'error' keys
     */
    private static function validate_image_file(array $file, array $options = []): array {
        // Check if file was uploaded
        if (empty($file['name']) || empty($file['tmp_name'])) {
            return ['valid' => false, 'error' => 'No file uploaded'];
        }
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $error_message = self::get_upload_error_message($file['error']);
            return ['valid' => false, 'error' => $error_message];
        }
        
        // Check file size
        $max_size = $options['max_size'] ?? self::MAX_FILE_SIZE;
        if ($file['size'] > $max_size) {
            $max_mb = round($max_size / (1024 * 1024), 1);
            return ['valid' => false, 'error' => "File too large. Maximum size: {$max_mb}MB"];
        }
        
        // Check file extension
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_extension, self::ALLOWED_IMAGE_EXTENSIONS, true)) {
            $allowed = implode(', ', self::ALLOWED_IMAGE_EXTENSIONS);
            return ['valid' => false, 'error' => "Invalid file extension. Allowed: {$allowed}"];
        }
        
        // Check MIME type from uploaded file
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mime_type, self::ALLOWED_IMAGE_TYPES, true)) {
            return ['valid' => false, 'error' => 'Invalid file type detected'];
        }
        
        // Additional image validation
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            return ['valid' => false, 'error' => 'File is not a valid image'];
        }
        
        // Check image dimensions if specified
        if (!empty($options['max_width']) || !empty($options['max_height'])) {
            $width = $image_info[0];
            $height = $image_info[1];
            
            if (!empty($options['max_width']) && $width > $options['max_width']) {
                return ['valid' => false, 'error' => "Image width too large. Maximum: {$options['max_width']}px"];
            }
            
            if (!empty($options['max_height']) && $height > $options['max_height']) {
                return ['valid' => false, 'error' => "Image height too large. Maximum: {$options['max_height']}px"];
            }
        }
        
        return ['valid' => true, 'error' => null];
    }
    
    /**
     * Get allowed MIME types for WordPress upload
     */
    private static function get_allowed_mimes(): array {
        // WordPress expects extension => mime_type format
        return [
            'jpg|jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];
    }
    
    /**
     * Convert PHP upload error code to user-friendly message
     */
    private static function get_upload_error_message(int $error_code): string {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds server upload limit';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds form upload limit';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Server error: missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Server error: failed to write file';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload stopped by server extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Sanitize filename for safe storage
     */
    public static function sanitize_filename(string $filename): string {
        // Remove any path information
        $filename = basename($filename);
        
        // Sanitize using WordPress function
        $filename = sanitize_file_name($filename);
        
        // Additional security: remove any remaining dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Ensure filename isn't empty after sanitization
        if (empty($filename)) {
            $filename = 'upload_' . time() . '.jpg';
        }
        
        return $filename;
    }
    
    /**
     * Get file size limit in human readable format
     */
    public static function get_max_file_size_text(): string {
        $max_mb = round(self::MAX_FILE_SIZE / (1024 * 1024), 1);
        return "{$max_mb}MB";
    }
    
    /**
     * Get allowed file types as text
     */
    public static function get_allowed_types_text(): string {
        return implode(', ', self::ALLOWED_IMAGE_EXTENSIONS);
    }
}
