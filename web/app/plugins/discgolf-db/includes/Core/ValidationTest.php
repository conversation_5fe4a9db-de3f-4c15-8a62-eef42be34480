<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Simple validation test to ensure our changes don't break functionality
 * This is not a full test suite but basic smoke tests
 */
class ValidationTest {
    
    private static array $test_results = [];
    
    /**
     * Run all validation tests
     */
    public static function run_all_tests(): array {
        self::$test_results = [];
        
        // Only run tests if user has admin privileges
        if (!current_user_can('manage_options')) {
            return ['error' => 'Admin privileges required for testing'];
        }
        
        self::test_database_schema();
        self::test_security_functions();
        self::test_file_upload_validator();
        self::test_cache_system();
        self::test_input_validation();
        
        return self::$test_results;
    }
    
    /**
     * Test database schema consistency
     */
    private static function test_database_schema(): void {
        global $wpdb;
        
        try {
            // Test that all tables exist
            $tables = [
                'discgolf_discs',
                'discgolf_reviews', 
                'discgolf_images',
                'discgolf_user_collection',
                'discgolf_xp_ledger'
            ];
            
            foreach ($tables as $table) {
                $full_table = $wpdb->prefix . $table;
                $exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table}'");
                
                if ($exists !== $full_table) {
                    self::$test_results[] = [
                        'test' => 'database_schema',
                        'status' => 'FAIL',
                        'message' => "Table {$table} does not exist"
                    ];
                    return;
                }
            }
            
            // Test user_id column consistency in reviews table
            $columns = $wpdb->get_results("SHOW COLUMNS FROM {$wpdb->prefix}discgolf_reviews");
            $user_id_column = null;
            
            foreach ($columns as $column) {
                if ($column->Field === 'user_id') {
                    $user_id_column = $column;
                    break;
                }
            }
            
            if (!$user_id_column) {
                self::$test_results[] = [
                    'test' => 'database_schema',
                    'status' => 'FAIL',
                    'message' => 'user_id column missing from reviews table'
                ];
                return;
            }
            
            // Check if user_id is BIGINT UNSIGNED (our standardization)
            $expected_types = ['bigint unsigned', 'bigint(20) unsigned'];
            if (!in_array(strtolower($user_id_column->Type), $expected_types)) {
                self::$test_results[] = [
                    'test' => 'database_schema',
                    'status' => 'WARNING',
                    'message' => "user_id column type is {$user_id_column->Type}, expected BIGINT UNSIGNED"
                ];
            }
            
            self::$test_results[] = [
                'test' => 'database_schema',
                'status' => 'PASS',
                'message' => 'Database schema validation passed'
            ];
            
        } catch (\Exception $e) {
            self::$test_results[] = [
                'test' => 'database_schema',
                'status' => 'FAIL',
                'message' => 'Database schema test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test security functions
     */
    private static function test_security_functions(): void {
        try {
            // Test rate limiting
            $rate_limit_works = Security::check_rate_limit('test_action', 1);
            if (!$rate_limit_works) {
                self::$test_results[] = [
                    'test' => 'security_functions',
                    'status' => 'FAIL',
                    'message' => 'Rate limiting not working'
                ];
                return;
            }
            
            // Test input sanitization
            $test_input = ['test_field' => '<script>alert("xss")</script>'];
            $rules = ['test_field' => ['type' => 'text', 'required' => true]];
            
            $sanitized = Security::sanitize_user_input($test_input, $rules);
            
            if (strpos($sanitized['test_field'], '<script>') !== false) {
                self::$test_results[] = [
                    'test' => 'security_functions',
                    'status' => 'FAIL',
                    'message' => 'Input sanitization not working properly'
                ];
                return;
            }
            
            // Test SQL validation
            global $wpdb;
            $valid_table = $wpdb->prefix . 'discgolf_discs';
            $invalid_table = 'malicious_table; DROP TABLE users; --';
            
            if (!Security::validate_sql_table_name($valid_table)) {
                self::$test_results[] = [
                    'test' => 'security_functions',
                    'status' => 'FAIL',
                    'message' => 'Valid table name rejected'
                ];
                return;
            }
            
            if (Security::validate_sql_table_name($invalid_table)) {
                self::$test_results[] = [
                    'test' => 'security_functions',
                    'status' => 'FAIL',
                    'message' => 'Invalid table name accepted'
                ];
                return;
            }
            
            self::$test_results[] = [
                'test' => 'security_functions',
                'status' => 'PASS',
                'message' => 'Security functions validation passed'
            ];
            
        } catch (\Exception $e) {
            self::$test_results[] = [
                'test' => 'security_functions',
                'status' => 'FAIL',
                'message' => 'Security functions test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test file upload validator
     */
    private static function test_file_upload_validator(): void {
        try {
            // Test with invalid file data
            $invalid_file = [
                'name' => 'test.php',
                'type' => 'application/x-php',
                'size' => 1000,
                'tmp_name' => '',
                'error' => UPLOAD_ERR_OK
            ];
            
            $result = FileUploadValidator::validate_image_upload($invalid_file);
            
            if ($result['valid']) {
                self::$test_results[] = [
                    'test' => 'file_upload_validator',
                    'status' => 'FAIL',
                    'message' => 'File upload validator accepted invalid file'
                ];
                return;
            }
            
            // Test filename sanitization
            $dangerous_filename = '../../../etc/passwd<script>alert(1)</script>.jpg';
            $sanitized = FileUploadValidator::sanitize_filename($dangerous_filename);
            
            if (strpos($sanitized, '../') !== false || strpos($sanitized, '<script>') !== false) {
                self::$test_results[] = [
                    'test' => 'file_upload_validator',
                    'status' => 'FAIL',
                    'message' => 'Filename sanitization not working properly'
                ];
                return;
            }
            
            self::$test_results[] = [
                'test' => 'file_upload_validator',
                'status' => 'PASS',
                'message' => 'File upload validator working correctly'
            ];
            
        } catch (\Exception $e) {
            self::$test_results[] = [
                'test' => 'file_upload_validator',
                'status' => 'FAIL',
                'message' => 'File upload validator test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test cache system
     */
    private static function test_cache_system(): void {
        try {
            $test_key = 'test_cache_key_' . time();
            $test_value = ['test' => 'data', 'timestamp' => time()];
            
            // Test cache set and get
            $cached_value = QueryCache::get_or_set($test_key, function() use ($test_value) {
                return $test_value;
            }, 60);
            
            if ($cached_value !== $test_value) {
                self::$test_results[] = [
                    'test' => 'cache_system',
                    'status' => 'FAIL',
                    'message' => 'Cache system not storing/retrieving data correctly'
                ];
                return;
            }
            
            // Test cache invalidation
            QueryCache::invalidate($test_key);
            
            // Clean up
            delete_transient("dgdb_cache_{$test_key}");
            
            self::$test_results[] = [
                'test' => 'cache_system',
                'status' => 'PASS',
                'message' => 'Cache system working correctly'
            ];
            
        } catch (\Exception $e) {
            self::$test_results[] = [
                'test' => 'cache_system',
                'status' => 'FAIL',
                'message' => 'Cache system test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test input validation
     */
    private static function test_input_validation(): void {
        try {
            // Test disc ID validation
            $valid_disc_id = InputValidator::validate_disc_id(123);
            if (!$valid_disc_id['valid'] || $valid_disc_id['value'] !== 123) {
                self::$test_results[] = [
                    'test' => 'input_validation',
                    'status' => 'FAIL',
                    'message' => 'Valid disc ID rejected'
                ];
                return;
            }
            
            $invalid_disc_id = InputValidator::validate_disc_id(-1);
            if ($invalid_disc_id['valid']) {
                self::$test_results[] = [
                    'test' => 'input_validation',
                    'status' => 'FAIL',
                    'message' => 'Invalid disc ID accepted'
                ];
                return;
            }
            
            // Test rating validation
            $valid_rating = InputValidator::validate_rating(4);
            if (!$valid_rating['valid'] || $valid_rating['value'] !== 4) {
                self::$test_results[] = [
                    'test' => 'input_validation',
                    'status' => 'FAIL',
                    'message' => 'Valid rating rejected'
                ];
                return;
            }
            
            $invalid_rating = InputValidator::validate_rating(10);
            if ($invalid_rating['valid']) {
                self::$test_results[] = [
                    'test' => 'input_validation',
                    'status' => 'FAIL',
                    'message' => 'Invalid rating accepted'
                ];
                return;
            }
            
            self::$test_results[] = [
                'test' => 'input_validation',
                'status' => 'PASS',
                'message' => 'Input validation working correctly'
            ];
            
        } catch (\Exception $e) {
            self::$test_results[] = [
                'test' => 'input_validation',
                'status' => 'FAIL',
                'message' => 'Input validation test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get test summary
     */
    public static function get_test_summary(array $results): array {
        $total = count($results);
        $passed = count(array_filter($results, fn($r) => $r['status'] === 'PASS'));
        $failed = count(array_filter($results, fn($r) => $r['status'] === 'FAIL'));
        $warnings = count(array_filter($results, fn($r) => $r['status'] === 'WARNING'));
        
        return [
            'total' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'warnings' => $warnings,
            'success_rate' => $total > 0 ? round(($passed / $total) * 100, 2) : 0
        ];
    }
}
