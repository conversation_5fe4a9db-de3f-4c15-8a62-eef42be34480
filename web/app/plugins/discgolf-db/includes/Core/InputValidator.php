<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Input validation and sanitization utility for the plugin
 * Provides consistent validation across all AJAX handlers and forms
 */
class InputValidator {
    
    /**
     * Validate and sanitize disc ID
     * 
     * @param mixed $input Raw input value
     * @return array ['valid' => bool, 'value' => int|null, 'error' => string|null]
     */
    public static function validate_disc_id($input): array {
        $disc_id = intval($input);
        
        if ($disc_id <= 0) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Invalid disc ID'
            ];
        }
        
        return [
            'valid' => true,
            'value' => $disc_id,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize user ID
     * 
     * @param mixed $input Raw input value
     * @return array ['valid' => bool, 'value' => int|null, 'error' => string|null]
     */
    public static function validate_user_id($input): array {
        $user_id = intval($input);
        
        if ($user_id <= 0) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Invalid user ID'
            ];
        }
        
        return [
            'valid' => true,
            'value' => $user_id,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize rating (1-5)
     * 
     * @param mixed $input Raw input value
     * @return array ['valid' => bool, 'value' => int|null, 'error' => string|null]
     */
    public static function validate_rating($input): array {
        $rating = intval($input);
        
        if ($rating < 1 || $rating > 5) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Rating must be between 1 and 5'
            ];
        }
        
        return [
            'valid' => true,
            'value' => $rating,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize flight number (speed, glide, turn, fade)
     * 
     * @param mixed $input Raw input value
     * @param float $min Minimum allowed value
     * @param float $max Maximum allowed value
     * @param float $step Step increment (e.g., 0.5)
     * @return array ['valid' => bool, 'value' => float|null, 'error' => string|null]
     */
    public static function validate_flight_number($input, float $min, float $max, float $step = 0.5): array {
        if ($input === '' || $input === null) {
            return [
                'valid' => true,
                'value' => null,
                'error' => null
            ];
        }
        
        if (!is_numeric($input)) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Flight number must be numeric'
            ];
        }
        
        $value = floatval($input);
        
        if ($value < $min || $value > $max) {
            return [
                'valid' => false,
                'value' => null,
                'error' => "Flight number must be between {$min} and {$max}"
            ];
        }
        
        // Snap to step increment
        if ($step > 0) {
            $value = round($value / $step) * $step;
        }
        
        // Format to avoid floating point precision issues
        $value = floatval(number_format($value, 2, '.', ''));
        
        return [
            'valid' => true,
            'value' => $value,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize disc weight (100-300 grams)
     * 
     * @param mixed $input Raw input value
     * @return array ['valid' => bool, 'value' => int|null, 'error' => string|null]
     */
    public static function validate_disc_weight($input): array {
        if ($input === '' || $input === null) {
            return [
                'valid' => true,
                'value' => null,
                'error' => null
            ];
        }
        
        $weight = intval($input);
        
        if ($weight < 100 || $weight > 300) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Disc weight must be between 100 and 300 grams'
            ];
        }
        
        return [
            'valid' => true,
            'value' => $weight,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize text field
     * 
     * @param mixed $input Raw input value
     * @param int $max_length Maximum allowed length
     * @param bool $required Whether the field is required
     * @return array ['valid' => bool, 'value' => string|null, 'error' => string|null]
     */
    public static function validate_text_field($input, int $max_length = 255, bool $required = false): array {
        $value = sanitize_text_field((string)$input);
        
        if ($required && empty($value)) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'This field is required'
            ];
        }
        
        if (strlen($value) > $max_length) {
            return [
                'valid' => false,
                'value' => null,
                'error' => "Text must be {$max_length} characters or less"
            ];
        }
        
        return [
            'valid' => true,
            'value' => $value,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize textarea field
     * 
     * @param mixed $input Raw input value
     * @param int $max_length Maximum allowed length
     * @param bool $required Whether the field is required
     * @return array ['valid' => bool, 'value' => string|null, 'error' => string|null]
     */
    public static function validate_textarea_field($input, int $max_length = 2000, bool $required = false): array {
        $value = sanitize_textarea_field((string)$input);
        
        if ($required && empty($value)) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'This field is required'
            ];
        }
        
        if (strlen($value) > $max_length) {
            return [
                'valid' => false,
                'value' => null,
                'error' => "Text must be {$max_length} characters or less"
            ];
        }
        
        return [
            'valid' => true,
            'value' => $value,
            'error' => null
        ];
    }
    
    /**
     * Validate and sanitize URL
     * 
     * @param mixed $input Raw input value
     * @param bool $required Whether the field is required
     * @return array ['valid' => bool, 'value' => string|null, 'error' => string|null]
     */
    public static function validate_url($input, bool $required = false): array {
        $value = esc_url_raw((string)$input);
        
        if ($required && empty($value)) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'URL is required'
            ];
        }
        
        if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
            return [
                'valid' => false,
                'value' => null,
                'error' => 'Invalid URL format'
            ];
        }
        
        return [
            'valid' => true,
            'value' => $value,
            'error' => null
        ];
    }
    
    /**
     * Validate multiple fields at once
     * 
     * @param array $validations Array of validation rules
     * @return array ['valid' => bool, 'values' => array, 'errors' => array]
     */
    public static function validate_multiple(array $validations): array {
        $values = [];
        $errors = [];
        $all_valid = true;
        
        foreach ($validations as $field => $validation) {
            $result = $validation;
            
            if (!$result['valid']) {
                $all_valid = false;
                $errors[$field] = $result['error'];
            } else {
                $values[$field] = $result['value'];
            }
        }
        
        return [
            'valid' => $all_valid,
            'values' => $values,
            'errors' => $errors
        ];
    }
    
    /**
     * Validate AJAX request basics (nonce, login)
     * 
     * @param string $nonce_action Nonce action name
     * @param bool $require_login Whether login is required
     * @return array ['valid' => bool, 'error' => string|null]
     */
    public static function validate_ajax_request(string $nonce_action = 'dgdb', bool $require_login = true): array {
        if ($require_login && !is_user_logged_in()) {
            return [
                'valid' => false,
                'error' => 'Authentication required'
            ];
        }
        
        if (!check_ajax_referer($nonce_action, 'nonce', false)) {
            return [
                'valid' => false,
                'error' => 'Invalid security token'
            ];
        }
        
        return [
            'valid' => true,
            'error' => null
        ];
    }
}
