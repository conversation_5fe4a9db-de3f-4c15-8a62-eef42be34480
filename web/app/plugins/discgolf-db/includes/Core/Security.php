<?php

namespace DiscGolfDB\Core;

/**
 * Security helper class for capability checks and permission validation
 */
class Security {
    
    /**
     * Check if current user can manage the plugin (admin operations)
     */
    public static function can_manage_plugin(): bool {
        return current_user_can('manage_options');
    }
    
    /**
     * Check if current user can moderate content (approve/reject submissions)
     */
    public static function can_moderate_content(): bool {
        return current_user_can('moderate_comments') || current_user_can('manage_options');
    }
    
    /**
     * Check if current user can edit others' content
     */
    public static function can_edit_others_content(): bool {
        return current_user_can('edit_others_posts') || current_user_can('manage_options');
    }
    
    /**
     * Check if current user can upload files
     */
    public static function can_upload_files(): bool {
        return current_user_can('upload_files');
    }
    
    /**
     * Check if current user can publish content
     */
    public static function can_publish_content(): bool {
        return current_user_can('publish_posts');
    }
    
    /**
     * Require login and nonce verification for AJAX requests
     */
    public static function require_ajax_auth(string $nonce_action = 'dgdb'): void {
        if (!is_user_logged_in()) {
            wp_send_json_error('Authentication required', 401);
        }
        
        check_ajax_referer($nonce_action, 'nonce');
    }
    
    /**
     * Require specific capability for AJAX requests
     */
    public static function require_capability(string $capability, string $error_message = 'Insufficient permissions'): void {
        if (!current_user_can($capability)) {
            wp_send_json_error($error_message, 403);
        }
    }
    
    /**
     * Check if user can modify specific content (own content or has edit_others capability)
     */
    public static function can_modify_content(int $author_id): bool {
        $current_user_id = get_current_user_id();
        
        // User can modify their own content
        if ($current_user_id === $author_id) {
            return true;
        }
        
        // Or if they have permission to edit others' content
        return self::can_edit_others_content();
    }
    
    /**
     * Validate that user can perform admin operations
     */
    public static function require_admin(): void {
        if (!self::can_manage_plugin()) {
            wp_send_json_error('Admin privileges required', 403);
        }
    }
    
    /**
     * Validate that user can moderate content
     */
    public static function require_moderator(): void {
        if (!self::can_moderate_content()) {
            wp_send_json_error('Moderator privileges required', 403);
        }
    }
    
    /**
     * Rate limiting helper - prevent spam submissions
     */
    public static function check_rate_limit(string $action, int $limit_seconds = 60): bool {
        if (!is_user_logged_in()) {
            // For non-logged-in users, use IP-based rate limiting
            return self::check_ip_rate_limit($action, $limit_seconds);
        }

        $user_id = get_current_user_id();
        $transient_key = "dgdb_rate_limit_{$action}_{$user_id}";

        if (get_transient($transient_key)) {
            self::log_security_event('rate_limit_exceeded', [
                'action' => $action,
                'user_id' => $user_id,
                'limit_seconds' => $limit_seconds
            ]);
            return false; // Rate limited
        }

        // Set rate limit
        set_transient($transient_key, time(), $limit_seconds);
        return true;
    }

    /**
     * IP-based rate limiting for non-authenticated users
     */
    public static function check_ip_rate_limit(string $action, int $limit_seconds = 60): bool {
        $ip = self::get_client_ip();
        $transient_key = "dgdb_ip_rate_limit_{$action}_" . md5($ip);

        if (get_transient($transient_key)) {
            self::log_security_event('ip_rate_limit_exceeded', [
                'action' => $action,
                'ip' => $ip,
                'limit_seconds' => $limit_seconds
            ]);
            return false; // Rate limited
        }

        // Set rate limit
        set_transient($transient_key, time(), $limit_seconds);
        return true;
    }

    /**
     * Get client IP address safely
     */
    public static function get_client_ip(): string {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Sanitize and validate user input with context
     */
    public static function sanitize_input(string $input, string $context = 'text'): string {
        switch ($context) {
            case 'email':
                return sanitize_email($input);
            case 'url':
                return esc_url_raw($input);
            case 'textarea':
                return sanitize_textarea_field($input);
            case 'html':
                return wp_kses_post($input);
            case 'slug':
                return sanitize_title($input);
            case 'number':
                return (string) intval($input);
            case 'float':
                return (string) floatval($input);
            default:
                return sanitize_text_field($input);
        }
    }
    
    /**
     * Log security events for monitoring
     */
    public static function log_security_event(string $event, array $context = []): void {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $user_id = get_current_user_id();
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

            $log_data = [
                'event' => $event,
                'user_id' => $user_id,
                'ip' => $ip,
                'timestamp' => current_time('mysql'),
                'context' => $context
            ];

            error_log('DGDB Security Event: ' . wp_json_encode($log_data));
        }
    }

    /**
     * Enhanced nonce verification with logging
     */
    public static function verify_nonce_with_logging(string $nonce, string $action): bool {
        $valid = wp_verify_nonce($nonce, $action);

        if (!$valid) {
            self::log_security_event('nonce_verification_failed', [
                'action' => $action,
                'provided_nonce' => substr($nonce, 0, 8) . '...' // Log partial nonce for debugging
            ]);
        }

        return $valid;
    }

    /**
     * Create nonce with consistent action naming
     */
    public static function create_nonce(string $action): string {
        return wp_create_nonce($action);
    }

    /**
     * Validate SQL table name to prevent injection
     */
    public static function validate_sql_table_name(string $table_name): bool {
        // Only allow alphanumeric characters, underscores, and WordPress prefix
        global $wpdb;
        $allowed_pattern = '/^' . preg_quote($wpdb->prefix, '/') . '[a-zA-Z0-9_]+$/';
        return preg_match($allowed_pattern, $table_name) === 1;
    }

    /**
     * Validate SQL column name to prevent injection
     */
    public static function validate_sql_column_name(string $column_name): bool {
        // Only allow alphanumeric characters and underscores
        return preg_match('/^[a-zA-Z0-9_]+$/', $column_name) === 1;
    }

    /**
     * Enhanced SQL injection prevention for dynamic queries
     */
    public static function prepare_dynamic_query(string $query, array $table_names = [], array $column_names = []): string {
        // Validate table names
        foreach ($table_names as $placeholder => $table_name) {
            if (!self::validate_sql_table_name($table_name)) {
                throw new \InvalidArgumentException("Invalid table name: {$table_name}");
            }
            $query = str_replace($placeholder, $table_name, $query);
        }

        // Validate column names
        foreach ($column_names as $placeholder => $column_name) {
            if (!self::validate_sql_column_name($column_name)) {
                throw new \InvalidArgumentException("Invalid column name: {$column_name}");
            }
            $query = str_replace($placeholder, $column_name, $query);
        }

        return $query;
    }

    /**
     * Check for suspicious user behavior patterns
     */
    public static function check_suspicious_activity(string $action, array $context = []): bool {
        if (!is_user_logged_in()) {
            return false;
        }

        $user_id = get_current_user_id();
        $activity_key = "dgdb_activity_{$action}_{$user_id}";

        // Get recent activity count
        $recent_activity = get_transient($activity_key) ?: 0;
        $recent_activity++;

        // Define thresholds for different actions
        $thresholds = [
            'review_submit' => 10,    // 10 reviews per hour
            'collection_add' => 50,   // 50 collection adds per hour
            'image_upload' => 20,     // 20 image uploads per hour
            'profile_update' => 5,    // 5 profile updates per hour
        ];

        $threshold = $thresholds[$action] ?? 20; // Default threshold

        if ($recent_activity > $threshold) {
            self::log_security_event('suspicious_activity_detected', array_merge($context, [
                'action' => $action,
                'user_id' => $user_id,
                'activity_count' => $recent_activity,
                'threshold' => $threshold
            ]));
            return true; // Suspicious
        }

        // Update activity counter
        set_transient($activity_key, $recent_activity, HOUR_IN_SECONDS);
        return false;
    }

    /**
     * Validate user permissions for specific disc operations
     */
    public static function can_user_modify_disc(int $user_id, int $disc_id): bool {
        // Admins can modify any disc
        if (user_can($user_id, 'manage_options')) {
            return true;
        }

        // Moderators can modify any disc
        if (user_can($user_id, 'moderate_comments')) {
            return true;
        }

        // Regular users can only modify their own contributions
        // This would need to be implemented based on your business logic
        return false;
    }

    /**
     * Sanitize user input with context-aware validation
     */
    public static function sanitize_user_input(array $input, array $rules): array {
        $sanitized = [];

        foreach ($rules as $field => $rule) {
            if (!isset($input[$field])) {
                if ($rule['required'] ?? false) {
                    throw new \InvalidArgumentException("Required field missing: {$field}");
                }
                continue;
            }

            $value = $input[$field];

            switch ($rule['type']) {
                case 'int':
                    $sanitized[$field] = intval($value);
                    if (isset($rule['min']) && $sanitized[$field] < $rule['min']) {
                        throw new \InvalidArgumentException("Value too small for {$field}");
                    }
                    if (isset($rule['max']) && $sanitized[$field] > $rule['max']) {
                        throw new \InvalidArgumentException("Value too large for {$field}");
                    }
                    break;

                case 'float':
                    $sanitized[$field] = floatval($value);
                    if (isset($rule['min']) && $sanitized[$field] < $rule['min']) {
                        throw new \InvalidArgumentException("Value too small for {$field}");
                    }
                    if (isset($rule['max']) && $sanitized[$field] > $rule['max']) {
                        throw new \InvalidArgumentException("Value too large for {$field}");
                    }
                    break;

                case 'text':
                    $sanitized[$field] = sanitize_text_field($value);
                    if (isset($rule['max_length']) && strlen($sanitized[$field]) > $rule['max_length']) {
                        throw new \InvalidArgumentException("Text too long for {$field}");
                    }
                    break;

                case 'textarea':
                    $sanitized[$field] = sanitize_textarea_field($value);
                    if (isset($rule['max_length']) && strlen($sanitized[$field]) > $rule['max_length']) {
                        throw new \InvalidArgumentException("Text too long for {$field}");
                    }
                    break;

                case 'email':
                    $sanitized[$field] = sanitize_email($value);
                    if (!is_email($sanitized[$field])) {
                        throw new \InvalidArgumentException("Invalid email for {$field}");
                    }
                    break;

                case 'url':
                    $sanitized[$field] = esc_url_raw($value);
                    if (!filter_var($sanitized[$field], FILTER_VALIDATE_URL)) {
                        throw new \InvalidArgumentException("Invalid URL for {$field}");
                    }
                    break;

                default:
                    $sanitized[$field] = sanitize_text_field($value);
            }
        }

        return $sanitized;
    }
}
