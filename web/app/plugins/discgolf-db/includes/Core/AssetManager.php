<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Consolidated Asset Manager with lazy loading and conditional loading
 * Replaces the old Assets.php with better performance and organization
 */
class AssetManager {
    private static bool $registered = false;
    private static array $loaded_contexts = [];

    public static function init(): void {
        if (self::$registered) {
            return;
        }

        add_action('wp_enqueue_scripts', [self::class, 'register_assets'], 1);
        add_action('init', [self::class, 'register_assets'], 1); // Also register early for shortcodes

        // Initialize performance optimizations
        self::init_script_optimization();

        self::$registered = true;
    }

    public static function register_assets(): void {
        $ver = defined('DGDB_VERSION') ? DGDB_VERSION : '1.7.0';

        // Add cache busting in development
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $ver .= '-' . filemtime(DGDB_PATH . 'assets/style.css');
        }

        // Register all assets but don't enqueue yet
        wp_register_style('dgdb-style', DGDB_URL . 'assets/style.css', [], $ver);
        wp_register_script('dgdb-discs', DGDB_URL . 'assets/discs.js', [], $ver, true);
        wp_register_script('dgdb-collect', DGDB_URL . 'assets/collect.js', [], $ver, true);
        wp_register_script('dgdb-disc-wiki', DGDB_URL . 'assets/disc-wiki.js', [], $ver, true);
        wp_register_script('dgdb-profile', DGDB_URL . 'assets/profile.js', [], $ver, true);
        wp_register_script('dgdb-reviews', DGDB_URL . 'assets/reviews.js', [], $ver, true);

        // Add preload hints for critical assets
        add_action('wp_head', [self::class, 'add_preload_hints'], 1);
    }

    /**
     * Load assets for shortcode context (disc grid)
     */
    public static function load_for_shortcode(): void {
        if (isset(self::$loaded_contexts['shortcode'])) {
            return;
        }

        self::ensure_registered();
        
        wp_enqueue_style('dgdb-style');
        
        self::inject_config('dgdb-discs');
        wp_enqueue_script('dgdb-discs');
        
        self::inject_config('dgdb-collect');
        wp_enqueue_script('dgdb-collect');

        self::add_ajax_footer_script();
        self::$loaded_contexts['shortcode'] = true;
    }

    /**
     * Load assets for single disc page
     */
    public static function load_for_single_disc(array $overrides = []): void {
        if (isset(self::$loaded_contexts['single_disc'])) {
            return;
        }

        self::ensure_registered();
        
        wp_enqueue_style('dgdb-style');

        // Disc wiki functionality
        self::inject_config('dgdb-disc-wiki', $overrides);
        wp_enqueue_script('dgdb-disc-wiki');

        // Reviews functionality
        self::inject_config('dgdb-reviews', $overrides);
        wp_enqueue_script('dgdb-reviews');

        // Collection toggle
        self::inject_config('dgdb-collect', $overrides);
        wp_enqueue_script('dgdb-collect');

        wp_enqueue_script(
        'dgdb-flight-chart',
        DGDB_URL . 'assets/flight-chart.js',
        [],
        defined('DGDB_VERSION') ? DGDB_VERSION : '1.0',
        true
        );

        // Wiki-specific configuration
        self::inject_wiki_config($overrides);

        self::add_ajax_footer_script();
        self::$loaded_contexts['single_disc'] = true;

    }

    /**
     * Load assets for profile page
     */
    public static function load_for_profile(array $overrides = []): void {
        if (isset(self::$loaded_contexts['profile'])) {
            return;
        }

        self::ensure_registered();
        
        wp_enqueue_style('dgdb-style');

        // Profile-specific functionality
        self::inject_config('dgdb-profile', $overrides);
        wp_enqueue_script('dgdb-profile');

        // Collection functionality (for profile collections)
        self::inject_config('dgdb-collect', $overrides);
        wp_enqueue_script('dgdb-collect');

        self::add_ajax_footer_script();
        self::$loaded_contexts['profile'] = true;
    }

    /**
     * Inject global DGDB configuration
     */
    private static function inject_config(string $handle, array $overrides = []): void {
        $config = array_merge([
            'ajax' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dgdb'),
            'nonceImages' => wp_create_nonce('dgdb_images'),
            'loggedIn' => is_user_logged_in(),
            'useDiscPage' => true,
            'discBase' => home_url('/disc/'),
        ], $overrides);

        wp_add_inline_script($handle, 'window.DGDB = ' . wp_json_encode($config) . ';', 'before');
    }

    /**
     * Inject wiki-specific configuration
     */
    private static function inject_wiki_config(array $overrides = []): void {
        $config = array_merge([
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dgdb_wiki_nonce'),
            'can_contribute' => is_user_logged_in() ? 1 : 0,
        ], $overrides);

        wp_add_inline_script(
            'dgdb-disc-wiki',
            'window.dgdbWiki = ' . wp_json_encode($config) . ';',
            'before'
        );
    }

    /**
     * Add AJAX URL footer script
     */
    private static function add_ajax_footer_script(): void {
        static $added = false;
        if ($added) {
            return;
        }

        add_action('wp_print_footer_scripts', function () {
            printf(
                '<script data-ajaxurl="%s"></script>',
                esc_url(admin_url('admin-ajax.php'))
            );
        }, 1);

        $added = true;
    }

    /**
     * Ensure assets are registered
     */
    private static function ensure_registered(): void {
        if (!wp_script_is('dgdb-style', 'registered')) {
            self::register_assets();
        }
    }

    /**
     * Get asset dependencies for a context
     */
    public static function get_dependencies(string $context): array {
        $deps = [
            'shortcode' => ['dgdb-style', 'dgdb-discs', 'dgdb-collect'],
            'single_disc' => ['dgdb-style', 'dgdb-disc-wiki', 'dgdb-reviews', 'dgdb-collect'],
            'profile' => ['dgdb-style', 'dgdb-profile', 'dgdb-collect'],
        ];

        return $deps[$context] ?? [];
    }

    /**
     * Check if assets are loaded for a context
     */
    public static function is_loaded(string $context): bool {
        return isset(self::$loaded_contexts[$context]);
    }

    /**
     * Force load all assets (for debugging or backwards compatibility)
     */
    public static function load_all(): void {
        self::load_for_shortcode();
        self::load_for_single_disc();
        self::load_for_profile();
    }

    /**
     * Get loaded contexts (for debugging)
     */
    public static function get_loaded_contexts(): array {
        return array_keys(self::$loaded_contexts);
    }

    /**
     * Add preload hints for critical assets
     */
    public static function add_preload_hints(): void {
        // Only add preload hints if we're loading assets
        if (empty(self::$loaded_contexts)) {
            return;
        }

        $ver = defined('DGDB_VERSION') ? DGDB_VERSION : '1.7.0';

        // Preload critical CSS
        echo '<link rel="preload" href="' . esc_url(DGDB_URL . 'assets/style.css?ver=' . $ver) . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";

        // Preload critical JS based on context
        if (isset(self::$loaded_contexts['shortcode'])) {
            echo '<link rel="preload" href="' . esc_url(DGDB_URL . 'assets/discs.js?ver=' . $ver) . '" as="script">' . "\n";
        }

        if (isset(self::$loaded_contexts['single_disc'])) {
            echo '<link rel="preload" href="' . esc_url(DGDB_URL . 'assets/reviews.js?ver=' . $ver) . '" as="script">' . "\n";
        }
    }

    /**
     * Optimize asset loading with defer/async attributes
     */
    public static function optimize_script_loading(string $tag, string $handle, string $src): string {
        // Add defer to non-critical scripts
        $defer_scripts = ['dgdb-discs', 'dgdb-collect', 'dgdb-disc-wiki', 'dgdb-profile'];

        if (in_array($handle, $defer_scripts, true)) {
            $tag = str_replace(' src', ' defer src', $tag);
        }

        // Add async to reviews script (can load independently)
        if ($handle === 'dgdb-reviews') {
            $tag = str_replace(' src', ' async src', $tag);
        }

        return $tag;
    }

    /**
     * Initialize script optimization
     */
    public static function init_script_optimization(): void {
        add_filter('script_loader_tag', [self::class, 'optimize_script_loading'], 10, 3);
    }
}
