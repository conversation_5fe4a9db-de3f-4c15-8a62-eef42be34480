<?php

namespace DiscGolfDB\Core;

/**
 * Base AJAX handler class to eliminate code duplication
 */
abstract class BaseAjaxHandler {
    
    /**
     * Standard AJAX authentication and validation
     */
    protected static function validate_ajax_request(string $nonce_action = 'dgdb', bool $require_login = true): array {
        // Check nonce
        if (!check_ajax_referer($nonce_action, 'nonce', false)) {
            return [
                'valid' => false,
                'error' => 'Invalid security token',
                'code' => 403
            ];
        }
        
        // Check login if required
        if ($require_login && !is_user_logged_in()) {
            return [
                'valid' => false,
                'error' => 'Authentication required',
                'code' => 401
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Verify ownership of a database record
     */
    protected static function verify_ownership(string $table, int $record_id, string $user_id_column = 'user_id'): bool {
        global $wpdb;
        
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT {$user_id_column} FROM `{$table}` WHERE id = %d",
            $record_id
        ));
        
        return (int)$owner_id === get_current_user_id();
    }
    
    /**
     * Get integer parameter from POST/GET with validation
     */
    protected static function get_int_param(string $key, int $min = 1, int $max = null, string $method = 'POST'): int {
        $source = $method === 'POST' ? $_POST : $_GET;
        $value = max($min, intval($source[$key] ?? 0));
        
        if ($max !== null) {
            $value = min($max, $value);
        }
        
        return $value;
    }
    
    /**
     * Get string parameter from POST/GET with sanitization
     */
    protected static function get_string_param(string $key, string $default = '', string $method = 'POST'): string {
        $source = $method === 'POST' ? $_POST : $_GET;
        return sanitize_text_field($source[$key] ?? $default);
    }
    
    /**
     * Get textarea parameter from POST/GET with sanitization
     */
    protected static function get_textarea_param(string $key, string $default = '', string $method = 'POST'): string {
        $source = $method === 'POST' ? $_POST : $_GET;
        return sanitize_textarea_field($source[$key] ?? $default);
    }
    
    /**
     * Validate required parameters
     */
    protected static function validate_required_params(array $params, string $method = 'POST'): array {
        $source = $method === 'POST' ? $_POST : $_GET;
        $missing = [];
        
        foreach ($params as $param) {
            if (!isset($source[$param]) || empty($source[$param])) {
                $missing[] = $param;
            }
        }
        
        if (!empty($missing)) {
            return [
                'valid' => false,
                'error' => 'Missing required parameters: ' . implode(', ', $missing),
                'missing' => $missing
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Standard success response
     */
    protected static function success_response(array $data = [], string $message = ''): void {
        $response = $data;
        if ($message) {
            $response['message'] = $message;
        }
        wp_send_json_success($response);
    }
    
    /**
     * Standard error response
     */
    protected static function error_response(string $message, int $code = 400, array $data = []): void {
        $response = array_merge(['message' => $message], $data);
        wp_send_json_error($response, $code);
    }
    
    /**
     * Log AJAX action for debugging
     */
    protected static function log_action(string $action, array $context = []): void {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $user_id = get_current_user_id();
            $log_data = array_merge([
                'action' => $action,
                'user_id' => $user_id,
                'timestamp' => current_time('mysql'),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ], $context);
            
            error_log('DGDB AJAX: ' . wp_json_encode($log_data));
        }
    }
    
    /**
     * Rate limiting check
     */
    protected static function check_rate_limit(string $action, int $limit_seconds = 60): bool {
        return Security::check_rate_limit($action, $limit_seconds);
    }
    
    /**
     * Sanitize float value
     */
    protected static function sanitize_float($value): ?float {
        if ($value === null || $value === '') {
            return null;
        }
        
        $float_val = floatval($value);
        return ($float_val >= -10 && $float_val <= 10) ? $float_val : null;
    }
    
    /**
     * Format datetime for output
     */
    protected static function format_datetime(string $datetime): string {
        if (empty($datetime)) {
            return '';
        }
        
        try {
            $dt = new \DateTime($datetime, wp_timezone());
            return $dt->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return $datetime;
        }
    }
    
    /**
     * Invalidate related caches
     */
    protected static function invalidate_caches(int $user_id = null, int $disc_id = null): void {
        if ($user_id) {
            QueryCache::invalidate_user_cache($user_id);
        }
        
        if ($disc_id) {
            QueryCache::invalidate_disc_cache($disc_id);
        }
    }
    
    /**
     * Execute with error handling
     */
    protected static function execute_with_error_handling(callable $callback, string $action = 'unknown'): void {
        try {
            self::log_action($action);
            $callback();
        } catch (\Exception $e) {
            self::log_action($action . '_error', ['error' => $e->getMessage()]);
            
            if (defined('WP_DEBUG') && WP_DEBUG) {
                self::error_response('Error: ' . $e->getMessage());
            } else {
                self::error_response('An error occurred while processing your request');
            }
        }
    }
    
    /**
     * Validate disc ID exists
     */
    protected static function validate_disc_exists(int $disc_id): bool {
        global $wpdb;
        
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_discs WHERE id = %d",
            $disc_id
        ));
        
        return (int)$exists > 0;
    }
    
    /**
     * Get user display name safely
     */
    protected static function get_user_display_name(int $user_id): string {
        $user = get_user_by('id', $user_id);
        return $user ? $user->display_name : 'User';
    }
    
    /**
     * Paginate results
     */
    protected static function paginate_results(int $total, int $page, int $per_page): array {
        $pages = max(1, (int)ceil($total / $per_page));
        $offset = ($page - 1) * $per_page;
        
        return [
            'page' => $page,
            'pages' => $pages,
            'total' => $total,
            'per_page' => $per_page,
            'offset' => $offset
        ];
    }
}
