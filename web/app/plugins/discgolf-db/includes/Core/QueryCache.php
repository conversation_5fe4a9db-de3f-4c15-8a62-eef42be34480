<?php

namespace DiscGolfDB\Core;

/**
 * Query caching helper to reduce database load
 */
class QueryCache {
    
    private static array $cache = [];
    private static int $default_ttl = 300; // 5 minutes
    
    /**
     * Get cached query result or execute and cache
     */
    public static function get_or_set(string $key, callable $callback, int $ttl = null): mixed {
        $ttl = $ttl ?? self::$default_ttl;
        
        // Check transient cache first
        $cached = get_transient("dgdb_cache_{$key}");
        if ($cached !== false) {
            return $cached;
        }
        
        // Check in-memory cache
        if (isset(self::$cache[$key])) {
            $cache_data = self::$cache[$key];
            if ($cache_data['expires'] > time()) {
                return $cache_data['data'];
            }
            unset(self::$cache[$key]);
        }
        
        // Execute callback and cache result
        $result = $callback();
        
        // Store in both caches
        self::$cache[$key] = [
            'data' => $result,
            'expires' => time() + $ttl
        ];
        
        set_transient("dgdb_cache_{$key}", $result, $ttl);
        
        return $result;
    }
    
    /**
     * Invalidate cache by key or pattern
     */
    public static function invalidate(string $key_or_pattern): void {
        // Remove from in-memory cache
        if (isset(self::$cache[$key_or_pattern])) {
            unset(self::$cache[$key_or_pattern]);
        }

        // Remove from transient cache
        delete_transient("dgdb_cache_{$key_or_pattern}");

        // If it's a pattern, remove matching keys
        if (strpos($key_or_pattern, '*') !== false) {
            $pattern = str_replace('*', '', $key_or_pattern);

            // Clear matching in-memory cache
            foreach (array_keys(self::$cache) as $key) {
                if (strpos($key, $pattern) === 0) {
                    unset(self::$cache[$key]);
                    delete_transient("dgdb_cache_{$key}");
                }
            }

            // Batch clear from database using SQL for patterns (more efficient)
            global $wpdb;
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s OR option_name LIKE %s",
                '_transient_dgdb_cache_' . $pattern . '%',
                '_transient_timeout_dgdb_cache_' . $pattern . '%'
            ));

            // Log pattern invalidation in debug mode
            if (defined('WP_DEBUG') && WP_DEBUG && $deleted > 0) {
                error_log("DGDB Cache: Pattern invalidation '{$key_or_pattern}' cleared {$deleted} transients");
            }
        } else {
            // Log single key invalidation in debug mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("DGDB Cache: Invalidated key '{$key_or_pattern}'");
            }
        }

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("DGDB: Cache invalidated for key/pattern: {$key_or_pattern}");
        }
    }
    
    /**
     * Clear all cache
     */
    public static function clear_all(): void {
        self::$cache = [];
        
        // Clear all DGDB transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_dgdb_cache_%' OR option_name LIKE '_transient_timeout_dgdb_cache_%'");
    }
    
    /**
     * Get user collection with caching
     */
    public static function get_user_collection(int $user_id, int $limit = 50, int $offset = 0): array {
        $cache_key = "user_collection_{$user_id}_{$limit}_{$offset}";
        
        return self::get_or_set($cache_key, function() use ($user_id, $limit, $offset) {
            return \DiscGolfDB\Collection::get_user_collection($user_id, $limit, $offset);
        }, 600); // 10 minutes
    }
    
    /**
     * Get user XP data with caching
     */
    public static function get_user_xp(int $user_id): array {
        $cache_key = "user_xp_{$user_id}";
        
        return self::get_or_set($cache_key, function() use ($user_id) {
            if (class_exists('\DiscGolfDB\Gamification\XP')) {
                return \DiscGolfDB\Gamification\XP::get_user_state($user_id);
            }
            return [];
        }, 300); // 5 minutes
    }
    
    /**
     * Get disc reviews with caching
     */
    public static function get_disc_reviews(int $disc_id, int $page = 1, int $per_page = 5): array {
        $cache_key = "disc_reviews_{$disc_id}_{$page}_{$per_page}";

        return self::get_or_set($cache_key, function() use ($disc_id, $page, $per_page) {
            // Call the static method directly
            if (class_exists('\DiscGolfDB\Ajax\Reviews') && method_exists('\DiscGolfDB\Ajax\Reviews', 'list_reviews_data')) {
                return \DiscGolfDB\Ajax\Reviews::list_reviews_data($disc_id, $page, $per_page);
            }

            // Fallback: implement the query directly
            global $wpdb;
            $offset = ($page - 1) * $per_page;

            $table = $wpdb->prefix . 'discgolf_reviews';
            $total = (int) $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table WHERE disc_id=%d",
                $disc_id
            ));

            $users_table = $wpdb->users;
            $rows = $wpdb->get_results($wpdb->prepare(
                "SELECT r.user_id, r.rating, r.title, r.review, r.plastic_type, r.weight,
                        r.speed, r.glide, r.turn, r.fade,
                        r.created_at, u.display_name
                 FROM $table r
                 LEFT JOIN $users_table u ON u.ID = r.user_id
                 WHERE r.disc_id = %d
                 ORDER BY r.created_at DESC
                 LIMIT %d OFFSET %d",
                $disc_id, $per_page, $offset
            ), ARRAY_A);

            $items = array_map(function ($r) {
                return [
                    'user_id'      => (int)($r['user_id'] ?? 0),
                    'user'         => $r['display_name'] ?: 'User',
                    'rating'       => (int)($r['rating'] ?? 0),
                    'title'        => (string)($r['title'] ?? ''),
                    'review'       => (string)($r['review'] ?? ''),
                    'plastic_type' => (string)($r['plastic_type'] ?? ''),
                    'weight'       => isset($r['weight']) ? (int)$r['weight'] : null,
                    'speed'        => self::out_float($r['speed'] ?? null),
                    'glide'        => self::out_float($r['glide'] ?? null),
                    'turn'         => self::out_float($r['turn'] ?? null),
                    'fade'         => self::out_float($r['fade'] ?? null),
                    'created_at'   => self::format_datetime($r['created_at'] ?? ''),
                ];
            }, $rows ?: []);

            $pages = max(1, (int)ceil($total / $per_page));
            return [
                'items' => $items,
                'meta'  => [
                    'page'     => $page,
                    'pages'    => $pages,
                    'total'    => $total,
                    'per_page' => $per_page,
                ],
            ];
        }, 60); // 1 minute - shorter cache for reviews
    }
    
    /**
     * Get user review counts with caching
     */
    public static function get_user_review_count(int $user_id): int {
        $cache_key = "user_review_count_{$user_id}";
        
        return self::get_or_set($cache_key, function() use ($user_id) {
            global $wpdb;
            return (int) $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_reviews WHERE user_id = %d",
                $user_id
            ));
        }, 600); // 10 minutes
    }
    
    /**
     * Invalidate user-related caches when user data changes
     */
    public static function invalidate_user_cache(int $user_id): void {
        self::invalidate("user_collection_{$user_id}*");
        self::invalidate("user_xp_{$user_id}");
        self::invalidate("user_review_count_{$user_id}");
    }
    
    /**
     * Invalidate disc-related caches when disc data changes
     */
    public static function invalidate_disc_cache(int $disc_id): void {
        self::invalidate("disc_reviews_{$disc_id}*");
    }
    
    /**
     * Get cache statistics for debugging
     */
    public static function get_stats(): array {
        return [
            'in_memory_keys' => count(self::$cache),
            'memory_usage' => memory_get_usage(),
            'cache_keys' => array_keys(self::$cache)
        ];
    }
    
    /**
     * Warm up commonly accessed caches
     */
    public static function warm_cache(): void {
        // This could be called on a cron job to pre-populate frequently accessed data
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DGDB: Cache warming started');
        }

        // Example: Pre-cache popular discs or recent reviews
        // Implementation would depend on specific use cases
    }

    /**
     * Helper method to format float values (copied from Reviews class)
     */
    private static function out_float($value): ?float {
        if ($value === null || $value === '') {
            return null;
        }

        $float_val = floatval($value);
        return ($float_val >= -10 && $float_val <= 10) ? $float_val : null;
    }

    /**
     * Helper method to format datetime (copied from Reviews class)
     */
    private static function format_datetime(string $datetime): string {
        if (empty($datetime)) {
            return '';
        }

        try {
            $dt = new \DateTime($datetime, wp_timezone());
            return $dt->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return $datetime;
        }
    }
}
