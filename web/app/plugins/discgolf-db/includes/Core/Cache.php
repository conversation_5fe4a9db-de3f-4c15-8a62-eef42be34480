<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Simple caching system for the plugin
 * Uses WordPress transients with fallback to object cache
 */
class Cache {
    private const PREFIX = 'dgdb_';
    private const DEFAULT_EXPIRY = 3600; // 1 hour

    /**
     * Get cached value
     */
    public static function get(string $key, $default = null) {
        $cache_key = self::PREFIX . $key;
        
        // Try object cache first (if available)
        if (wp_using_ext_object_cache()) {
            $value = wp_cache_get($cache_key, 'dgdb');
            if ($value !== false) {
                return $value;
            }
        }
        
        // Fallback to transients
        $value = get_transient($cache_key);
        return $value !== false ? $value : $default;
    }

    /**
     * Set cached value
     */
    public static function set(string $key, $value, int $expiry = self::DEFAULT_EXPIRY): bool {
        $cache_key = self::PREFIX . $key;
        
        // Set in object cache if available
        if (wp_using_ext_object_cache()) {
            wp_cache_set($cache_key, $value, 'dgdb', $expiry);
        }
        
        // Always set transient as fallback
        return set_transient($cache_key, $value, $expiry);
    }

    /**
     * Delete cached value
     */
    public static function delete(string $key): bool {
        $cache_key = self::PREFIX . $key;
        
        // Delete from object cache
        if (wp_using_ext_object_cache()) {
            wp_cache_delete($cache_key, 'dgdb');
        }
        
        // Delete transient
        return delete_transient($cache_key);
    }

    /**
     * Get or set cached value with callback
     */
    public static function remember(string $key, callable $callback, int $expiry = self::DEFAULT_EXPIRY) {
        $value = self::get($key);
        
        if ($value === null) {
            $value = call_user_func($callback);
            self::set($key, $value, $expiry);
        }
        
        return $value;
    }

    /**
     * Cache disc data
     */
    public static function get_disc(int $disc_id): ?array {
        return self::remember("disc_{$disc_id}", function() use ($disc_id) {
            global $wpdb;
            return $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$wpdb->prefix}discgolf_discs WHERE id = %d",
                $disc_id
            ), ARRAY_A);
        }, 1800); // 30 minutes
    }

    /**
     * Cache user collection count
     */
    public static function get_user_collection_count(int $user_id): int {
        return (int) self::remember("user_collection_count_{$user_id}", function() use ($user_id) {
            global $wpdb;
            return $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_user_collection WHERE user_id = %d",
                $user_id
            ));
        }, 900); // 15 minutes
    }

    /**
     * Cache disc reviews count
     */
    public static function get_disc_reviews_count(int $disc_id): int {
        return (int) self::remember("disc_reviews_count_{$disc_id}", function() use ($disc_id) {
            global $wpdb;
            return $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_reviews WHERE disc_id = %d",
                $disc_id
            ));
        }, 600); // 10 minutes
    }

    /**
     * Cache manufacturers list
     */
    public static function get_manufacturers(): array {
        return self::remember('manufacturers_list', function() {
            global $wpdb;
            return $wpdb->get_col(
                "SELECT DISTINCT manufacturer FROM {$wpdb->prefix}discgolf_discs 
                 WHERE manufacturer IS NOT NULL AND manufacturer != '' 
                 ORDER BY manufacturer ASC"
            );
        }, 3600); // 1 hour
    }

    /**
     * Cache approval years list
     */
    public static function get_approval_years(): array {
        return self::remember('approval_years_list', function() {
            global $wpdb;
            return $wpdb->get_col(
                "SELECT DISTINCT approval_year FROM {$wpdb->prefix}discgolf_discs 
                 WHERE approval_year IS NOT NULL 
                 ORDER BY approval_year DESC"
            );
        }, 3600); // 1 hour
    }

    /**
     * Invalidate disc-related caches
     */
    public static function invalidate_disc_cache(int $disc_id): void {
        self::delete("disc_{$disc_id}");
        self::delete("disc_reviews_count_{$disc_id}");
        // Also invalidate lists that might include this disc
        self::delete('manufacturers_list');
        self::delete('approval_years_list');
    }

    /**
     * Invalidate user-related caches
     */
    public static function invalidate_user_cache(int $user_id): void {
        self::delete("user_collection_count_{$user_id}");
    }

    /**
     * Invalidate review-related caches
     */
    public static function invalidate_review_cache(int $disc_id): void {
        self::delete("disc_reviews_count_{$disc_id}");
    }

    /**
     * Clear all plugin caches
     */
    public static function flush_all(): void {
        global $wpdb;
        
        // Delete all transients with our prefix
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_' . self::PREFIX . '%'
        ));
        
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_timeout_' . self::PREFIX . '%'
        ));
        
        // Clear object cache group if available
        if (wp_using_ext_object_cache()) {
            wp_cache_flush_group('dgdb');
        }
    }

    /**
     * Get cache statistics (for debugging)
     */
    public static function get_stats(): array {
        global $wpdb;
        
        $transient_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_' . self::PREFIX . '%'
        ));
        
        return [
            'transient_count' => (int) $transient_count,
            'using_object_cache' => wp_using_ext_object_cache(),
            'prefix' => self::PREFIX
        ];
    }
}
