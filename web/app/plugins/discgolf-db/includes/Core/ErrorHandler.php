<?php
namespace DiscGolfDB\Core;

defined('ABSPATH') || exit;

/**
 * Centralized error handling for the plugin
 */
class ErrorHandler {

    private static bool $initialized = false;
    private static array $error_counts = [];

    /**
     * Initialize error handling
     */
    public static function init(): void {
        if (self::$initialized) {
            return;
        }

        // Register shutdown function to catch fatal errors
        register_shutdown_function([self::class, 'handle_shutdown']);

        self::$initialized = true;
    }

    /**
     * Handle fatal errors on shutdown
     */
    public static function handle_shutdown(): void {
        $error = error_get_last();

        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            // Only handle errors from our plugin
            if (strpos($error['file'], 'discgolf-db') !== false) {
                self::log('Fatal Error: ' . $error['message'], [
                    'file' => $error['file'],
                    'line' => $error['line'],
                    'type' => 'fatal'
                ]);
            }
        }
    }

    /**
     * Log an error with context
     */
    public static function log(string $message, array $context = []): void {
        $timestamp = current_time('mysql');
        $user_id = get_current_user_id();

        $log_data = [
            'timestamp' => $timestamp,
            'user_id' => $user_id,
            'message' => $message,
            'context' => $context,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? ''
        ];

        // Rate limiting to prevent log spam
        $error_key = md5($message);
        self::$error_counts[$error_key] = (self::$error_counts[$error_key] ?? 0) + 1;

        if (self::$error_counts[$error_key] <= 10) {
            error_log('DGDB Error: ' . wp_json_encode($log_data));
        }

        // Store critical errors in database
        if (self::is_critical_error($message, $context)) {
            self::store_error_in_db($log_data);
        }
    }

    /**
     * Handle AJAX errors consistently
     */
    public static function ajax_error(string $message, array $context = [], int $code = 500): void {
        self::log($message, $context);
        
        $response = ['message' => $message];
        if (defined('WP_DEBUG') && WP_DEBUG && !empty($context)) {
            $response['debug'] = $context;
        }
        
        wp_send_json_error($response, $code);
    }

    /**
     * Handle database errors
     */
    public static function db_error(string $operation, \wpdb $wpdb, array $context = []): void {
        $message = "Database error during {$operation}";
        $error_context = array_merge($context, [
            'last_error' => $wpdb->last_error,
            'last_query' => $wpdb->last_query
        ]);
        
        self::log($message, $error_context);
    }

    /**
     * Validate required POST parameters
     */
    public static function validate_required_post(array $required_fields): array {
        $missing = [];
        $values = [];
        
        foreach ($required_fields as $field => $sanitizer) {
            if (!isset($_POST[$field])) {
                $missing[] = $field;
                continue;
            }
            
            $value = $_POST[$field];
            
            // Apply sanitization
            switch ($sanitizer) {
                case 'int':
                    $values[$field] = intval($value);
                    break;
                case 'text':
                    $values[$field] = sanitize_text_field($value);
                    break;
                case 'textarea':
                    $values[$field] = sanitize_textarea_field($value);
                    break;
                case 'email':
                    $values[$field] = sanitize_email($value);
                    break;
                case 'url':
                    $values[$field] = esc_url_raw($value);
                    break;
                default:
                    $values[$field] = sanitize_text_field($value);
            }
        }
        
        if (!empty($missing)) {
            self::ajax_error('Missing required fields: ' . implode(', ', $missing), ['missing' => $missing], 400);
        }
        
        return $values;
    }

    /**
     * Validate nonce and login status
     */
    public static function validate_ajax_request(string $nonce_action = 'dgdb', bool $require_login = true): void {
        if ($require_login && !is_user_logged_in()) {
            self::ajax_error('Authentication required', [], 401);
        }
        
        if (!check_ajax_referer($nonce_action, 'nonce', false)) {
            self::ajax_error('Invalid security token', [], 403);
        }
    }

    /**
     * Wrap AJAX handler with error handling
     */
    public static function wrap_ajax_handler(callable $handler): void {
        try {
            call_user_func($handler);
        } catch (\Exception $e) {
            self::ajax_error(
                'An unexpected error occurred',
                [
                    'exception' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            );
        }
    }

    /**
     * Check if error is critical and should be stored in database
     */
    private static function is_critical_error(string $message, array $context): bool {
        $critical_keywords = ['fatal', 'exception', 'database', 'security', 'sql'];

        foreach ($critical_keywords as $keyword) {
            if (stripos($message, $keyword) !== false) {
                return true;
            }
        }

        return isset($context['type']) && in_array($context['type'], ['fatal', 'exception', 'database', 'security']);
    }

    /**
     * Store error in database for admin review
     */
    private static function store_error_in_db(array $log_data): void {
        global $wpdb;

        $table = $wpdb->prefix . 'discgolf_error_log';

        // Create table if it doesn't exist
        self::ensure_error_table_exists();

        $wpdb->insert($table, [
            'timestamp' => $log_data['timestamp'],
            'user_id' => $log_data['user_id'],
            'message' => substr($log_data['message'], 0, 500),
            'context' => wp_json_encode($log_data),
            'resolved' => 0
        ]);
    }

    /**
     * Ensure error log table exists
     */
    private static function ensure_error_table_exists(): void {
        global $wpdb;

        $table = $wpdb->prefix . 'discgolf_error_log';
        $charset = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS `{$table}` (
            id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            user_id BIGINT UNSIGNED NULL,
            message TEXT NOT NULL,
            context LONGTEXT NULL,
            resolved TINYINT(1) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            KEY idx_timestamp (timestamp),
            KEY idx_resolved (resolved)
        ) {$charset}";

        $wpdb->query($sql);
    }

    /**
     * Log database errors
     */
    public static function log_database_error(string $query, string $error): void {
        self::log('Database Error: ' . $error, [
            'type' => 'database',
            'query' => substr($query, 0, 200),
            'wpdb_error' => $error
        ]);
    }

    /**
     * Log security events
     */
    public static function log_security_event(string $event, array $context = []): void {
        self::log('Security Event: ' . $event, array_merge($context, ['type' => 'security']));
    }
}
