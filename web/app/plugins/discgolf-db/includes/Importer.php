<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

class Importer {
    /**
     * Import PDGA/merged CSV content.
     * Returns human string like "Processed X rows. New: Y, Updated: Z."
     */
    public static function import_csv(string $csv_raw): string {
        $f = fopen('php://temp', 'r+');
        fwrite($f, $csv_raw);
        rewind($f);

        $header = fgetcsv($f);
        if (!$header) return 'CSV saknar header.';

        // map header -> index (lowercased)
        $map = [];
        foreach ($header as $i => $h) $map[strtolower(trim((string)$h))] = $i;

        // Column indices (tolerant)
        $idx_man  = Utils::findIdx($map, ['manufacturer / distributor','manufacturer','distributor']);
        $idx_name = Utils::findIdx($map, ['disc model','model','name']);
        $idx_class= Utils::findIdx($map, ['class','type']);
        $idx_mw   = Utils::findIdx($map, ['max weight (gr)','max weight']);
        $idx_dia  = Utils::findIdx($map, ['diameter (cm)','diameter']);
        $idx_h    = Utils::findIdx($map, ['height (cm)','height']);
        $idx_rd   = Utils::findIdx($map, ['rim depth (cm)','rim depth']);
        $idx_ird  = Utils::findIdx($map, ['inside rim diameter (cm)','inside rim diameter']);
        $idx_rt   = Utils::findIdx($map, ['rim thickness (cm)','rim thickness']);
        $idx_rr   = Utils::findIdx($map, ['rim depth / diameter ratio (%)','rim depth / diameter ratio']);
        $idx_rc   = Utils::findIdx($map, ['rim configuration']);
        $idx_fx   = Utils::findIdx($map, ['flexibility (kg)','flexibility']);

        // Flight numbers
        $idx_speed = Utils::findIdx($map, ['speed']);
        $idx_glide = Utils::findIdx($map, ['glide']);
        $idx_turn  = Utils::findIdx($map, ['turn']);
        $idx_fade  = Utils::findIdx($map, ['fade']);
        // Optional legacy "stability" (meta only if present)
        $idx_stability = Utils::findIdx($map, ['stability']);

        $idx_mwv  = Utils::findIdx($map, ['max weight vint (gr)','vint']);
        $idx_cn   = Utils::findIdx($map, ['certification number','certification']);
        $idx_ad   = Utils::findIdx($map, ['approved date','approval date']);

        if ($idx_man===null || $idx_name===null) {
            return 'Hittade inte kolumnerna “Manufacturer / Distributor” och/eller “Disc Model”.';
        }

        global $wpdb;
        $table = $wpdb->prefix.'discgolf_discs';
        $rows=0; $ins=0; $upd=0;

        // Canonical labels for modal/details
        $LBL = [
            'manufacturer'  => 'Manufacturer / Distributor',
            'name'          => 'Disc Model',
            'max_weight'    => 'Max Weight (gr)',
            'diameter'      => 'Diameter (cm)',
            'height'        => 'Height (cm)',
            'rim_depth'     => 'Rim Depth (cm)',
            'inside_rim'    => 'Inside Rim Diameter (cm)',
            'rim_thickness' => 'Rim Thickness (cm)',
            'rim_ratio'     => 'Rim Depth / Diameter Ratio (%)',
            'rim_config'    => 'Rim Configuration',
            'flex'          => 'Flexibility (kg)',
            'class'         => 'Class',
            'max_weight_v'  => 'Max Weight Vint (gr)',
            'cert_no'       => 'Certification Number',
            'approved_date' => 'Approved Date',
        ];

        // helpers
        $read_raw = static function($row, $idx): string {
            if ($idx === null || !isset($row[$idx])) return '';
            return trim((string)$row[$idx]);
        };
        $to_num_or_null = static function($v) {
            if ($v === '' || $v === null) return null;
            if (is_string($v)) $v = str_replace(',', '.', trim($v));
            return is_numeric($v) ? (float)$v : null;
        };

        while (($row = fgetcsv($f)) !== false) {
            $rows++;

            $manufacturer = trim((string)($row[$idx_man]  ?? ''));
            $name         = trim((string)($row[$idx_name] ?? ''));
            if ($manufacturer === '' || $name === '') continue;

            $type = $idx_class !== null ? trim((string)($row[$idx_class] ?? '')) : '';

            // Approved Date -> date + year
            $approval_date = null;
            $approval_year = null;
            if ($idx_ad !== null && isset($row[$idx_ad])) {
                $val = trim((string)$row[$idx_ad]);
                $ts = strtotime($val);
                if ($ts) {
                    $approval_date = date('Y-m-d', $ts);
                    $approval_year = (int)date('Y', $ts);
                } elseif (preg_match('/\b(19|20)\d{2}\b/', $val, $m)) {
                    $approval_year = (int)$m[0];
                }
            }

            // Max weight numeric
            $max_weight = null;
            if ($idx_mw !== null && isset($row[$idx_mw])) {
                $mw = str_replace(',', '.', (string)$row[$idx_mw]);
                if (is_numeric($mw)) $max_weight = (float)$mw;
            }

            // Build fields for modal (human-readable labels)
            $fields = [];
            $fields[$LBL['manufacturer']]  = $manufacturer;
            $fields[$LBL['name']]          = $name;
            if ($idx_mw  !== null) $fields[$LBL['max_weight']]    = $read_raw($row, $idx_mw);
            if ($idx_dia !== null) $fields[$LBL['diameter']]      = $read_raw($row, $idx_dia);
            if ($idx_h   !== null) $fields[$LBL['height']]        = $read_raw($row, $idx_h);
            if ($idx_rd  !== null) $fields[$LBL['rim_depth']]     = $read_raw($row, $idx_rd);
            if ($idx_ird !== null) $fields[$LBL['inside_rim']]    = $read_raw($row, $idx_ird);
            if ($idx_rt  !== null) $fields[$LBL['rim_thickness']] = $read_raw($row, $idx_rt);
            if ($idx_rr  !== null) $fields[$LBL['rim_ratio']]     = $read_raw($row, $idx_rr);
            if ($idx_rc  !== null) $fields[$LBL['rim_config']]    = $read_raw($row, $idx_rc);
            if ($idx_fx  !== null) $fields[$LBL['flex']]          = $read_raw($row, $idx_fx);
            if ($idx_class!==null) $fields[$LBL['class']]         = $type;
            if ($idx_mwv !== null) $fields[$LBL['max_weight_v']]  = $read_raw($row, $idx_mwv);
            if ($idx_cn  !== null) $fields[$LBL['cert_no']]       = $read_raw($row, $idx_cn);
            if ($approval_date)     $fields[$LBL['approved_date']] = $approval_date;

            // Flight numbers — read raw strings once
            $speed_raw = $read_raw($row, $idx_speed);
            $glide_raw = $read_raw($row, $idx_glide);
            $turn_raw  = $read_raw($row, $idx_turn);
            $fade_raw  = $read_raw($row, $idx_fade);

            // Put both human labels and lowercase keys into meta_json
            if ($idx_speed !== null) { $fields['Speed'] = $speed_raw; $fields['speed'] = $speed_raw; }
            if ($idx_glide !== null) { $fields['Glide'] = $glide_raw; $fields['glide'] = $glide_raw; }
            if ($idx_turn  !== null) { $fields['Turn']  = $turn_raw;  $fields['turn']  = $turn_raw;  }
            if ($idx_fade  !== null) { $fields['Fade']  = $fade_raw;  $fields['fade']  = $fade_raw;  }
            if ($idx_stability !== null) {
                $fields['Stability'] = $read_raw($row, $idx_stability);
                // (no lowercase stability column; left in meta only by design)
            }

            // Normalized numeric values for fast filtering (nullable)
            $spd = $to_num_or_null($speed_raw);
            $gld = $to_num_or_null($glide_raw);
            $trn = $to_num_or_null($turn_raw);
            $fd  = $to_num_or_null($fade_raw);

            // Chips compact
            $chips = [];
            if ($approval_date) $chips['Approved Date'] = $approval_date;
            if (!empty($type))  $chips['Class'] = $type;

            $meta_json = wp_json_encode([
                'fields' => $fields,
                'chips'  => $chips,
            ]);

            // Upsert
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table WHERE manufacturer=%s AND name=%s",
                $manufacturer, $name
            ));

            $data = [
                'manufacturer'  => $manufacturer,
                'name'          => $name,
                'type'          => $type,
                'approval_year' => $approval_year ?: null,
                'approval_date' => $approval_date ?: null,
                'max_weight'    => $max_weight ?: null,

                // NEW normalized columns for fast filtering
                'speed'         => $spd,
                'glide'         => $gld,
                'turn'          => $trn,
                'fade'          => $fd,

                // JSON for UI/specs
                'meta_json'     => $meta_json,
            ];

            // fmt must match $data order exactly:
            // %s,%s,%s,%d,%s,%f,%f,%f,%f,%f,%s
            $fmt = ['%s','%s','%s','%d','%s','%f','%f','%f','%f','%f','%s'];

            if ($exists) {
                $wpdb->update($table, $data, ['id' => $exists], $fmt, ['%d']);
                $upd++;
            } else {
                $wpdb->insert($table, $data, $fmt);
                $ins++;
            }
        }

        fclose($f);

        // Reattach reviews if present
        DB::relinkReviewsFromBackup();

        return "Bearbetade $rows rader. Nya: $ins, uppdaterade: $upd.";
    }
}
