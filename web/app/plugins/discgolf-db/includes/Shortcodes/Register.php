<?php
namespace DiscGolfDB\Shortcodes;

defined('ABSPATH') || exit;

class Register {
  public static function register(): void {
    add_shortcode('dgdb_register', [self::class,'render']);
    add_action('init', [self::class,'handle_post']);
  }
  public static function render(): string {

    if (is_user_logged_in()) return '<div class="notice">You are already logged in.</div>';
    $out = '<form method="post" class="dgdb-card" style="max-width:420px">';
    $out .= wp_nonce_field('dgdb_register','dgdb_reg_nonce', true, false);
    $out .= '<p><label>Email<br><input type="email" name="email" required></label></p>';
    $out .= '<p><label>Username<br><input type="text" name="username" required></label></p>';
    $out .= '<p><label>Password<br><input type="password" name="pass1" required></label></p>';
    $out .= '<p><button class="dgdb-btn" type="submit" name="dgdb_register_submit" value="1">Create account</button></p>';
    $out .= '</form>';
    return $out;
  }
  public static function handle_post(): void {
    if (!isset($_POST['dgdb_register_submit'])) return;
    if (!isset($_POST['dgdb_reg_nonce']) || !wp_verify_nonce($_POST['dgdb_reg_nonce'],'dgdb_register')) return;

    $email = sanitize_email($_POST['email'] ?? '');
    $user  = sanitize_user($_POST['username'] ?? '');
    $pass  = (string)($_POST['pass1'] ?? '');
    if (!$email || !$user || !$pass) return;

    $uid = wp_create_user($user, $pass, $email);
    if (is_wp_error($uid)) {
      add_filter('the_content', fn($c)=> $c.'<div class="notice notice-error"><p>'.esc_html($uid->get_error_message()).'</p></div>');
      return;
    }
    wp_set_current_user($uid);
    wp_set_auth_cookie($uid, true);
    wp_safe_redirect(home_url('/profile')); // send them to profile start
    exit;
  }
}
