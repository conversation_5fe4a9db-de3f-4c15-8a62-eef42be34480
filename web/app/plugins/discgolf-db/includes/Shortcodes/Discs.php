<?php
namespace DiscGolfDB\Shortcodes;

use DiscGolfDB\Assets;

defined('ABSPATH') || exit;

class Discs {
    public static function register(): void {
        add_shortcode('discgolf_discs', [self::class, 'render']);
    }

    public static function render(): string {

        try {
            // Load assets for shortcode
            \DiscGolfDB\Core\AssetManager::load_for_shortcode();

            $template_path = DGDB_PATH . 'templates/discs-list.php';

            if (!file_exists($template_path)) {
                return '<div class="notice notice-error"><p>Template not found: ' . esc_html($template_path) . '</p></div>';
            }

            ob_start();
            include $template_path;
            $content = ob_get_clean();

            if (empty($content)) {
                return '<div class="notice notice-warning"><p>Template rendered but produced no output</p></div>';
            }

            return $content;
        } catch (\Exception $e) {
            error_log('DGDB Shortcode Error: ' . $e->getMessage());
            return '<div class="notice notice-error"><p>Error rendering disc list: ' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
}
