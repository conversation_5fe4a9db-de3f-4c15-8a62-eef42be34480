<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

class SingleDiscRouter {
    private static bool $is_single_disc = false;
    private static array $ctx = [];

    public static function register(): void {
        add_action('init', [self::class, 'add_rewrite']);
        add_filter('query_vars', [self::class, 'query_vars']);

        add_action('template_redirect', [self::class, 'bootstrap_if_needed'], 1);
        add_filter('the_content', [self::class, 'render_content'], 999);
        add_filter('document_title_parts', [self::class, 'title_parts'], 10);
        add_action('wp_enqueue_scripts', [self::class, 'enqueue_assets']);
        add_filter('body_class', [self::class, 'body_class']);
    }

    public static function add_rewrite(): void {
        // Use the regular "disc" page (so theme header/footer stay intact)
        add_rewrite_rule(
            '^disc/([0-9]+)/?$',
            'index.php?pagename=disc&dgdb_disc_id=$matches[1]',
            'top'
        );
    }

    public static function query_vars(array $vars): array {
        $vars[] = 'dgdb_disc_id';
        return $vars;
    }

    public static function bootstrap_if_needed(): void {
        if (is_admin()) return;

        $disc_id = absint(get_query_var('dgdb_disc_id'));
        if (!$disc_id || get_query_var('pagename') !== 'disc') return;

        global $wpdb;
        $disc = $wpdb->get_row($wpdb->prepare(
            "SELECT id, manufacturer, name, type, approval_year, approval_date, max_weight, meta_json
             FROM {$wpdb->prefix}discgolf_discs WHERE id=%d",
            $disc_id
        ), ARRAY_A);
        if (!$disc) return;

        $img = $wpdb->get_row($wpdb->prepare(
            "SELECT image_url, source_url
               FROM {$wpdb->prefix}discgolf_images
              WHERE disc_id=%d
              LIMIT 1",
            $disc_id
        ), ARRAY_A) ?: null;

        self::$is_single_disc = true;
        self::$ctx = [
            'disc_id' => $disc_id,
            'disc'    => $disc,
            'img'     => $img,
        ];
    }

    public static function render_content(string $content): string {
        if (!self::$is_single_disc || !in_the_loop() || !is_main_query()) return $content;

        ob_start();
        $ctx = self::$ctx; // available inside template
        include DGDB_PATH . 'templates/single-disc.php';
        return ob_get_clean();
    }

    public static function title_parts(array $parts): array {
        if (!self::$is_single_disc) return $parts;
        $d = self::$ctx['disc'] ?? null;
        if ($d) $parts['title'] = trim(($d['manufacturer'] ? $d['manufacturer'].' ' : '').$d['name']);
        return $parts;
    }

    public static function enqueue_assets(): void {
        if (!self::$is_single_disc) return;

        // Use new AssetManager for single disc pages
        \DiscGolfDB\Core\AssetManager::load_for_single_disc([
            'discId'         => (int) (self::$ctx['disc_id'] ?? 0),
            'can_contribute' => is_user_logged_in() ? 1 : 0,
        ]);
    }

    public static function body_class(array $classes): array {
        if (self::$is_single_disc) $classes[] = 'dgdb-disc-page';
        return $classes;
    }
}
