<?php
namespace DiscGolfDB\Ajax;

defined('ABSPATH') || exit;

class Reviews {
    public static function register(): void {
        // Create/update review (must be logged in)
        add_action('wp_ajax_dg_add_review', [self::class, 'add_review']);

        // Get my review for a disc (must be logged in)
        add_action('wp_ajax_dg_get_my_review', [self::class, 'get_my_review']);

        // Public list of reviews (pagination)
        add_action('wp_ajax_dg_list_reviews', [self::class, 'list_reviews']);
        add_action('wp_ajax_nopriv_dg_list_reviews', [self::class, 'list_reviews']); // allow visitors
    }

    /** POST: action=dg_add_review, nonce, disc_id, rating, title?, review?, plastic_type?, weight?, speed?, glide?, turn?, fade? */
    public static function add_review(): void {
        try {
            // Validate AJAX request basics
            $request_validation = \DiscGolfDB\Core\InputValidator::validate_ajax_request('dgdb', true);
            if (!$request_validation['valid']) {
                wp_send_json_error($request_validation['error']);
            }

            // Rate limiting - prevent spam reviews
            if (!\DiscGolfDB\Core\Security::check_rate_limit('review_submit', 120)) {
                wp_send_json_error([
                    'message' => 'För många recensioner. Vänta 2 minuter innan du försöker igen.',
                    'type' => 'rate_limit',
                    'wait_time' => 120
                ]);
            }

            // Validate all input fields
            $validations = [
                'disc_id' => \DiscGolfDB\Core\InputValidator::validate_disc_id($_POST['disc_id'] ?? 0),
                'rating' => \DiscGolfDB\Core\InputValidator::validate_rating($_POST['rating'] ?? 0),
                'title' => \DiscGolfDB\Core\InputValidator::validate_text_field($_POST['title'] ?? '', 100, false),
                'review' => \DiscGolfDB\Core\InputValidator::validate_textarea_field($_POST['review'] ?? '', 2000, false),
                'plastic_type' => \DiscGolfDB\Core\InputValidator::validate_text_field($_POST['plastic_type'] ?? '', 50, false),
                'weight' => \DiscGolfDB\Core\InputValidator::validate_disc_weight($_POST['weight'] ?? ''),
                'speed' => \DiscGolfDB\Core\InputValidator::validate_flight_number($_POST['speed'] ?? null, 0, 15, 0.5),
                'glide' => \DiscGolfDB\Core\InputValidator::validate_flight_number($_POST['glide'] ?? null, 0, 7, 0.5),
                'turn' => \DiscGolfDB\Core\InputValidator::validate_flight_number($_POST['turn'] ?? null, -5, 2, 0.5),
                'fade' => \DiscGolfDB\Core\InputValidator::validate_flight_number($_POST['fade'] ?? null, 0, 5, 0.5),
            ];

            $validation_result = \DiscGolfDB\Core\InputValidator::validate_multiple($validations);
            if (!$validation_result['valid']) {
                wp_send_json_error('Validation failed: ' . implode(', ', $validation_result['errors']));
            }

            // Extract validated values
            extract($validation_result['values']);

            global $wpdb;
            $user_id = get_current_user_id();

            $table = $wpdb->prefix . 'discgolf_reviews';

            // Does this user already have a review? (we keep one row per user+disc)
            $existing_id = (int) $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM `{$table}` WHERE user_id=%d AND disc_id=%d ORDER BY created_at DESC, id DESC LIMIT 1",
                $user_id, $disc_id
            ));

            $data = [
                'user_id'      => $user_id,
                'disc_id'      => $disc_id,
                'rating'       => $rating,
                'title'        => $title,
                'review'       => $review,
                'plastic_type' => $plastic_type,
                'weight'       => $weight,
                'speed'        => $speed,
                'glide'        => $glide,
                'turn'         => $turn,
                'fade'         => $fade,
                'created_at'   => current_time('mysql'),
            ];
            $formats = ['%d','%d','%d','%s','%s','%s','%d','%f','%f','%f','%f','%s'];

            if ($existing_id) {
                // update
                $ok = $wpdb->update($table, $data, ['id' => $existing_id], $formats, ['%d']);
                $keep_id = $existing_id;
                $updated = true;
            } else {
                // insert
                $ok = $wpdb->insert($table, $data, $formats);
                $keep_id = (int)$wpdb->insert_id;
                $updated = false;
            }

            if ($ok === false) {
                $msg = 'DB error';
                if (defined('WP_DEBUG') && WP_DEBUG && !empty($wpdb->last_error)) {
                    $msg .= ': ' . $wpdb->last_error;
                }
                wp_send_json_error($msg);
            }

            // Clean any historical duplicates
            if ($keep_id) {
                $wpdb->query($wpdb->prepare(
                    "DELETE FROM `{$table}` WHERE user_id=%d AND disc_id=%d AND id <> %d",
                    $user_id, $disc_id, $keep_id
                ));
            }

            // Smart cache invalidation - only invalidate what's necessary
            self::invalidate_review_caches($disc_id, $user_id);

            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("DGDB: Review added for disc {$disc_id}, caches invalidated");
            }

            // --- Gamification: award XP for saving a review ---
            if (class_exists('\DiscGolfDB\Gamification\XP')) {
                \DiscGolfDB\Gamification\XP::awardForReview(
                    $keep_id, // the saved review id
                    $user_id,
                    [
                        'title'  => $title,
                        'text'   => $review,
                        'speed'  => $speed,
                        'glide'  => $glide,
                        'turn'   => $turn,
                        'fade'   => $fade,
                        'plastic'=> $plastic_type,
                        'weight' => $weight,
                        // 'image' / 'first_on_disc' if you compute those
                    ]
                );
                do_action('dgdb_xp_changed', $user_id);
            }


            // Return the saved review (for optimistic UI)
            $my = self::fetch_user_review_full($user_id, $disc_id);
            wp_send_json_success([
                'message' => $updated ? 'Saved (updated)' : 'Saved (new)',
                'updated' => $updated,
                'my'      => $my,
            ]);
        } catch (\Exception $e) {
            error_log('DGDB add_review error: ' . $e->getMessage());
            wp_send_json_error('An error occurred while saving the review');
        }
    }

    /** GET: action=dg_get_my_review&nonce&disc_id */
    public static function get_my_review(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) {
            wp_send_json_error('Not logged in');
        }

        $user_id = get_current_user_id();
        $disc_id = max(1, intval($_GET['disc_id'] ?? 0));
        if (!$disc_id) wp_send_json_error('No disc');

        $my = self::fetch_user_review_full($user_id, $disc_id);
        if ($my['has']) {
            wp_send_json_success($my);
        } else {
            wp_send_json_success(['has' => false]);
        }
    }

    /** GET: action=dg_list_reviews&disc_id[&page=1&per_page=5] — public */
    public static function list_reviews(): void {
        $disc_id  = max(1, intval($_GET['disc_id'] ?? 0));
        $page     = max(1, intval($_GET['page'] ?? 1));
        $per_page = max(1, min(50, intval($_GET['per_page'] ?? 5)));

        if (!$disc_id) wp_send_json_error('No disc');

        // Debug logging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("DGDB: list_reviews called for disc {$disc_id}, page {$page}");
        }

        // Use cached data for better performance
        $data = \DiscGolfDB\Core\QueryCache::get_disc_reviews($disc_id, $page, $per_page);

        // Debug the result
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $count = isset($data['items']) ? count($data['items']) : 0;
            $total = isset($data['meta']['total']) ? $data['meta']['total'] : 0;
            error_log("DGDB: list_reviews returning {$count} items, total: {$total}");
        }

        wp_send_json_success($data);
    }

    /** Helper method for getting review data (used by cache) - Optimized to prevent N+1 queries */
    public static function list_reviews_data(int $disc_id, int $page = 1, int $per_page = 5): array {
        global $wpdb;

        $offset = ($page - 1) * $per_page;

        // Use a single optimized query with proper indexing
        $table = $wpdb->prefix . 'discgolf_reviews';
        $users_table = $wpdb->users;

        // Get total count efficiently
        $total = (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table} WHERE disc_id = %d",
            $disc_id
        ));

        if ($total === 0) {
            return [
                'items' => [],
                'meta'  => [
                    'page'     => $page,
                    'pages'    => 1,
                    'total'    => 0,
                    'per_page' => $per_page,
                ],
            ];
        }

        // Single optimized query with all needed data
        $rows = $wpdb->get_results($wpdb->prepare(
            "SELECT r.user_id, r.rating, r.title, r.review, r.plastic_type, r.weight,
                    r.speed, r.glide, r.turn, r.fade, r.created_at,
                    COALESCE(u.display_name, 'User') as display_name
             FROM {$table} r
             LEFT JOIN {$users_table} u ON u.ID = r.user_id
             WHERE r.disc_id = %d
             ORDER BY r.created_at DESC, r.id DESC
             LIMIT %d OFFSET %d",
            $disc_id, $per_page, $offset
        ), ARRAY_A);

        // Process results efficiently
        $items = [];
        foreach ($rows as $r) {
            $items[] = [
                'user_id'      => (int)$r['user_id'],
                'user'         => $r['display_name'],
                'rating'       => (int)$r['rating'],
                'title'        => (string)$r['title'],
                'review'       => (string)$r['review'],
                'plastic_type' => (string)$r['plastic_type'],
                'weight'       => $r['weight'] ? (int)$r['weight'] : null,
                'speed'        => self::out_float($r['speed']),
                'glide'        => self::out_float($r['glide']),
                'turn'         => self::out_float($r['turn']),
                'fade'         => self::out_float($r['fade']),
                'created_at'   => self::format_datetime($r['created_at']),
            ];
        }

        $pages = max(1, (int)ceil($total / $per_page));
        return [
            'items' => $items,
            'meta'  => [
                'page'     => $page,
                'pages'    => $pages,
                'total'    => $total,
                'per_page' => $per_page,
            ],
        ];
    }

    /* ---------------- helpers ---------------- */

    /**
     * Normalize a float from POST:
     * - '' or null => null
     * - clamps to [min, max]
     * - snaps to step
     */
    protected static function norm_float($val, float $min, float $max, float $step): ?float {
        if ($val === '' || $val === null) return null;
        if (!is_numeric($val)) return null;
        $num = floatval($val);
        $num = max($min, min($max, $num));
        if ($step > 0) {
            $num = round($num / $step) * $step;
        }
        // avoid tails like 2.499999
        return floatval(number_format($num, 2, '.', ''));
    }

    protected static function out_float($val): ?float {
        if ($val === null || $val === '' || !is_numeric($val)) return null;
        return floatval($val);
    }

    protected static function fetch_user_review_full(int $user_id, int $disc_id): array {
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_reviews';
        $row = $wpdb->get_row($wpdb->prepare(
            "SELECT rating, title, review, plastic_type, weight, speed, glide, turn, fade, created_at
             FROM $table
             WHERE user_id=%d AND disc_id=%d
             ORDER BY created_at DESC, id DESC
             LIMIT 1",
            $user_id, $disc_id
        ), ARRAY_A);

        if (!$row) return ['has' => false];

        return [
            'has'          => true,
            'rating'       => (int)$row['rating'],
            'title'        => (string)$row['title'],
            'review'       => (string)$row['review'],
            'plastic_type' => (string)$row['plastic_type'],
            'weight'       => isset($row['weight']) ? (int)$row['weight'] : null,
            'speed'        => self::out_float($row['speed']),
            'glide'        => self::out_float($row['glide']),
            'turn'         => self::out_float($row['turn']),
            'fade'         => self::out_float($row['fade']),
            'created_at'   => self::format_datetime($row['created_at'] ?? ''),
        ];
    }

    protected static function format_datetime(string $dt): string {
        if (empty($dt)) return '';
        $ts = strtotime($dt);
        if (!$ts) return $dt;
        return date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $ts);
    }

    /**
     * Smart cache invalidation - only invalidate what's necessary
     */
    private static function invalidate_review_caches(int $disc_id, int $user_id): void {
        // Invalidate specific disc review caches
        \DiscGolfDB\Core\QueryCache::invalidate("disc_reviews_{$disc_id}");

        // Invalidate user-specific caches
        \DiscGolfDB\Core\QueryCache::invalidate("user_review_{$user_id}_{$disc_id}");

        // Invalidate disc statistics that might include review counts/averages
        \DiscGolfDB\Core\QueryCache::invalidate("disc_stats_{$disc_id}");

        // Only invalidate broader caches if this is a significant change
        $review_count = self::get_disc_review_count($disc_id);
        if ($review_count <= 5) {
            // For discs with few reviews, invalidate more broadly as each review has bigger impact
            \DiscGolfDB\Core\QueryCache::invalidate_disc_cache($disc_id);
        }

        // Don't flush entire WordPress object cache - too aggressive
        // Only clear specific transients
        delete_transient("dgdb_disc_{$disc_id}_review_summary");
    }

    /**
     * Get review count for a disc (cached)
     */
    private static function get_disc_review_count(int $disc_id): int {
        return \DiscGolfDB\Core\QueryCache::get_or_set(
            "disc_review_count_{$disc_id}",
            function() use ($disc_id) {
                global $wpdb;
                $table = $wpdb->prefix . 'discgolf_reviews';
                return (int) $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM {$table} WHERE disc_id = %d",
                    $disc_id
                ));
            },
            300 // 5 minutes cache
        );
    }
}
