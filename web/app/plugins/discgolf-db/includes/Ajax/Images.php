<?php
namespace DiscGolfDB\Ajax;

defined('ABSPATH') || exit;

class Images {
    public static function register(): void {
        add_action('wp_ajax_dg_upload_image', [self::class, 'handle']);
    }

    public static function handle(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');
        if (empty($_FILES['disc_image'])) wp_send_json_error('No file uploaded');

        $disc_id = max(1, intval($_POST['disc_id'] ?? 0));
        $user_id = get_current_user_id();
        if (!$disc_id) wp_send_json_error('No disc');

        // Use secure file upload handler
        $upload_result = \DiscGolfDB\Core\FileUpload::handle_image_upload($_FILES['disc_image']);

        if (!$upload_result['success']) {
            wp_send_json_error($upload_result['error']);
        }

        global $wpdb;
        $wpdb->replace($wpdb->prefix.'discgolf_images', [
            'disc_id'   => $disc_id,
            'user_id'   => $user_id,
            'image_url' => esc_url_raw($upload_result['url']),
        ], ['%d','%d','%s']);

        wp_send_json_success(['url' => $upload_result['url'], 'message' => 'Uppladdad!']);
    }
}
