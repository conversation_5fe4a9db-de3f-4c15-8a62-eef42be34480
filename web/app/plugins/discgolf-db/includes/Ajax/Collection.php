<?php
namespace DiscGolfDB\Ajax;

use DiscGolfDB\Collection;

defined('ABSPATH') || exit;

class CollectionAjax {
    public static function register(): void {
        // Support both action names (old + new)
        add_action('wp_ajax_dgdb_toggle_collect',     [self::class, 'toggle']);
        add_action('wp_ajax_dgdb_collection_toggle',  [self::class, 'toggle']);

        add_action('wp_ajax_dgdb_collection_update', [self::class, 'update']);
        add_action('wp_ajax_dgdb_profile_update',    [self::class, 'profile_update']);
        add_action('wp_ajax_dgdb_profile_upload',    [self::class, 'profile_upload']);

        // New collection management actions
        add_action('wp_ajax_dgdb_collection_delete',    [self::class, 'delete_item']);
        add_action('wp_ajax_dgdb_collection_duplicate', [self::class, 'duplicate_item']);
        add_action('wp_ajax_dgdb_personal_disc_save',   [self::class, 'save_personal_disc']);
        add_action('wp_ajax_dgdb_personal_disc_get',    [self::class, 'get_personal_disc']);
        add_action('wp_ajax_dgdb_personal_disc_upload', [self::class, 'upload_personal_image']);
    }

    /** POST: action=dgdb_toggle_collect (or dgdb_collection_toggle), nonce, disc_id */
    public static function toggle(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $disc_id = max(1, intval($_POST['disc_id'] ?? 0));
        if (!$disc_id) wp_send_json_error('No disc');

        $user_id = get_current_user_id();
        $state = Collection::toggle_item($user_id, $disc_id);

        // Invalidate user collection cache
        \DiscGolfDB\Core\Cache::invalidate_user_cache($user_id);

        wp_send_json_success(['state' => $state]);
    }

    /** POST: action=dgdb_collection_update, nonce, disc_id, notes (owner only) */
    public static function update(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $disc_id = max(1, intval($_POST['disc_id'] ?? 0));
        $notes   = sanitize_textarea_field($_POST['notes'] ?? '');
        if (!$disc_id) wp_send_json_error('No disc');

        $user_id = get_current_user_id();
        $success = Collection::upsert_item($user_id, $disc_id, ['notes' => $notes]);
        if (!$success) wp_send_json_error('Failed to save');

        // Invalidate user collection cache
        \DiscGolfDB\Core\Cache::invalidate_user_cache($user_id);

        wp_send_json_success(['message' => 'Saved']);
    }

    /** POST: action=dgdb_profile_update, fields... */
    public static function profile_update(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $user_id = get_current_user_id();

        update_user_meta($user_id, 'dgdb_favorite_disc_id', max(0, intval($_POST['favorite_disc_id'] ?? 0)));
        update_user_meta($user_id, 'dgdb_favorite_disc_text', sanitize_text_field($_POST['favorite_disc_text'] ?? ''));
        update_user_meta($user_id, 'dgdb_favorite_course',   sanitize_text_field($_POST['favorite_course'] ?? ''));
        update_user_meta($user_id, 'dgdb_pdga_number',       max(0, intval($_POST['pdga_number'] ?? 0)));

        update_user_meta($user_id, 'dgdb_bag_name',  sanitize_text_field($_POST['bag_name'] ?? ''));
        update_user_meta($user_id, 'dgdb_mini_disc', sanitize_text_field($_POST['mini_disc'] ?? ''));
        update_user_meta($user_id, 'dgdb_accessories', sanitize_text_field($_POST['accessories'] ?? ''));

        // bag JSON
        $bag_json = json_decode(stripslashes((string)($_POST['bag_json'] ?? '[]')), true);
        if (!is_array($bag_json)) $bag_json = [];
        update_user_meta($user_id, 'dgdb_bag_json', $bag_json);

        wp_send_json_success(['message' => 'Profile updated']);
    }

    /** POST multipart: action=dgdb_profile_upload, kind=avatar|banner, file */
    public static function profile_upload(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');
        if (empty($_FILES['file']['name'])) wp_send_json_error('No file');

        $kind = ($_POST['kind'] ?? 'avatar') === 'banner' ? 'banner' : 'avatar';

        // Enhanced file validation
        $validation = \DiscGolfDB\Core\FileUploadValidator::validate_image_upload($_FILES['file'], 'profile');
        if (!$validation['valid']) {
            wp_send_json_error($validation['error']);
        }

        // Rate limiting for uploads
        if (!\DiscGolfDB\Core\Security::check_rate_limit('profile_upload', 300)) { // 5 minutes
            wp_send_json_error('Too many uploads. Please wait before uploading again.');
        }

        require_once ABSPATH.'wp-admin/includes/file.php';
        $up = wp_handle_upload($_FILES['file'], ['test_form' => false]);
        if (empty($up['url']) || !empty($up['error'])) {
            wp_send_json_error($up['error'] ?? 'Upload failed');
        }

        // attach to media library
        $att_id = wp_insert_attachment([
            'post_title'     => 'Profile '.$kind,
            'post_mime_type' => $validation['file_info']['mime_type'],
            'guid'           => $up['url']
        ], $up['file']);

        require_once ABSPATH.'wp-admin/includes/image.php';
        $meta = wp_generate_attachment_metadata($att_id, $up['file']);
        wp_update_attachment_metadata($att_id, $meta);

        $meta_key = $kind === 'banner' ? 'dgdb_banner_id' : 'dgdb_profile_photo_id';
        update_user_meta(get_current_user_id(), $meta_key, $att_id);

        wp_send_json_success(['attachment_id' => $att_id, 'url' => $up['url']]);
    }

    /** POST: action=dgdb_collection_delete, nonce, collection_id */
    public static function delete_item(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $collection_id = max(1, intval($_POST['collection_id'] ?? 0));
        if (!$collection_id) wp_send_json_error('No collection ID');

        // Verify ownership
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM `{$table}` WHERE id = %d",
            $collection_id
        ));

        if ((int)$owner_id !== get_current_user_id()) {
            wp_send_json_error('Not authorized');
        }

        $success = Collection::delete_item_by_id($collection_id);
        if (!$success) wp_send_json_error('Failed to delete');

        // Invalidate user collection cache
        \DiscGolfDB\Core\Cache::invalidate_user_cache(get_current_user_id());

        wp_send_json_success(['message' => 'Item deleted']);
    }

    /** POST: action=dgdb_collection_duplicate, nonce, collection_id */
    public static function duplicate_item(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $collection_id = max(1, intval($_POST['collection_id'] ?? 0));
        if (!$collection_id) wp_send_json_error('No collection ID');

        // Verify ownership
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM `{$table}` WHERE id = %d",
            $collection_id
        ));

        if ((int)$owner_id !== get_current_user_id()) {
            wp_send_json_error('Not authorized');
        }

        $new_id = Collection::duplicate_item($collection_id);
        if (!$new_id) wp_send_json_error('Failed to duplicate');

        // Invalidate user collection cache
        \DiscGolfDB\Core\Cache::invalidate_user_cache(get_current_user_id());

        wp_send_json_success(['message' => 'Item duplicated', 'new_collection_id' => $new_id]);
    }

    /** POST: action=dgdb_personal_disc_save, nonce, collection_id, personal_data */
    public static function save_personal_disc(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $collection_id = max(1, intval($_POST['collection_id'] ?? 0));
        if (!$collection_id) wp_send_json_error('No collection ID');

        // Verify ownership
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM `{$table}` WHERE id = %d",
            $collection_id
        ));

        if ((int)$owner_id !== get_current_user_id()) {
            wp_send_json_error('Not authorized');
        }

        // Parse custom fields JSON
        $custom_fields = [];
        if (!empty($_POST['custom_fields'])) {
            $fields_json = json_decode(stripslashes($_POST['custom_fields']), true);
            if (is_array($fields_json)) {
                foreach ($fields_json as $field) {
                    if (isset($field['name']) && isset($field['value']) && !empty(trim($field['name']))) {
                        $custom_fields[] = [
                            'name' => sanitize_text_field($field['name']),
                            'value' => sanitize_text_field($field['value']),
                            'type' => sanitize_text_field($field['type'] ?? 'text')
                        ];
                    }
                }
            }
        }

        $personal_data = [
            'custom_image_id' => max(0, intval($_POST['custom_image_id'] ?? 0)) ?: null,
            'plastic_type' => sanitize_text_field($_POST['plastic_type'] ?? ''),
            'weight' => !empty($_POST['weight']) ? (int)$_POST['weight'] : null,
            'personal_notes' => sanitize_textarea_field($_POST['personal_notes'] ?? ''),
            'custom_fields' => $custom_fields,
        ];

        $success = Collection::save_personal_info($collection_id, $personal_data);
        if (!$success) wp_send_json_error('Failed to save');

        wp_send_json_success(['message' => 'Personal disc information saved']);
    }

    /** POST: action=dgdb_personal_disc_get, nonce, collection_id */
    public static function get_personal_disc(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');

        $collection_id = max(1, intval($_POST['collection_id'] ?? 0));
        if (!$collection_id) wp_send_json_error('No collection ID');

        // Verify ownership
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM `{$table}` WHERE id = %d",
            $collection_id
        ));

        if ((int)$owner_id !== get_current_user_id()) {
            wp_send_json_error('Not authorized');
        }

        $personal_info = Collection::get_personal_info($collection_id);

        // Add image URL if custom image exists
        if ($personal_info && $personal_info['custom_image_id']) {
            $personal_info['custom_image_url'] = wp_get_attachment_image_url($personal_info['custom_image_id'], 'medium');
            if (!$personal_info['custom_image_url']) {
                $personal_info['custom_image_url'] = wp_get_attachment_url($personal_info['custom_image_id']);
            }
        }

        // Parse custom fields JSON
        if ($personal_info && $personal_info['custom_fields']) {
            $personal_info['custom_fields'] = json_decode($personal_info['custom_fields'], true);
        } else if ($personal_info) {
            $personal_info['custom_fields'] = [];
        }

        wp_send_json_success(['personal_info' => $personal_info]);
    }

    /** POST multipart: action=dgdb_personal_disc_upload, nonce, collection_id, file */
    public static function upload_personal_image(): void {
        check_ajax_referer('dgdb', 'nonce');
        if (!is_user_logged_in()) wp_send_json_error('Not logged in');
        if (empty($_FILES['file']['name'])) wp_send_json_error('No file');

        $collection_id = max(1, intval($_POST['collection_id'] ?? 0));
        if (!$collection_id) wp_send_json_error('No collection ID');

        // Verify ownership
        global $wpdb;
        $table = $wpdb->prefix . 'discgolf_user_collection';
        $owner_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM `{$table}` WHERE id = %d",
            $collection_id
        ));

        if ((int)$owner_id !== get_current_user_id()) {
            wp_send_json_error('Not authorized');
        }

        // Enhanced file validation
        $validation = \DiscGolfDB\Core\FileUploadValidator::validate_image_upload($_FILES['file'], 'general');
        if (!$validation['valid']) {
            wp_send_json_error($validation['error']);
        }

        // Rate limiting for uploads
        if (!\DiscGolfDB\Core\Security::check_rate_limit('personal_disc_upload', 180)) { // 3 minutes
            wp_send_json_error('Too many uploads. Please wait before uploading again.');
        }

        require_once ABSPATH.'wp-admin/includes/file.php';
        $up = wp_handle_upload($_FILES['file'], ['test_form' => false]);
        if (empty($up['url']) || !empty($up['error'])) {
            wp_send_json_error($up['error'] ?? 'Upload failed');
        }

        // attach to media library
        $att_id = wp_insert_attachment([
            'post_title'     => 'Personal Disc Image',
            'post_mime_type' => $validation['file_info']['mime_type'],
            'guid'           => $up['url']
        ], $up['file']);

        require_once ABSPATH.'wp-admin/includes/image.php';
        $meta = wp_generate_attachment_metadata($att_id, $up['file']);
        wp_update_attachment_metadata($att_id, $meta);

        wp_send_json_success(['attachment_id' => $att_id, 'url' => $up['url']]);
    }
}
