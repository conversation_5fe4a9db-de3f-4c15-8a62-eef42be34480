<?php
namespace DiscGolfDB\Ajax;
defined('ABSPATH') || exit;

class Debug {
  public static function register(): void {
    add_action('wp_ajax_dg_ping', [self::class, 'ping']);
    add_action('wp_ajax_nopriv_dg_ping', [self::class, 'ping']);
  }
  public static function ping(): void {
    $out = [
      'hooks' => [
        'dg_add_review'   => has_action('wp_ajax_dg_add_review') ? 'yes' : 'no',
        'dg_get_my_review'=> has_action('wp_ajax_dg_get_my_review') ? 'yes' : 'no',
        'dg_list_reviews' => has_action('wp_ajax_dg_list_reviews') ? 'yes' : 'no',
        'dg_list_reviews_nopriv' => has_action('wp_ajax_nopriv_dg_list_reviews') ? 'yes' : 'no',
      ],
      'nonce_example' => wp_create_nonce('dgdb'),
    ];
    wp_send_json_success($out);
  }
}
