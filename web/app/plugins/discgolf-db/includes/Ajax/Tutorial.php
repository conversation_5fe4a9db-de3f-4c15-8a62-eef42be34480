<?php
namespace DiscGolfDB\Ajax;

defined('ABSPATH') || exit;

/**
 * AJAX for interactive tutorial progress.
 * Action: dgdb_tutorial_progress
 * Returns: { steps: { key: { done: bool, meta: array } }, totals: { done:int, total:int } }
 */
class Tutorial {
    public static function register(): void {
        add_action('wp_ajax_dgdb_tutorial_progress', [self::class, 'progress']);
    }

    public static function progress(): void {
        check_ajax_referer('dgdb_tutorial', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error(['message' => 'Not logged in'], 401);
        }
        $uid = get_current_user_id();

        global $wpdb;
        $pfx = $wpdb->prefix;

        // Ledger helpers
        $ledger_has = function(string $src) use($wpdb,$pfx,$uid): bool {
            $sql = "SELECT 1 FROM {$pfx}discgolf_xp_ledger WHERE user_id=%d AND source=%s LIMIT 1";
            return (bool)$wpdb->get_var($wpdb->prepare($sql, $uid, $src));
        };

        // One-time flags (from our awarders), fall back to ledger if needed
        $profile_done = (bool)get_user_meta($uid, 'dgdb_xp_awarded_profile_edit', true) || $ledger_has('profile_edit');
        $first_add    = (bool)get_user_meta($uid, 'dgdb_xp_awarded_first_collection_add', true) || (bool)$wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_user_collection WHERE user_id=%d LIMIT 1", $uid
        ));
        $first_edit   = (bool)get_user_meta($uid, 'dgdb_xp_awarded_first_disc_edit', true) || (bool)$wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_personal_discs pd JOIN {$pfx}discgolf_user_collection c ON c.id=pd.collection_id AND c.user_id=%d LIMIT 1",
            $uid
        ));
        $has_review   = (bool)$wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_reviews WHERE user_id=%d LIMIT 1", $uid
        ));
        $has_referral = $ledger_has('referral');

        // Wiki submission: any pending/approved submission across images/plastics/texts
        $wiki_pending_or_approved = (bool)($wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_images WHERE user_id=%d LIMIT 1", $uid
        )) ?: $wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_plastics WHERE created_by=%d LIMIT 1", $uid
        )) ?: $wpdb->get_var($wpdb->prepare(
            "SELECT 1 FROM {$pfx}discgolf_text_suggestions WHERE created_by=%d LIMIT 1", $uid
        )));

        $steps = [
            'profile_edit'         => ['done'=>$profile_done, 'meta'=>[]],
            'first_collection_add' => ['done'=>$first_add,   'meta'=>[]],
            'first_disc_edit'      => ['done'=>$first_edit,  'meta'=>[]],
            'review1'              => ['done'=>$has_review,  'meta'=>[]],
            'referral'             => ['done'=>$has_referral,'meta'=>[]],
            'wiki_submit'          => ['done'=>$wiki_pending_or_approved, 'meta'=>[]],
        ];

        $done = 0; foreach ($steps as $s) { if (!empty($s['done'])) $done++; }
        wp_send_json_success([
            'steps'  => $steps,
            'totals' => ['done'=>$done, 'total'=>count($steps)],
        ]);
    }
}
