<?php
namespace DiscGolfDB\Ajax;

use DiscGolfDB\Gamification\XP;

defined('ABSPATH') || exit;

class Gamification {
    public static function register(): void {
        // HUD (compact)
        add_action('wp_ajax_dgdb_gamify_hud',        [self::class, 'hud']);
        add_action('wp_ajax_nopriv_dgdb_gamify_hud', [self::class, 'hud']);

        // Modal details (logged-in)
        add_action('wp_ajax_dgdb_gamify_details',    [self::class, 'details']);

        // mark levelup toast as seen (optional)
        add_action('wp_ajax_dgdb_gamify_mark_seen',  [self::class, 'mark_seen']);
    }

    public static function hud(): void {
        $uid = get_current_user_id();
        if (!$uid) {
            wp_send_json_success(['guest' => true]);
        }

        $state = XP::get_user_state((int)$uid);

        // Add referral URL
        $state['invite_url'] = \DiscGolfDB\Gamification\Referrals::get_invite_url((int)$uid);

        // Weekly progress snapshot (for modal)
        $R = get_option(XP::OPT_RULES, XP::default_rules());
        $need = (int)($R['streak']['week_min_reviews'] ?? 3);

        global $wpdb;
        $weekStart = (new \DateTimeImmutable('monday this week', wp_timezone()))->format('Y-m-d') . ' 00:00:00';
        $cnt = (int)$wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}discgolf_reviews WHERE user_id=%d AND created_at >= %s",
            $uid, $weekStart
        ));
        $state['weekly'] = [
            'count' => $cnt,
            'need'  => $need,
        ];

        $user = [
            'id'        => (int)$uid,
            'name'      => wp_get_current_user()->display_name ?: 'You',
            'avatar'    => \DiscGolfDB\Utils::get_user_avatar_url($uid, ['size' => 48]),
            'level'     => (int)$state['level'],
            'rank'      => (string)($state['rank']['label'] ?? 'Rookie'),
            'rank_slug' => (string)($state['rank']['slug']  ?? 'rookie'),
            'curr_xp'   => (int)$state['curr_xp'],
            'next_xp'   => (int)$state['next_xp'],
            'total_xp'  => (int)$state['total_xp'],
        ];

        wp_send_json_success(['user' => $user, 'loggedIn' => true]);
    }

    /**
     * Detailed data for the modal.
     * Returns:
     * - profile: name, avatar, joined
     * - xp: total_xp, level, floor_xp, next_xp, rank, thresholds
     * - recent: last 20 ledger rows (action, delta, created_at, source)
     * - counts: contributions summary (reviews, plastics submitted/approved, etc.)
     */
    public static function details(): void {
        if (!is_user_logged_in()) {
            wp_send_json_error('Not logged in');
        }
        $uid = (int)get_current_user_id();

        $user = wp_get_current_user();
        $profile = [
            'id'      => $uid,
            'name'    => $user->display_name ?: $user->user_login,
            'avatar'  => \DiscGolfDB\Utils::get_user_avatar_url($uid, ['size' => 96]),
            'joined'  => mysql2date(get_option('date_format'), $user->user_registered),
        ];

        // XP state + thresholds
        $state = XP::get_user_state($uid);

        global $wpdb;
        // recent ledger entries
        $ledgerT = $wpdb->prefix . 'discgolf_xp_ledger';
        $recent = $wpdb->get_results($wpdb->prepare(
            "SELECT action, source, source_ref, delta, created_at
             FROM $ledgerT
             WHERE user_id=%d
             ORDER BY id DESC
             LIMIT 20",
            $uid
        ), ARRAY_A) ?: [];

        // contribution counts
        $reviewsT  = $wpdb->prefix . 'discgolf_reviews';
        $plasticsT = $wpdb->prefix . 'discgolf_plastics';

        $counts = [
            'reviews'           => (int)$wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $reviewsT WHERE user_id=%d", $uid)),
            'plastics_submitted'=> (int)$wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $plasticsT WHERE created_by=%d", $uid)),
            'plastics_approved' => (int)$wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $plasticsT WHERE created_by=%d AND status='approved'", $uid)),
        ];

        // rank thresholds list (for the ladder)
        $thr = get_option(\DiscGolfDB\Gamification\XP::OPT_RANKS, []);
        $thresholds = [];
        if ($thr && is_array($thr)) {
            ksort($thr, SORT_NUMERIC);
            foreach ($thr as $lvl => $min) {
                $thresholds[] = ['level' => (int)$lvl, 'min_xp' => (int)$min];
            }
        }

        wp_send_json_success([
            'profile'     => $profile,
            'xp'          => [
                'total_xp' => (int)$state['total_xp'],
                'floor_xp' => (int)$state['floor_xp'],
                'next_xp'  => (int)$state['next_xp'],
                'level'    => (int)$state['level'],
                'rank'     => $state['rank'],
                'thresholds'=> $thresholds,
            ],
            'recent'      => array_map(function($r){
                return [
                    'action'     => (string)$r['action'],
                    'source'     => (string)$r['source'],
                    'source_ref' => isset($r['source_ref']) ? (int)$r['source_ref'] : null,
                    'delta'      => (int)$r['delta'],
                    'created_at' => mysql2date(get_option('date_format') . ' ' . get_option('time_format'), $r['created_at']),
                ];
            }, $recent),
            'counts'      => $counts,
        ]);
    }

    public static function mark_seen(): void {
        $uid = get_current_user_id();
        if ($uid) update_user_meta($uid, 'dgdb_level_seen_at', time());
        wp_send_json_success(true);
    }
}
