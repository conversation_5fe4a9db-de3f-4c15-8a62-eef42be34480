<?php
namespace DiscGolfDB\Ajax;

defined('ABSPATH') || exit;

class Plastics {
    public static function register(): void {
        add_action('wp_ajax_dg_list_plastics', [self::class, 'list_plastics']);
        add_action('wp_ajax_nopriv_dg_list_plastics', [self::class, 'list_plastics']); // public list

        add_action('wp_ajax_dg_save_plastics', [self::class, 'save_plastics']); // requires login
    }

    /** GET: action=dg_list_plastics&disc_id
     * Returns available options + vote totals + your selection (if logged in)
     */
    public static function list_plastics(): void {
        global $wpdb;
        $disc_id = max(1, intval($_GET['disc_id'] ?? 0));
        if (!$disc_id) wp_send_json_error('No disc');

        $tp = $wpdb->prefix . 'discgolf_plastics';
        $tv = $wpdb->prefix . 'discgolf_plastic_votes';
        $tu = $wpdb->users;

        // All plastics linked to this disc
        $rows = $wpdb->get_results($wpdb->prepare("
            SELECT p.id, p.plastic_name, p.status, p.source,
                   COALESCE(SUM(v.id IS NOT NULL), 0) AS votes
            FROM $tp p
            LEFT JOIN $tv v ON v.plastic_id = p.id
            WHERE p.disc_id = %d
            GROUP BY p.id
            ORDER BY votes DESC, p.plastic_name ASC
        ", $disc_id), ARRAY_A) ?: [];

        // Current user's selection set
        $mine = [];
        if (is_user_logged_in()) {
            $uid = get_current_user_id();
            $mine = $wpdb->get_col($wpdb->prepare("
                SELECT plastic_id FROM $tv WHERE disc_id=%d AND user_id=%d
            ", $disc_id, $uid)) ?: [];
            $mine = array_map('intval', $mine);
        }

        // Brand options (from seed) that might not yet be added to this disc
        // We expose them so UI can “quick add” + vote in one go.
        $brand_opts = [];
        $brand = (string) $wpdb->get_var($wpdb->prepare("SELECT manufacturer FROM {$wpdb->prefix}discgolf_discs WHERE id=%d", $disc_id));
        if ($brand !== '') {
            $map = get_option(\DiscGolfDB\Admin\BrandPlastics::OPTION, []);
            // brand aliases same as seeder
            $aliases = [
                'Innova' => 'Innova Champion Discs',
                'Innova Champion discs' => 'Innova Champion Discs',
                'Latitude64' => 'Latitude 64',
                'Discmania Golf Discs' => 'Discmania',
            ];
            $target = $aliases[$brand] ?? $brand;
            $brand_opts = $map[$target] ?? $map[$brand] ?? [];
        }

        wp_send_json_success([
            'items' => array_map(function($r){
                return [
                    'id'     => (int)$r['id'],
                    'name'   => (string)$r['plastic_name'],
                    'status' => (string)$r['status'],       // approved|pending
                    'source' => (string)$r['source'],       // seed:brand-map|community|…
                    'votes'  => (int)$r['votes'],
                ];
            }, $rows),
            'mine'   => $mine,
            'brand_options' => array_values($brand_opts), // strings
        ]);
    }

    /** POST: action=dg_save_plastics
     * payload: disc_id, ids[] (existing plastic_id votes), add_names[] (free-text / brand options not yet linked)
     * Creates missing plastics as pending, then upserts votes for the current user.
     */
    public static function save_plastics(): void {
        try {
            check_ajax_referer('dgdb', 'nonce');
            if (!is_user_logged_in()) wp_send_json_error('Not logged in');

            global $wpdb;
            $uid = get_current_user_id();
            $disc_id = max(1, intval($_POST['disc_id'] ?? 0));
            if (!$disc_id) wp_send_json_error('No disc');

            $ids       = array_map('intval', (array)($_POST['ids'] ?? []));
            $add_names = array_map('sanitize_text_field', (array)($_POST['add_names'] ?? []));
            $add_names = array_values(array_filter(array_unique(array_map('trim', $add_names)), fn($s)=>$s!==''));
            if (count($ids) > 30 || count($add_names) > 10) wp_send_json_error('Too many items');

            $tp = $wpdb->prefix . 'discgolf_plastics';
            $tv = $wpdb->prefix . 'discgolf_plastic_votes';

            // Create missing plastics (pending) and collect their IDs
            foreach ($add_names as $name) {
                // Case-insensitive search for existing plastic row for this disc
                $pid = (int)$wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM $tp WHERE disc_id=%d AND LOWER(plastic_name)=LOWER(%s) LIMIT 1",
                    $disc_id, $name
                ));
                if (!$pid) {
                    $ok = $wpdb->insert($tp, [
                        'disc_id'      => $disc_id,
                        'plastic_name' => $name,
                        'notes'        => null,
                        'source'       => 'community',
                        'status'       => 'pending',
                        'created_by'   => $uid,
                        'approved_by'  => null,
                        'created_at'   => current_time('mysql'),
                        'approved_at'  => null,
                    ], ['%d','%s','%s','%s','%s','%d','%d','%s','%s']);
                    if ($ok !== false) $pid = (int)$wpdb->insert_id;
                }
                if ($pid) $ids[] = $pid;
            }

            // Normalize unique
            $ids = array_values(array_unique(array_filter($ids, fn($v)=>$v>0)));

            // Replace user’s votes for this disc with new set (simple model)
            $wpdb->query($wpdb->prepare("DELETE FROM $tv WHERE disc_id=%d AND user_id=%d", $disc_id, $uid));
            foreach ($ids as $pid) {
                // Ensure the plastic belongs to the disc
                $belongs = (int)$wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $tp WHERE id=%d AND disc_id=%d", $pid, $disc_id));
                if (!$belongs) continue;

                $wpdb->insert($tv, [
                    'disc_id'    => $disc_id,
                    'plastic_id' => $pid,
                    'user_id'    => $uid,
                    'created_at' => current_time('mysql'),
                ], ['%d','%d','%d','%s']);
            }

            // Return fresh list
            self::list_plastics();
        } catch (\Throwable $e) {
            wp_send_json_error('Error: ' . $e->getMessage());
        }
    }
}
