<?php
namespace DiscGolfDB;

defined('ABSPATH') || exit;

class Utils {
    /** Tolerant CSV header finder */
    public static function findIdx(array $header_map, array $candidates): ?int {
        foreach ($candidates as $c) {
            $c = strtolower($c);
            foreach ($header_map as $k => $i) {
                if (strpos($k, $c) !== false) return $i;
            }
        }
        return null;
    }

    /** Safe include template capturing output */
    public static function render_template(string $file, array $vars = []): string {
        $path = DGDB_PATH . 'templates/' . ltrim($file, '/');
        if (!file_exists($path)) return '';
        ob_start();
        extract($vars, EXTR_SKIP);
        include $path;
        return ob_get_clean();
    }

    public static function pdga_disc_search_url(string $manufacturer, string $name): string {
        // generic site search (no scraping, no image use)
        $q = rawurlencode(trim($manufacturer.' '.$name));
        return "https://www.pdga.com/search?keys={$q}";
    }

    /** Safe HTML escaping for output */
    public static function esc_html(string $text): string {
        return esc_html($text);
    }

    /** Safe attribute escaping for output */
    public static function esc_attr(string $text): string {
        return esc_attr($text);
    }

    /** Safe URL escaping for output */
    public static function esc_url(string $url): string {
        return esc_url($url);
    }

    /** Safe textarea content escaping */
    public static function esc_textarea(string $text): string {
        return esc_textarea($text);
    }

    /** Format star rating safely */
    public static function format_stars(int $rating): string {
        $rating = max(0, min(5, $rating));
        $full = '★';
        $empty = '☆';
        return str_repeat($full, $rating) . str_repeat($empty, 5 - $rating);
    }

    /** Format date safely */
    public static function format_date(string $date): string {
        if (empty($date)) return '';
        $timestamp = strtotime($date);
        if (!$timestamp) return esc_html($date);
        return esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $timestamp));
    }

    /**
     * Get user's profile photo URL, falling back to Gravatar if none uploaded
     */
    public static function get_user_avatar_url(int $user_id, array $args = []): string {
        $size = $args['size'] ?? 96;

        // Check for custom profile photo
        $profile_photo_id = get_user_meta($user_id, 'dgdb_profile_photo_id', true);

        if ($profile_photo_id) {
            $image_url = wp_get_attachment_image_url($profile_photo_id, 'full');
            if ($image_url) {
                return $image_url;
            }
        }

        // Fallback to WordPress/Gravatar avatar
        return get_avatar_url($user_id, ['size' => $size]);
    }
}
