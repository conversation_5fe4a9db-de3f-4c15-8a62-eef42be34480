# Translation of Themes - Twenty Twenty-Five in Swedish
# This file is distributed under the same license as the Themes - Twenty Twenty-Five package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-05 21:32:38+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: sv_SE\n"
"Project-Id-Version: Themes - Twenty Twenty-Five\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Five emphasizes simplicity and adaptability. It offers flexible design options, supported by a variety of patterns for different page types, such as services and landing pages, making it ideal for building personal blogs, professional portfolios, online magazines, or business websites. Its templates cater to various blog styles, from text-focused to image-heavy layouts. Additionally, it supports international typography and diverse color palettes, ensuring accessibility and customization for users worldwide."
msgstr "Twenty Twenty-Five lägger tyngdpunkten på enkelhet och anpassningsbarhet. Temat erbjuder flexibla designalternativ, som stöds av ett stort urval mönster för olika typer av sidor, såsom tjänstebeskrivningar och landningssidor, vilket gör temat idealisk för att bygga personliga bloggar, professionella portfolios, nätpublikationer och företagswebbplatser. Dess mallar tillgodoser olika bloggstilar, från textbaserade till bildtunga layouter. Dessutom stöder temat internationell typografi och olika färgpaletter, som säkerställer tillgänglighet och anpassning för användare över hela världen."

#. Theme Name of the theme
#: style.css patterns/footer-columns.php:66 patterns/footer-newsletter.php:42
#: patterns/footer.php:75 patterns/page-portfolio-home.php:226
#, gp-priority: high
msgid "Twenty Twenty-Five"
msgstr "Twenty Twenty-Five"

#: patterns/template-home-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned home"
msgstr "Högerjusterad startsida"

#: patterns/template-archive-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned archive"
msgstr "Högerjusterad arkivsida"

#: patterns/template-search-text-blog.php
msgctxt "Pattern title"
msgid "Text blog search results"
msgstr "Sökresultat för textbaserad blogg"

#: patterns/template-query-loop-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned query loop"
msgstr "Högerställd query-loop"

#: patterns/template-query-loop-text-blog.php
msgctxt "Pattern title"
msgid "Text blog query loop"
msgstr "Query-loop för textbaserad blogg"

#: patterns/text-faqs.php:35 patterns/text-faqs.php:51
#: patterns/text-faqs.php:71 patterns/text-faqs.php:87
msgctxt "Answer in the FAQs pattern."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist."
msgstr "Detta utsökta urval visar många skiftande fotografier som fångar essensen hos olika tidsepoker och kulturer och återspeglar varje konstnärs unika stil och perspektiv."

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Sida utan rubrik"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Sidopanel"

#: theme.json
msgctxt "Template part name"
msgid "Footer Newsletter"
msgstr "Sidfot nyhetsbrev"

#: theme.json
msgctxt "Template part name"
msgid "Footer Columns"
msgstr "Sidfotskolumner"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Sidfot"

#: theme.json
msgctxt "Template part name"
msgid "Header with large title"
msgstr "Sidhuvud med stor rubrik"

#: theme.json
msgctxt "Template part name"
msgid "Vertical site header"
msgstr "Vertikalt webbplatshuvud"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Sidhuvud"

#: styles/02-noon.json styles/typography/typography-preset-1.json
msgctxt "Font family name"
msgid "Beiruti"
msgstr "Beiruti"

#: styles/05-twilight.json styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Roboto Slab"
msgstr "Roboto Slab"

#: styles/04-afternoon.json styles/07-sunrise.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-6.json
msgctxt "Font family name"
msgid "Platypi"
msgstr "Platypi"

#: styles/04-afternoon.json styles/06-morning.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-5.json
msgctxt "Font family name"
msgid "Ysabeau Office"
msgstr "Ysabeau Office"

#: styles/08-midnight.json styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Fira Sans"
msgstr "Fira Sans"

#: theme.json styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Fira Code"
msgstr "Fira Code"

#: styles/03-dusk.json styles/typography/typography-preset-2.json
msgctxt "Font family name"
msgid "Vollkorn"
msgstr "Vollkorn"

#: styles/02-noon.json styles/06-morning.json styles/07-sunrise.json
#: styles/08-midnight.json styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-6.json
#: styles/typography/typography-preset-7.json
msgctxt "Font family name"
msgid "Literata"
msgstr "Literata"

#: theme.json styles/05-twilight.json
#: styles/typography/typography-preset-4.json
msgctxt "Font family name"
msgid "Manrope"
msgstr "Manrope"

#: theme.json
msgctxt "Space size name"
msgid "XX-Large"
msgstr "XXL"

#: theme.json
msgctxt "Space size name"
msgid "X-Large"
msgstr "XL"

#: theme.json
msgctxt "Space size name"
msgid "Large"
msgstr "L"

#: theme.json
msgctxt "Space size name"
msgid "Regular"
msgstr "Normal"

#: theme.json
msgctxt "Space size name"
msgid "Small"
msgstr "S"

#: theme.json
msgctxt "Space size name"
msgid "X-Small"
msgstr "XS"

#: theme.json
msgctxt "Space size name"
msgid "Tiny"
msgstr "Pytteliten"

#: styles/typography/typography-preset-7.json
msgctxt "Style variation name"
msgid "Literata & Fira Sans"
msgstr "Literata och Fira Sans"

#: styles/typography/typography-preset-6.json
msgctxt "Style variation name"
msgid "Platypi & Literata"
msgstr "Platypi och Literata"

#: styles/typography/typography-preset-5.json
msgctxt "Style variation name"
msgid "Literata & Ysabeau Office"
msgstr "Literata och Ysabeau Office"

#: styles/typography/typography-preset-4.json
msgctxt "Style variation name"
msgid "Roboto Slab & Manrope"
msgstr "Roboto Slab och Manrope"

#: styles/typography/typography-preset-3.json
msgctxt "Style variation name"
msgid "Platypi & Ysabeau Office"
msgstr "Platypi och Ysabeau Office"

#: styles/typography/typography-preset-2.json
msgctxt "Style variation name"
msgid "Vollkorn & Fira Code"
msgstr "Vollkorn och Fira Code"

#: styles/typography/typography-preset-1.json
msgctxt "Style variation name"
msgid "Beiruti & Literata"
msgstr "Beiruti och Literata"

#: styles/sections/section-5.json
msgctxt "Style variation name"
msgid "Style 5"
msgstr "Stil 5"

#: styles/sections/section-4.json
msgctxt "Style variation name"
msgid "Style 4"
msgstr "Stil 4"

#: styles/sections/section-3.json
msgctxt "Style variation name"
msgid "Style 3"
msgstr "Stil 3"

#: styles/sections/section-2.json
msgctxt "Style variation name"
msgid "Style 2"
msgstr "Stil 2"

#: styles/sections/section-1.json
msgctxt "Style variation name"
msgid "Style 1"
msgstr "Stil 1"

#: styles/blocks/post-terms-1.json
msgctxt "Style variation name"
msgid "Pill shaped"
msgstr "Pillerformad"

#: styles/blocks/03-annotation.json
msgctxt "Style variation name"
msgid "Annotation"
msgstr "Bildkommentar"

#: styles/blocks/02-subtitle.json
msgctxt "Style variation name"
msgid "Subtitle"
msgstr "Underrubrik"

#: styles/blocks/01-display.json
msgctxt "Style variation name"
msgid "Display"
msgstr "Visning"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Duotone name"
msgid "Midnight filter"
msgstr "Midnattsfilter"

#: styles/08-midnight.json styles/colors/08-midnight.json
msgctxt "Style variation name"
msgid "Midnight"
msgstr "Midnatt"

#: styles/07-sunrise.json styles/colors/07-sunrise.json
msgctxt "Style variation name"
msgid "Sunrise"
msgstr "Soluppgång"

#: styles/06-morning.json styles/colors/06-morning.json
msgctxt "Style variation name"
msgid "Morning"
msgstr "Morgon"

#: styles/05-twilight.json styles/colors/05-twilight.json
msgctxt "Style variation name"
msgid "Twilight"
msgstr "Halvdager"

#: styles/04-afternoon.json styles/colors/04-afternoon.json
msgctxt "Style variation name"
msgid "Afternoon"
msgstr "Eftermiddag"

#: styles/03-dusk.json styles/colors/03-dusk.json
msgctxt "Style variation name"
msgid "Dusk"
msgstr "Skymning"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Extra extra stor"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra stor"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Large"
msgstr "Stor"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medium"

#: theme.json styles/02-noon.json styles/04-afternoon.json
#: styles/05-twilight.json styles/06-morning.json styles/08-midnight.json
#: styles/typography/typography-preset-1.json
#: styles/typography/typography-preset-3.json
#: styles/typography/typography-preset-4.json
#: styles/typography/typography-preset-5.json
#: styles/typography/typography-preset-7.json
msgctxt "Font size name"
msgid "Small"
msgstr "Liten"

#: styles/02-noon.json styles/colors/02-noon.json
msgctxt "Style variation name"
msgid "Noon"
msgstr "Mitt på dagen"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 6"
msgstr "Accent 6"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 5"
msgstr "Accent 5"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 4"
msgstr "Accent 4"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 3"
msgstr "Accent 3"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 2"
msgstr "Accent 2"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Accent 1"
msgstr "Accent 1"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: theme.json styles/01-evening.json styles/02-noon.json styles/03-dusk.json
#: styles/04-afternoon.json styles/05-twilight.json styles/06-morning.json
#: styles/07-sunrise.json styles/08-midnight.json styles/colors/01-evening.json
#: styles/colors/02-noon.json styles/colors/03-dusk.json
#: styles/colors/04-afternoon.json styles/colors/05-twilight.json
#: styles/colors/06-morning.json styles/colors/07-sunrise.json
#: styles/colors/08-midnight.json
msgctxt "Color name"
msgid "Base"
msgstr "Bas"

#: styles/01-evening.json styles/colors/01-evening.json
msgctxt "Style variation name"
msgid "Evening"
msgstr "Kväll"

#: patterns/vertical-header.php
msgctxt "Pattern description"
msgid "Vertical site header with site title and navigation."
msgstr "Vertikalt webbplatshuvud med webbplatsens rubrik och navigering"

#: patterns/vertical-header.php
msgctxt "Pattern title"
msgid "Vertical site header"
msgstr "Vertikalt webbplatshuvud"

#: patterns/text-faqs.php:83
msgctxt "Question in the FAQs pattern."
msgid "Are signed copies available?"
msgstr "Finns det signerade exemplar?"

#: patterns/text-faqs.php:67
msgctxt "Question in the FAQs pattern."
msgid "When will The Stories Book be released?"
msgstr "När kommer The Stories Book att publiceras?"

#: patterns/text-faqs.php:47
msgctxt "Question in the FAQs pattern."
msgid "How much does The Stories Book cost?"
msgstr "Vad kostar The Stories Book?"

#: patterns/text-faqs.php:31
msgctxt "Question in the FAQs pattern."
msgid "What is The Stories Book about?"
msgstr "Vad handlar The Stories Book om?"

#: patterns/text-faqs.php:21
msgctxt "Heading of the FAQs pattern."
msgid "Frequently Asked Questions"
msgstr "Svar på vanliga frågor"

#: patterns/text-faqs.php
msgctxt "Pattern description"
msgid "A FAQs section with a FAQ heading and list of questions and answers."
msgstr "Frågeavsnitt med rubrik och en lista med frågor och svar."

#: patterns/text-faqs.php
msgctxt "Pattern title"
msgid "FAQs"
msgstr "Svar på vanliga frågor"

#: patterns/testimonials-large.php:47
msgctxt "Alt text for testimonial image."
msgid "Picture of a person typing on a typewriter."
msgstr "Bild på en person som skriver på skrivmaskin."

#: patterns/testimonials-large.php:24
msgctxt "Testimonial heading."
msgid "What people are saying"
msgstr "Recension"

#: patterns/testimonials-large.php
msgctxt "Pattern description"
msgid "A testimonial with a large image on the right."
msgstr "Recension med stor bild på höger sida."

#: patterns/testimonials-large.php
msgctxt "Pattern title"
msgid "Review with large image on right"
msgstr "Recension med stor bild till höger"

#: patterns/testimonials-6-col.php:18
msgctxt "Testimonial section heading."
msgid "What people are saying"
msgstr "Recensioner"

#: patterns/testimonials-6-col.php
msgctxt "Pattern description"
msgid "A section with three columns and two rows, each containing a testimonial and citation."
msgstr "En sektion med tre kolumner och två rader, där varje ruta innehåller en recension och källa."

#: patterns/testimonials-6-col.php
msgctxt "Pattern title"
msgid "3 column layout with 6 testimonials"
msgstr "Layout i 3 kolumner med 6 recensioner"

#: patterns/testimonials-2-col.php:67 patterns/testimonials-6-col.php:34
#: patterns/testimonials-6-col.php:51 patterns/testimonials-6-col.php:68
#: patterns/testimonials-6-col.php:89 patterns/testimonials-6-col.php:104
#: patterns/testimonials-6-col.php:119
msgctxt "Sample testimonial citation."
msgid "Otto Reid <br><sub>Springfield, IL</sub>"
msgstr "Otto Reid <br><sub>Springfield, IL, USA</sub>"

#: patterns/testimonials-2-col.php:65 patterns/testimonials-6-col.php:30
#: patterns/testimonials-6-col.php:47 patterns/testimonials-6-col.php:64
#: patterns/testimonials-6-col.php:85 patterns/testimonials-6-col.php:101
#: patterns/testimonials-6-col.php:116
msgctxt "Sample testimonial."
msgid "“Amazing quality and care. I love all your products.”"
msgstr "”Fantastisk kvalitet och omsorg. Jag älskar verkligen era produkter.”"

#: patterns/testimonials-2-col.php:38 patterns/testimonials-large.php:36
msgctxt "Sample testimonial citation."
msgid "Jo Mulligan <br /><sub>Atlanta, GA</sub>"
msgstr "Jo Mulligan <br /><sub>Atlanta, GA, USA</sub>"

#: patterns/testimonials-2-col.php:36 patterns/testimonials-large.php:32
msgctxt "Sample testimonial."
msgid "“Superb product and customer service!”"
msgstr "”Utmärkt produkt och perfekt service!”"

#: patterns/testimonials-2-col.php:26 patterns/testimonials-2-col.php:55
msgctxt "Alt text for testimonial image."
msgid "Picture of a person"
msgstr "Bild av en person"

#: patterns/testimonials-2-col.php
msgctxt "Pattern description"
msgid "Two columns with testimonials and avatars."
msgstr "Två kolumner med recensioner och avatarer."

#: patterns/testimonials-2-col.php
msgctxt "Pattern title"
msgid "2 columns with avatar"
msgstr "2 kolumner med avatar"

#: patterns/template-single-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned single post"
msgstr "Högerställt enstaka inlägg"

#: patterns/template-single-text-blog.php
msgctxt "Pattern title"
msgid "Text blog single post"
msgstr "Enstaka inlägg i textbaserad blogg"

#: patterns/template-single-photo-blog.php:79
msgid "Next Photo"
msgstr "Nästa foto"

#: patterns/template-single-photo-blog.php:78
msgid "Previous Photo"
msgstr "Föregående foto"

#: patterns/template-single-photo-blog.php:61
msgctxt "Prefix before one or more tags. The tags are displayed in a separate block on the next line."
msgid "Tagged:"
msgstr "Etiketter:"

#: patterns/template-single-photo-blog.php:53
msgctxt "Prefix before one or more categories. The categories are displayed in a separate block on the next line."
msgid "Categories:"
msgstr "Kategorier:"

#: patterns/template-single-photo-blog.php:42
msgctxt "Prefix before the author name. The post author name is displayed in a separate block on the next line."
msgid "Posted by"
msgstr "av"

#: patterns/template-single-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog single post"
msgstr "Enstaka inlägg i fotoblogg"

#: patterns/template-single-offset.php:40
#: patterns/template-single-photo-blog.php:36
msgctxt "Prefix before the post date block."
msgid "Published on"
msgstr "Publicerat"

#: patterns/template-single-offset.php
msgctxt "Pattern title"
msgid "Offset post without featured image"
msgstr "Offset-inlägg utan utvald bild"

#: patterns/template-single-news-blog.php
msgctxt "Pattern title"
msgid "News blog single post with sidebar"
msgstr "Enstaka inlägg i nyhetsblogg med sidopanel"

#: patterns/template-home-with-sidebar-news-blog.php:88
#: patterns/template-single-left-aligned-content.php:56
#: patterns/template-single-news-blog.php:39
msgctxt "Separator between date and categories."
msgid "·"
msgstr " – "

#: patterns/template-single-left-aligned-content.php:31
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "by"
msgstr "av"

#: patterns/template-single-left-aligned-content.php
msgctxt "Pattern title"
msgid "Post with left-aligned content"
msgstr "Inlägg med vänsterställt innehåll"

#: patterns/template-search-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned blog, search"
msgstr "Sökning i högerställd blogg"

#: patterns/template-search-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog search results"
msgstr "Sökresultat för fotoblogg"

#: patterns/template-search-news-blog.php
msgctxt "Pattern title"
msgid "News blog search results"
msgstr "Sökresultat för nyhetsblogg"

#: patterns/template-query-loop.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column, with featured image and post date."
msgstr "Lista med inlägg, 1 kolumn, med utvald bild och inläggets datum."

#: patterns/template-query-loop.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Inläggslista, 1 kolumn"

#: patterns/template-page-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned page"
msgstr "Högerställd sida"

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns, with only featured images."
msgstr "En lista med inlägg, 3 kolumner, med endast utvalda bilder."

#: patterns/template-query-loop-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog posts"
msgstr "Inlägg i fotoblogg"

#: patterns/template-query-loop-news-blog.php:49
msgid "Older Posts"
msgstr "Tidigare inlägg"

#: patterns/template-query-loop-news-blog.php:45
msgid "Newer Posts"
msgstr "Senare inlägg"

#: patterns/template-query-loop-news-blog.php:30
msgctxt "Prefix before the author name. The post author name is displayed in a separate block."
msgid "Written by"
msgstr "av"

#: patterns/template-query-loop-news-blog.php
msgctxt "Pattern title"
msgid "News blog query loop"
msgstr "Query-loop för nyhetsblogg"

#: patterns/template-page-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog page"
msgstr "Sida i fotoblogg"

#: patterns/template-home-with-sidebar-news-blog.php:42
msgid "The Latest"
msgstr "Det senaste"

#: patterns/template-home-with-sidebar-news-blog.php
msgctxt "Pattern title"
msgid "News blog with sidebar"
msgstr "Nyhetsblogg med sidopanel"

#: patterns/template-home-text-blog.php
msgctxt "Pattern title"
msgid "Text blog home"
msgstr "Startsida för textbaserad blogg"

#: patterns/template-home-posts-grid-news-blog.php:114
msgid "Architecture"
msgstr "Arkitektur"

#: patterns/template-home-posts-grid-news-blog.php
msgctxt "Pattern title"
msgid "News blog with featured posts grid"
msgstr "Nyhetsblogg i rutnätslayout med utvalda inlägg"

#: patterns/template-home-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog home"
msgstr "Startsida för fotoblogg"

#: patterns/template-home-news-blog.php
msgctxt "Pattern title"
msgid "News blog home"
msgstr "Startsida för nyhetsblogg"

#: patterns/template-archive-text-blog.php
msgctxt "Pattern title"
msgid "Text blog archive"
msgstr "Arkiv för textbaserad blogg"

#: patterns/template-archive-photo-blog.php
msgctxt "Pattern title"
msgid "Photo blog archive"
msgstr "Arkiv för fotoblogg"

#: patterns/template-archive-news-blog.php
msgctxt "Pattern title"
msgid "News blog archive"
msgstr "Bloggarkiv för nyheter"

#: patterns/template-404-vertical-header-blog.php
msgctxt "Pattern title"
msgid "Right-aligned 404"
msgstr "Högerjusterad 404-sida"

#: patterns/services-team-photos.php:50
msgid "Man in hat, standing in front of a building."
msgstr "Man i hatt som står framför en byggnad."

#: patterns/services-team-photos.php:44
msgid "Picture of a person typing on a typewriter."
msgstr "Bild på en person som skriver på skrivmaskin."

#: patterns/services-team-photos.php:38
msgid "Portrait of a nurse"
msgstr "Porträtt av en sjuksköterska"

#: patterns/services-team-photos.php:21
msgid "Our small team is a group of driven, detail-oriented people who are passionate about their customers."
msgstr "Vårt lilla team består av kunniga, detaljinriktade medarbetare som älskar sina kunder."

#: patterns/services-team-photos.php
msgctxt "Pattern description"
msgid "Display team photos in a services section with grid layout."
msgstr "Visa teamfoton i ett tjänsteavsnitt med rutnätslayout."

#: patterns/services-team-photos.php
msgctxt "Pattern title"
msgid "Services, team photos"
msgstr "Tjänster, foton på teamet"

#: patterns/services-subscriber-only-section.php:69
msgid "Smartphones capturing a scenic wildflower meadow with trees"
msgstr "Smartphones fotograferar en naturskön äng med vilda blommor och träd"

#: patterns/services-subscriber-only-section.php:55
msgid "View plans"
msgstr "Visa paket"

#: patterns/services-subscriber-only-section.php:21
msgid "Subscribe to get unlimited access"
msgstr "Prenumerera för obegränsad tillgång"

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern description"
msgid "A subscriber-only section highlighting exclusive services and offerings."
msgstr "Ett avsnitt avsett endast för prenumeranter, som lyfter fram exklusiva tjänster och erbjudanden."

#: patterns/services-subscriber-only-section.php
msgctxt "Pattern title"
msgid "Services, subscriber only section"
msgstr "Tjänster, avsnitt endast för prenumeranter"

#: patterns/services-3-col.php:68
msgid "Deliver"
msgstr "Leverans"

#: patterns/services-3-col.php:50
msgid "Assemble"
msgstr "Montering"

#: patterns/services-3-col.php:36 patterns/services-3-col.php:54
#: patterns/services-3-col.php:72
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience"
msgstr "På samma sätt som blommor kan slå ut på oväntade platser har varje berättelse en skönhet och styrka"

#: patterns/services-3-col.php:32
msgid "Collect"
msgstr "Upphämtning"

#: patterns/services-3-col.php:27 patterns/services-3-col.php:45
#: patterns/services-3-col.php:63
msgid "Image for service"
msgstr "Illustration för tjänst"

#: patterns/services-3-col.php:17
msgid "Our services"
msgstr "Våra tjänster"

#: patterns/services-3-col.php
msgctxt "Pattern description"
msgid "Three columns with images and text to showcase services."
msgstr "Tre kolumner med bilder och text för att visa upp tjänster."

#: patterns/services-3-col.php
msgctxt "Pattern title"
msgid "Services, 3 columns"
msgstr "Tjänster, 3 kolumner"

#: patterns/pricing-3-col.php:125
msgid "40€"
msgstr "40 €"

#: patterns/pricing-3-col.php:117
msgid "Get access to our paid newsletter and an unlimited pass."
msgstr "Få tillgång till vårt betalda nyhetsbrev och en obegränsad evenemangsbiljett."

#: patterns/pricing-3-col.php:113
msgctxt "Name of membership package."
msgid "Expert"
msgstr "Expert"

#: patterns/pricing-3-col.php:89 patterns/pricing-3-col.php:129
msgid "Month"
msgstr "Månad"

#: patterns/pricing-3-col.php:85
msgid "20€"
msgstr "20 €"

#: patterns/pricing-3-col.php:77
msgid "Get access to our paid newsletter and a limited pass for one event."
msgstr "Få tillgång till vårt betalda nyhetsbrev och en begränsad biljett till ett evenemang."

#: patterns/pricing-3-col.php:41
msgid "Get access to our free articles and weekly newsletter."
msgstr "Få tillgång till våra kostnadsfria artiklar och veckoutskick."

#: patterns/pricing-3-col.php:19
msgid "Choose your membership"
msgstr "Välj önskat medlemskap"

#: patterns/pricing-3-col.php
msgctxt "Pattern description"
msgid "A three-column boxed pricing table designed to showcase services, descriptions, and pricing options."
msgstr "Inramad pristabell med tre kolumner, utformad för att visa upp tjänster, beskrivningar och prisalternativ."

#: patterns/pricing-3-col.php
msgctxt "Pattern title"
msgid "Pricing, 3 columns"
msgstr "Priser, 3 kolumner"

#: patterns/pricing-2-col.php:82
msgid "20€/month"
msgstr "20 €/månad"

#: patterns/pricing-2-col.php:78 patterns/pricing-3-col.php:73
msgctxt "Name of membership package."
msgid "Single"
msgstr "Enstaka"

#: patterns/pricing-2-col.php:68 patterns/pricing-2-col.php:112
#: patterns/pricing-3-col.php:59 patterns/pricing-3-col.php:99
#: patterns/pricing-3-col.php:139
msgctxt "Button text, refers to joining a community. Verb."
msgid "Join"
msgstr "Delta"

#: patterns/pricing-2-col.php:60 patterns/pricing-2-col.php:104
#: patterns/services-subscriber-only-section.php:43
msgid "Join our forums."
msgstr "Delta i våra forum."

#: patterns/pricing-2-col.php:56 patterns/pricing-2-col.php:100
#: patterns/services-subscriber-only-section.php:39
msgid "An elegant addition of home decor collection."
msgstr "Ett elegant tillskott till heminredningskollektionen."

#: patterns/pricing-2-col.php:52 patterns/pricing-2-col.php:96
#: patterns/services-subscriber-only-section.php:35
msgid "Get a free tote bag."
msgstr "Du får en gratis tygväska"

#: patterns/pricing-2-col.php:48 patterns/pricing-2-col.php:92
#: patterns/services-subscriber-only-section.php:31
msgid "Join our IRL events."
msgstr "Delta i våra offline-evenemang."

#: patterns/pricing-2-col.php:44 patterns/pricing-2-col.php:88
#: patterns/services-subscriber-only-section.php:27
msgid "Get access to our paid articles and weekly newsletter."
msgstr "Få tillgång till våra betalda artiklar och veckoutskick."

#: patterns/pricing-2-col.php:38 patterns/pricing-3-col.php:49
msgid "0€"
msgstr "0 €"

#: patterns/pricing-2-col.php:34 patterns/pricing-3-col.php:37
msgid "Free"
msgstr "Utan kostnad"

#: patterns/pricing-2-col.php:22
#: patterns/services-subscriber-only-section.php:61
msgid "Cancel or pause anytime."
msgstr "Avbryt eller pausa när som helst."

#: patterns/pricing-2-col.php:18 patterns/pricing-3-col.php:23
msgid "Pricing"
msgstr "Priser"

#: patterns/pricing-2-col.php
msgctxt "Pattern description"
msgid "Pricing section with two columns, pricing plan, description, and call-to-action buttons."
msgstr "Prisavsnitt med två kolumner, prispaket, beskrivning och call-to-action-knappar."

#: patterns/pricing-2-col.php
msgctxt "Pattern title"
msgid "Pricing, 2 columns"
msgstr "Prislista, 2 kolumner"

#: patterns/post-navigation.php:17 patterns/post-navigation.php:18
#: patterns/template-single-left-aligned-content.php:78
#: patterns/template-single-left-aligned-content.php:79
#: patterns/template-single-news-blog.php:95
#: patterns/template-single-news-blog.php:96
#: patterns/template-single-offset.php:61
#: patterns/template-single-offset.php:62
#: patterns/template-single-photo-blog.php:76
#: patterns/template-single-photo-blog.php:77
#: patterns/template-single-text-blog.php:36
#: patterns/template-single-text-blog.php:37
#: patterns/template-single-vertical-header-blog.php:82
#: patterns/template-single-vertical-header-blog.php:83
msgid "Post navigation"
msgstr "Inläggsnavigering"

#: patterns/post-navigation.php
msgctxt "Pattern description"
msgid "Next and previous post links."
msgstr "Länkar till nästa och föregående inlägg."

#: patterns/post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Inläggsnavigering"

#: patterns/page-shop-home.php
msgctxt "Pattern description"
msgid "A shop homepage pattern."
msgstr "Mönster för en butiks startsida."

#: patterns/page-shop-home.php
msgctxt "Pattern title"
msgid "Shop homepage"
msgstr "Startsida för butik"

#: patterns/page-portfolio-home.php:229
msgctxt "Phone number."
msgid "****** 349 1806"
msgstr "****** 349 1806"

#: patterns/page-portfolio-home.php:229
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/page-portfolio-home.php:27
msgid "My name is Anna Möller and these are some of my photo projects."
msgstr "Jag heter Anna Möller och det här är några av mina fotoprojekt."

#: patterns/page-portfolio-home.php
msgctxt "Pattern description"
msgid "A portfolio homepage pattern."
msgstr "Mönster för en portföljs startsida."

#: patterns/page-portfolio-home.php
msgctxt "Pattern title"
msgid "Portfolio homepage"
msgstr "Startsida för portfölj"

#: patterns/page-link-in-bio-with-tight-margins.php:42
msgid "I’m Asahachi Kōno, a Japanese photographer, a member of Los Angeles’s Japanese Camera Pictorialists of California. Before returning to Japan, I worked as a photo retoucher."
msgstr "Jag heter Asahachi Kōno, är japansk fotograf och medlem av Japanese Camera Pictorialists of California i Los Angeles. Innan jag återvände till Japan arbetade jag som retuschör."

#: patterns/page-link-in-bio-with-tight-margins.php:27
msgid "Black and white photo focusing on a woman and a child from afar."
msgstr "Svartvitt foto med fokus på en kvinna och ett barn på långt håll."

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern description"
msgid "A full-width, full-height link in bio section with an image, a paragraph and social links."
msgstr "En länk i full bredd och höjd i biografiavsnittet med en bild, ett textstycke och sociala länkar."

#: patterns/page-link-in-bio-with-tight-margins.php
msgctxt "Pattern title"
msgid "Link in bio with tight margins"
msgstr "Länk i biografi med snäva marginaler"

#: patterns/page-link-in-bio-wide-margins.php:38
msgctxt "Pattern placeholder text."
msgid "I’m Nora, a dedicated public interest attorney based in Denver. I’m a graduate of Stanford University."
msgstr "Jag heter Nora och är en advokat som arbetar med allmänintresse i Denver. Jag är utbildad vid Stanford University."

#: patterns/page-link-in-bio-wide-margins.php:34
msgid "Nora Winslow Keene"
msgstr "Nora Winslow Keene"

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern description"
msgid "A link in bio landing page with social links, a profile photo and a brief description."
msgstr "En länk på en landningssida med biografi med sociala länkar, ett profilfoto och en kort beskrivning."

#: patterns/page-link-in-bio-wide-margins.php
msgctxt "Pattern title"
msgid "Link in bio with profile, links and wide margins"
msgstr "Länk i biografi med profil, länkar och breda marginaler"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:57
msgid "Photo of a woman worker."
msgstr "Foto av en kvinnlig arbetare."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:30
msgid "Lewis W. Hine studied sociology before moving to New York in 1901 to work at the Ethical Culture School, where he took up photography to enhance his teaching practices"
msgstr "Lewis W. Hine studerade sociologi innan han 1901 flyttade till New York och började arbeta på Ethical Culture School, där han började fotografera för att kunna undervisa bättre"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php:26
msgid "Lewis Hine"
msgstr "Lewis Hine"

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern description"
msgid "A link in bio landing page with a heading, paragraph, links and a full height image."
msgstr "Länk på landningssida med biografi, med rubrik, textstycke, länkar och en bild i full höjd."

#: patterns/page-link-in-bio-heading-paragraph-links-image.php
msgctxt "Pattern title"
msgid "Link in bio heading, paragraph, links and full-height image"
msgstr "Länk i biografirubrik, textstycke, länkar och bild i fullhöjd"

#: patterns/page-landing-podcast.php
msgctxt "Pattern description"
msgid "A landing page for the podcast with a hero section, description, logos, grid with videos and newsletter signup."
msgstr "Landningssida för en podcast med hero-sektion, beskrivning, logotyper, rutnät med videoklipp och registrering för nyhetsbrev."

#: patterns/page-landing-podcast.php
msgctxt "Pattern title"
msgid "Landing page for podcast"
msgstr "Landningssida för en podcast"

#: patterns/page-landing-event.php
msgctxt "Pattern description"
msgid "A landing page for the event with a hero section, description, FAQs and call to action."
msgstr "Landningssida för ett evenemang med en hero-sektion, beskrivning, svar på vanliga frågor och uppmaning till handling (CTA)."

#: patterns/page-landing-event.php
msgctxt "Pattern title"
msgid "Landing page for event"
msgstr "Landningssida för ett evenemang"

#: patterns/page-landing-book.php
msgctxt "Pattern description"
msgid "A landing page for the book with a hero section, pre-order links, locations, FAQs and newsletter signup."
msgstr "En landningssida för en bok, med en hero-sektion, länkar för förhandsbeställning, platser, svar på vanliga frågor och registrering för nyhetsbrev."

#: patterns/page-landing-book.php
msgctxt "Pattern title"
msgid "Landing page for book"
msgstr "Landningssida för en bok"

#: patterns/page-cv-bio.php:47
msgctxt "Link to a page with information about what the person is working on right now."
msgid "Now"
msgstr "Pågående"

#: patterns/page-cv-bio.php:47
msgid "LinkedIn"
msgstr "LinkedIn"

#: patterns/page-cv-bio.php:43 patterns/page-link-in-bio-wide-margins.php:24
#: patterns/services-team-photos.php:32
msgid "Woman on beach, splashing water."
msgstr "Kvinna på en strand, vatten som stänker."

#: patterns/page-cv-bio.php:31
msgctxt "Pattern placeholder text."
msgid "My name is Nora Winslow Keene, and I’m a committed public interest attorney. Living in Denver, Colorado, I’ve spent years championing the rights of underrepresented workers. A graduate of Stanford University, I played a key role in securing critical protections for agricultural laborers, ensuring better wages and access to healthcare. My work has focused on advocating for environmental justice and improving the quality of life for rural communities. Every case I take on is driven by the belief that everyone deserves dignity and fair treatment in the workplace."
msgstr "Jag heter Nora Winslow Keene och jag är en engagerad advokat som arbetar med samhällsfrågor. Jag bor i Denver, Colorado, och har tillbringat många år med att kämpa för underrepresenterade arbetstagares rättigheter. Jag har en examen från Stanford University och spelade en nyckelroll i att säkra viktiga tryggheter för jordbruksarbetare, vilket säkerställde bättre löner och tillgång till hälso- och sjukvård. Mitt arbete har fokuserat på att förespråka miljörättvisa och förbättra livskvaliteten för landsbygdssamhällen. Varje fall jag tar mig an drivs av övertygelsen om att alla förtjänar värdighet och rättvis behandling på arbetsplatsen."

#: patterns/page-cv-bio.php:28
msgctxt "Example heading in pattern."
msgid "Hey,"
msgstr "Hallå där."

#: patterns/page-cv-bio.php
msgctxt "Pattern description"
msgid "A pattern for a CV/Bio landing page."
msgstr "Mönster för en landningssida med CV/biografi."

#: patterns/page-cv-bio.php
msgctxt "Pattern title"
msgid "CV/bio"
msgstr "CV/biografi"

#: patterns/page-coming-soon.php:33
msgid "Subscribe to get notified when our website is ready."
msgstr "Prenumerera för att få ett meddelande när vår webbplats är klar."

#: patterns/page-coming-soon.php:29
msgid "Something great is coming soon"
msgstr "Något stort kommer snart"

#: patterns/page-coming-soon.php:24
msgid "Event"
msgstr "Evenemang"

#: patterns/page-coming-soon.php
msgctxt "Pattern description"
msgid "A full-width cover banner that can be applied to a page or it can work as a single landing page."
msgstr "En omslagsbanner i full bredd som kan användas på en enstaka sida eller kan fungera som en webbplats med en sida/landningssida."

#: patterns/page-coming-soon.php
msgctxt "Pattern title"
msgid "Coming soon"
msgstr "Kommer snart"

#: patterns/page-business-home.php
msgctxt "Pattern description"
msgid "A business homepage pattern."
msgstr "Ett mönster för en företagshemsida."

#: patterns/page-business-home.php
msgctxt "Pattern title"
msgid "Business homepage"
msgstr "Företagshemsida"

#: patterns/overlapped-images.php
msgctxt "Pattern description"
msgid "A section with overlapping images, and a description."
msgstr "Ett avsnitt med överlappande bilder och en beskrivning."

#: patterns/overlapped-images.php
msgctxt "Pattern title"
msgid "Overlapping images and paragraph on right"
msgstr "Överlappande bilder och textstycke till höger"

#: patterns/more-posts.php:18
msgid "More posts"
msgstr "Fler inlägg"

#: patterns/more-posts.php
msgctxt "Pattern description"
msgid "Displays a list of posts with title and date."
msgstr "Visar en inläggslista med rubrik och datum."

#: patterns/more-posts.php
msgctxt "Pattern title"
msgid "More posts"
msgstr "Fler inlägg"

#: patterns/media-instagram-grid.php:56
msgid "Close up of two flowers on a dark background."
msgstr "Närbild av två blommor mot mörk bakgrund."

#: patterns/media-instagram-grid.php:48
msgid "Portrait of an African Woman dressed in traditional costume, wearing decorative jewelry."
msgstr "Porträtt av afrikansk kvinna i traditionell dräkt och med dekorativa smycken."

#: patterns/media-instagram-grid.php:40
msgid "Profile portrait of a native person."
msgstr "Urinvånare i profil."

#: patterns/media-instagram-grid.php:28
msgctxt "Example username for social media account."
msgid "@example"
msgstr "@example"

#: patterns/media-instagram-grid.php
msgctxt "Pattern description"
msgid "A grid section with photos and a link to an Instagram profile."
msgstr "Ett rutnätsavsnitt med foton och länk till en Instagram-profil."

#: patterns/media-instagram-grid.php
msgctxt "Pattern title"
msgid "Instagram grid"
msgstr "Instagram-rutnät"

#: patterns/logos.php:17
msgid "The Stories Podcast is sponsored by"
msgstr "The Stories – Podcast sponsras av"

#: patterns/logos.php
msgctxt "Pattern description"
msgid "Showcasing the podcast's clients with a heading and a series of client logos."
msgstr "Podcastens kunder presenteras med en rubrik och en serie kundlogotyper."

#: patterns/logos.php
msgctxt "Pattern title"
msgid "Logos"
msgstr "Logotyper"

#: patterns/hidden-written-by.php:20
msgid "in"
msgstr "i"

#: patterns/hidden-written-by.php:16
msgid "Written by "
msgstr "Skrivet av "

#: patterns/hidden-written-by.php
msgctxt "Pattern title"
msgid "Written by"
msgstr "Skrivet av"

#: patterns/hidden-sidebar.php:38 patterns/page-portfolio-home.php:65
#: patterns/page-portfolio-home.php:87 patterns/page-portfolio-home.php:121
#: patterns/page-portfolio-home.php:154 patterns/page-portfolio-home.php:176
#: patterns/page-portfolio-home.php:203 patterns/template-home-news-blog.php:40
#: patterns/template-home-posts-grid-news-blog.php:35
#: patterns/template-home-posts-grid-news-blog.php:60
#: patterns/template-home-posts-grid-news-blog.php:78
#: patterns/template-home-posts-grid-news-blog.php:103
#: patterns/template-home-with-sidebar-news-blog.php:62
#: patterns/template-home-with-sidebar-news-blog.php:119
#: patterns/template-query-loop-news-blog.php:55
#: patterns/template-query-loop-photo-blog.php:22
#: patterns/template-query-loop-text-blog.php:19
#: patterns/template-query-loop-vertical-header-blog.php:47
#: patterns/template-query-loop.php:31
msgctxt "Message explaining that there are no results returned from a search."
msgid "Sorry, but nothing was found. Please try a search with different keywords."
msgstr "Sökningen gav inget resultat. Försök med några andra nyckelord."

#: patterns/hidden-sidebar.php:37
#: patterns/template-home-posts-grid-news-blog.php:34
#: patterns/template-home-with-sidebar-news-blog.php:61
msgid "Add text or blocks that will display when a query returns no results."
msgstr "Lägg till text eller block som kommer att visas när en sökning inte gav något resultat."

#: patterns/hidden-sidebar.php:14
msgid "Other Posts"
msgstr "Andra inlägg"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Sidopanel"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Button text. Verb."
msgid "Search"
msgstr "Sök"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Sök"

#: patterns/hidden-blog-heading.php
msgctxt "Pattern description"
msgid "Hidden heading for the home page and index template."
msgstr "Dold rubrik för startsides- och indexmallen."

#: patterns/hidden-blog-heading.php
msgctxt "Pattern title"
msgid "Hidden blog heading"
msgstr "Dold bloggrubrik"

#: patterns/hidden-404.php:36
msgctxt "404 error message"
msgid "The page you are looking for doesn't exist, or it has been moved. Please try searching using the form below."
msgstr "Sidan du försökte öppna finns inte, eller har flyttat. Prova att söka med hjälp av formuläret nedan."

#: patterns/hidden-404.php:32
msgctxt "404 error message"
msgid "Page not found"
msgstr "Sidan hittades inte"

#: patterns/hidden-404.php:21
msgctxt "image description"
msgid "Small totara tree on ridge above Long Point"
msgstr "Litet totaraträd på åsen ovanför Long Point"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/hero-podcast.php:65
msgctxt "Button text"
msgid "RSS"
msgstr "RSS"

#: patterns/hero-podcast.php:61
msgctxt "Button text"
msgid "Pocket Casts"
msgstr "Pocket Casts"

#: patterns/hero-podcast.php:57
msgctxt "Button text"
msgid "Spotify"
msgstr "Spotify"

#: patterns/hero-podcast.php:53
msgctxt "Button text"
msgid "Apple Podcasts"
msgstr "Apple Podcasts"

#: patterns/hero-podcast.php:49
msgctxt "Button text"
msgid "YouTube"
msgstr "YouTube"

#: patterns/hero-podcast.php:43
msgid "Subscribe on your favorite platform"
msgstr "Prenumerera via din favoritplattform"

#: patterns/hero-podcast.php:36
msgctxt "Podcast description"
msgid "Storytelling, expert analysis, and vivid descriptions. The Stories Podcast brings history to life, making it accessible and engaging for a global audience."
msgstr "Berättelser, expertanalyser och inlevande beskrivningar. The Stories – Podcast väcker historien till liv och gör den tillgänglig och engagerande för en global publik."

#: patterns/hero-podcast.php:32
msgid "The Stories Podcast"
msgstr "The Stories – Podcast"

#: patterns/hero-podcast.php:22
msgctxt "Alt text for hero image."
msgid "Picture of a person"
msgstr "Bild av en person"

#: patterns/hero-podcast.php
msgctxt "Pattern title"
msgid "Hero podcast"
msgstr "Hero-avsnitt för podcast"

#: patterns/hero-overlapped-book-cover-with-links.php:113
msgid "Book Image"
msgstr "Bild av bok"

#: patterns/hero-overlapped-book-cover-with-links.php:34
msgctxt "Hero - Overlapped book cover pattern subline text"
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "En fin samling av ögonblick med fotografier av Louis Fleckenstein, Paul Strand och Asahachi Kōno."

#: patterns/hero-overlapped-book-cover-with-links.php:28
msgctxt "Hero - Overlapped book cover pattern headline text"
msgid "The Stories Book"
msgstr "The Stories Book"

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern description"
msgid "A hero with an overlapped book cover and links."
msgstr "Hero-avsnitt med överlappande bokomslag och länkar."

#: patterns/hero-overlapped-book-cover-with-links.php
msgctxt "Pattern title"
msgid "Hero, overlapped book cover with links"
msgstr "Hero-avsnitt, överlappande bokomslag med länkar"

#: patterns/hero-full-width-image.php:27
msgctxt "Sample hero paragraph"
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "På samma sätt som blommor kan slå ut på oväntade platser har varje berättelse en skönhet och styrka som kan avslöja dolda underverk."

#: patterns/hero-full-width-image.php:23
msgctxt "Sample hero heading"
msgid "Tell your story"
msgstr "Berätta din historia"

#: patterns/hero-full-width-image.php:18
msgctxt "Alt text for cover image."
msgid "Picture of a flower"
msgstr "Bild av en blomma"

#: patterns/hero-full-width-image.php
msgctxt "Pattern description"
msgid "A hero with a full width image, heading, short paragraph and button."
msgstr "Hero-avsnitt med bild i full bredd, rubrik, kort textstycke och knapp."

#: patterns/hero-full-width-image.php
msgctxt "Pattern title"
msgid "Hero, full width image"
msgstr "Hero, bild i full bredd"

#: patterns/hero-book.php:46
msgctxt "CTA text of the hero section."
msgid "Available for pre-order now."
msgstr "Du kan förbeställa nu."

#: patterns/hero-book.php:42
msgctxt "Content of the hero section."
msgid "A fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno."
msgstr "En fin samling av ögonblick med fotografier av Louis Fleckenstein, Paul Strand och Asahachi Kōno."

#: patterns/hero-book.php:38
msgctxt "Heading of the hero section."
msgid "The Stories Book"
msgstr "The Stories Book"

#: patterns/hero-book.php:24
msgid "Image of the book"
msgstr "Bild på boken"

#: patterns/hero-book.php
msgctxt "Pattern description"
msgid "A hero section for the book with a description and pre-order link."
msgstr "Ett hero-avsnitt för boken med beskrivning och länk till förhandsbeställning."

#: patterns/hero-book.php
msgctxt "Pattern title"
msgid "Hero book"
msgstr "Hero book"

#: patterns/heading-and-paragraph-with-image.php:36
msgctxt "Alt text for Overview picture."
msgid "Cliff Palace, Colorado"
msgstr "Cliff Palace, Colorado"

#: patterns/heading-and-paragraph-with-image.php:27
msgctxt "Event Overview Text."
msgid "Held over a weekend, the event is structured around a series of exhibitions, workshops, and panel discussions. The exhibitions showcase a curated selection of photographs that tell compelling stories from various corners of the globe, each image accompanied by detailed narratives that provide context and deeper insight into the historical significance of the scenes depicted. These photographs are drawn from the archives of renowned photographers, as well as emerging talents, ensuring a blend of both classical and contemporary perspectives."
msgstr "Evenemanget genomförs över en helg och är uppbyggt kring en serie utställningar, seminarier och paneldiskussioner. Utställningarna visar handplockade fotografier som berättar fängslande historier från olika delar av världen, och varje bild åtföljs av detaljerade berättelser som ger sammanhang och djupare insikt i den historiska betydelsen av de scener som avbildas. Fotografierna är hämtade från såväl kända fotografers arkiv som från nya talanger, vilket ger en blandning av både klassiska och samtida perspektiv."

#: patterns/heading-and-paragraph-with-image.php:23
msgid "About the event"
msgstr "Om evenemanget"

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern description"
msgid "A two-column section with a heading and paragraph on the left, and an image on the right."
msgstr "Ett tvåspaltigt avsnitt med en rubrik och ett textstycke till vänster och en bild till höger."

#: patterns/heading-and-paragraph-with-image.php
msgctxt "Pattern title"
msgid "Heading and paragraph with image on the right"
msgstr "Rubrik och textstycke med bild till höger"

#: patterns/header.php
msgctxt "Pattern description"
msgid "Site header with site title and navigation."
msgstr "Webbplatshuvud med webbplatsens rubrik och navigering."

#: patterns/header.php
msgctxt "Pattern title"
msgid "Header"
msgstr "Sidhuvud"

#: patterns/header-large-title.php
msgctxt "Pattern description"
msgid "Site header with large site title and right-aligned navigation."
msgstr "Webbplatshuvud med stor webbplatsrubrik och högerställd navigering."

#: patterns/header-large-title.php
msgctxt "Pattern title"
msgid "Header with large title"
msgstr "Sidhuvud med stor rubrik"

#: patterns/header-columns.php
msgctxt "Pattern description"
msgid "Site header with site title and navigation in columns."
msgstr "Webbplatshuvud med webbplatsens rubrik och navigering i kolumner."

#: patterns/header-columns.php
msgctxt "Pattern title"
msgid "Header with columns"
msgstr "Sidhuvud med kolumner"

#: patterns/header-centered.php
msgctxt "Pattern description"
msgid "Site header with centered site title and navigation."
msgstr "Webbplatshuvud med centrerad webbplatsrubrik och navigering."

#: patterns/header-centered.php
msgctxt "Pattern title"
msgid "Centered site header"
msgstr "Centrerat webbplatshuvud"

#: patterns/grid-with-categories.php:64
msgid "Sunflowers"
msgstr "Solrosor"

#: patterns/grid-with-categories.php:50
msgid "Cactus"
msgstr "Kaktus"

#: patterns/grid-with-categories.php:36
msgid "Anthuriums"
msgstr "Anthurium"

#: patterns/grid-with-categories.php:29
msgid "Close up of a red anthurium."
msgstr "Närbild av röd anthurium."

#: patterns/grid-with-categories.php:22
msgid "Top Categories"
msgstr "Toppkategorier"

#: patterns/grid-with-categories.php
msgctxt "Pattern description"
msgid "A grid section with different categories."
msgstr "En rutnätsavsnitt med olika kategorier."

#: patterns/grid-with-categories.php
msgctxt "Pattern title"
msgid "Grid with categories"
msgstr "Rutnät med kategorier"

#: patterns/grid-videos.php:23
msgid "Podcast"
msgstr "Podcast"

#: patterns/grid-videos.php:19
msgid "Explore the episodes"
msgstr "Utforska avsnitten"

#: patterns/grid-videos.php
msgctxt "Pattern description"
msgid "A grid with videos."
msgstr "Rutnät med videoklipp."

#: patterns/grid-videos.php
msgctxt "Pattern title"
msgid "Grid with videos"
msgstr "Rutnät med videoklipp"

#: patterns/format-link.php:23
msgid "https://example.com"
msgstr "https://example.com"

#: patterns/format-link.php:17
msgid "The Stories Book, a fine collection of moments in time featuring photographs from Louis Fleckenstein, Paul Strand and Asahachi Kōno, is available for pre-order"
msgstr "The Stories Book – en fin samling av ögonblick med fotografier av Louis Fleckenstein, Paul Strand och Asahachi Kōno, tillgänglig för förbeställning"

#: patterns/format-link.php
msgctxt "Pattern description"
msgid "A link post format with a description and an emphasized link for key content."
msgstr "Ett inläggsformat för länkar med beskrivning och framhävd länk som huvudinnehåll."

#: patterns/format-link.php
msgctxt "Pattern title"
msgid "Link format"
msgstr "Länkformat"

#: patterns/format-audio.php:30
msgid "Acoma Pueblo, in New Mexico, stands as a testament to the resilience and cultural heritage of the Acoma people"
msgstr "Acoma Pueblo i New Mexico är ett bevis på Acoma-folkets uthållighet och kulturarv"

#: patterns/format-audio.php:26
msgid "Episode 1: Acoma Pueblo with Prof. Fiona Presley"
msgstr "Avsnitt 1: Acoma Pueblo med professor Fiona Presley"

#: patterns/format-audio.php
msgctxt "Pattern description"
msgid "An audio post format with an image, title, audio player, and description."
msgstr "Ett ljudinläggsformat med bild, rubrik, ljudspelare och beskrivning."

#: patterns/format-audio.php
msgctxt "Pattern title"
msgid "Audio format"
msgstr "Ljudformat"

#: patterns/footer.php
msgctxt "Pattern description"
msgid "Footer columns with logo, title, tagline and links."
msgstr "Sidfotskolumner med logotyp, rubrik, slogan och länkar."

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer"
msgstr "Sidfot"

#: patterns/footer-social.php
msgctxt "Pattern description"
msgid "Footer with centered site title and social links."
msgstr "Sidfot med centrerad webbplatsrubrik och sociala länkar."

#: patterns/footer-social.php
msgctxt "Pattern title"
msgid "Centered footer with social links"
msgstr "Centrerad sidfot med sociala länkar"

#: patterns/footer-newsletter.php:24
msgid "Receive our articles in your inbox."
msgstr "Få våra artiklar via e-post."

#: patterns/footer-newsletter.php
msgctxt "Pattern description"
msgid "Footer with large site title and newsletter signup."
msgstr "Sidfot med webbplatsrubrik i stort format och anmälan till nyhetsbrev."

#: patterns/footer-newsletter.php
msgctxt "Pattern title"
msgid "Footer with newsletter signup"
msgstr "Sidfot med anmälan till nyhetsbrev"

#: patterns/footer-columns.php:52 patterns/footer.php:61
msgid "Themes"
msgstr "Teman"

#: patterns/footer-columns.php:51 patterns/footer.php:59
msgid "Patterns"
msgstr "Mönster"

#: patterns/footer-columns.php:50 patterns/footer.php:57
msgid "Shop"
msgstr "Butik"

#: patterns/footer-columns.php:48
msgid "Featured"
msgstr "Utvalt"

#: patterns/footer-columns.php:39 patterns/footer.php:51
msgid "Authors"
msgstr "Författare"

#: patterns/footer-columns.php:38 patterns/footer.php:49
msgid "FAQs"
msgstr "Svar på vanliga frågor"

#: patterns/footer-columns.php:37 patterns/footer.php:47
msgid "About"
msgstr "Om"

#: patterns/footer-columns.php:36 patterns/footer.php:45
#: patterns/hidden-blog-heading.php:15 patterns/template-home-text-blog.php:20
msgid "Blog"
msgstr "Blogg"

#: patterns/footer-columns.php
msgctxt "Pattern description"
msgid "Footer columns with title, tagline and links."
msgstr "Sidfotskolumner med rubrik, slogan och länkar."

#: patterns/footer-columns.php
msgctxt "Pattern title"
msgid "Footer with columns"
msgstr "Sidfot med kolumner"

#. translators: Designed with WordPress. %s: WordPress link.
#: patterns/footer-centered.php:33 patterns/footer-columns.php:73
#: patterns/footer-newsletter.php:49 patterns/footer-social.php:35
#: patterns/footer.php:82
msgid "Designed with %s"
msgstr "Utformad med %s"

#: patterns/footer-centered.php
msgctxt "Pattern description"
msgid "Footer with centered site title and tagline."
msgstr "Sidfot med centrerad webbplatsrubrik och slogan."

#: patterns/footer-centered.php
msgctxt "Pattern title"
msgid "Centered footer"
msgstr "Centrerad sidfot"

#: patterns/event-schedule.php:174
msgid "An introduction to African dialects"
msgstr "Introduktion till afrikanska dialekter"

#: patterns/event-schedule.php:163
msgid "Black and white photo of an African woman."
msgstr "Svartvitt foto av afrikansk kvinna."

#: patterns/event-schedule.php:142
msgid "Ancient buildings and symbols"
msgstr "Forntida byggnader och symboler"

#: patterns/event-schedule.php:132 patterns/media-instagram-grid.php:52
msgid "The Acropolis of Athens."
msgstr "Atens Akropolis."

#: patterns/event-schedule.php:89
msgid "Things you didn’t know about the deep ocean"
msgstr "Saker du inte visste om djuphavet"

#: patterns/event-schedule.php:78 patterns/media-instagram-grid.php:44
msgid "View of the deep ocean."
msgstr "Bild av havsdjupet."

#: patterns/event-schedule.php:65 patterns/event-schedule.php:97
#: patterns/event-schedule.php:150 patterns/event-schedule.php:182
msgctxt "Pattern placeholder text with link."
msgid "Lecture by <a href=\"#\">Prof. Fiona Presley</a>"
msgstr "Föreläsning av <a href=\"#\">Prof. Fiona Presley</a>"

#: patterns/event-schedule.php:60 patterns/event-schedule.php:92
#: patterns/event-schedule.php:145 patterns/event-schedule.php:177
msgctxt "Example event time in pattern."
msgid "9 AM — 11 AM"
msgstr "9:00 – 11:00"

#: patterns/event-schedule.php:57
msgid "Fauna from North America and its characteristics"
msgstr "Nordamerikansk fauna och dess särdrag"

#: patterns/event-schedule.php:46 patterns/media-instagram-grid.php:60
msgid "Birds on a lake."
msgstr "Fåglar på en sjö."

#: patterns/event-schedule.php:20
msgid "Agenda"
msgstr "Agenda"

#: patterns/event-schedule.php
msgctxt "Pattern description"
msgid "A section with specified dates and times for an event."
msgstr "Ett avsnitt med angivna datum och tider för ett evenemang."

#: patterns/event-schedule.php
msgctxt "Pattern title"
msgid "Event schedule"
msgstr "Evenemangskalender"

#: patterns/event-rsvp.php:91
msgid "Close up photo of white flowers on a grey background"
msgstr "Närbild av vita blommor mot grå bakgrund"

#: patterns/event-rsvp.php:81
msgctxt "Abbreviation for \"Please respond\"."
msgid "RSVP"
msgstr "Anmäl dig"

#: patterns/event-rsvp.php:73
msgid "This immersive event celebrates the universal human experience through the lenses of history and ancestry, featuring a diverse array of photographers whose works capture the essence of different cultures and historical moments."
msgstr "Detta upplevelseevenemang lyfter fram det allmängiltiga mänskliga sett genom historien och våra förfäder, genom många olika fotografer, vars verk fångar essensen av olika kulturer och historiska ögonblick."

#: patterns/event-rsvp.php:57
msgid "Free Workshop"
msgstr "Kostnadsfritt seminarium"

#: patterns/event-rsvp.php
msgctxt "Pattern description"
msgid "RSVP for an upcoming event with a cover image and event details."
msgstr "Anmälan till kommande evenemang med omslagsbild och evenemangsinformation."

#: patterns/event-rsvp.php
msgctxt "Pattern title"
msgid "Event RSVP"
msgstr "Evenemangsanmälan"

#: patterns/event-3-col.php:50 patterns/event-3-col.php:74
#: patterns/event-3-col.php:98
msgid "Event details"
msgstr "Evenemangsuppgifter"

#: patterns/event-3-col.php:34 patterns/event-3-col.php:58
#: patterns/event-3-col.php:82 patterns/format-audio.php:20
msgid "Event image"
msgstr "Evenemangsbild"

#: patterns/event-3-col.php:24 patterns/event-schedule.php:23
msgid "These are some of the upcoming events."
msgstr "Här är några av våra kommande evenemang."

#: patterns/event-3-col.php:20 patterns/footer-columns.php:49
#: patterns/footer.php:55
msgid "Events"
msgstr "Evenemang"

#: patterns/event-3-col.php
msgctxt "Pattern description"
msgid "A header with title and text and three columns that show 3 events with their images and titles."
msgstr "Sidhuvud med rubrik, text och tre kolumner som visar 3 evenemang med respektive bilder och rubriker."

#: patterns/event-3-col.php
msgctxt "Pattern title"
msgid "Events, 3 columns with event images and titles"
msgstr "Evenemang, 3 kolumner med evenemangsbilder och -rubriker"

#: patterns/cta-newsletter.php:32 patterns/footer-newsletter.php:30
#: patterns/page-coming-soon.php:39
#: patterns/services-subscriber-only-section.php:51
msgid "Subscribe"
msgstr "Prenumerera"

#: patterns/cta-newsletter.php:23
msgid "Get access to a curated collection of moments in time featuring photographs from historical relevance."
msgstr "Få tillgång till en handplockad samling av tidpunkter med historiskt relevanta fotografier."

#: patterns/cta-newsletter.php:19
msgid "Sign up to get daily stories"
msgstr "Prenumerera på berättelser varje dag"

#: patterns/cta-newsletter.php
msgctxt "Pattern title"
msgid "Newsletter sign-up"
msgstr "Anmälan till nyhetsbrev"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search input field placeholder text."
msgid "Type here..."
msgstr "Skriv här …"

#: patterns/cta-heading-search.php:21 patterns/hidden-search.php:14
msgctxt "Search form label."
msgid "Search"
msgstr "Sök"

#: patterns/cta-heading-search.php:18
msgid "What are you looking for?"
msgstr "Vad letar du efter?"

#: patterns/cta-heading-search.php
msgctxt "Pattern description"
msgid "Large heading with a search form for quick navigation."
msgstr "Stor rubrik med sökformulär för snabb navigering."

#: patterns/cta-heading-search.php
msgctxt "Pattern title"
msgid "Heading and search form"
msgstr "Rubrik och sökformulär"

#: patterns/cta-grid-products-link.php:134
msgid "Shop now"
msgstr "Köp nu"

#: patterns/cta-grid-products-link.php:114
msgid "Botany flowers"
msgstr "Botanistens blommor"

#: patterns/cta-grid-products-link.php:100
msgid "Cancel anytime"
msgstr "Avbryt när som helst"

#: patterns/cta-grid-products-link.php:84
msgid "Free shipping"
msgstr "Fri frakt"

#: patterns/cta-grid-products-link.php:76
msgid "Tailored to your needs"
msgstr "Anpassat till dina behov"

#: patterns/cta-grid-products-link.php:70
msgid "Flora of Akaka Falls State Park"
msgstr "Floran i Akaka Falls State Park"

#: patterns/cta-grid-products-link.php:59
msgid "30€"
msgstr "30 €"

#. translators: %s: Starting price, split into three rows using HTML <br> tags.
#. The price value has a font size set.
#: patterns/cta-grid-products-link.php:58
msgid "Starting at%s/month"
msgstr "Från %s/månad"

#: patterns/cta-grid-products-link.php:38
msgid "Closeup of plantlife in the Malibu Canyon area"
msgstr "Närbild på växtligheten i Malibu Canyon-området"

#: patterns/cta-grid-products-link.php:32
msgid "Delivered every week"
msgstr "Leverans varje vecka"

#: patterns/cta-grid-products-link.php:26
#: patterns/cta-grid-products-link.php:126
msgid "Black and white flower"
msgstr "Blomma i svartvitt"

#: patterns/cta-grid-products-link.php:20
msgid "Our online store."
msgstr "Vår nätbutik."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern description"
msgid "A call to action featuring product images."
msgstr "CTA med produktbilder."

#: patterns/cta-grid-products-link.php
msgctxt "Pattern title"
msgid "Call to action with grid layout with products and link"
msgstr "CTA med produkter och länkar i rutnätslayout"

#: patterns/cta-events-list.php:106 patterns/cta-events-list.php:144
msgid "Thornville, OH, USA"
msgstr "Thornville, OH, USA"

#: patterns/cta-events-list.php:75
msgid "Mexico City, Mexico"
msgstr "Mexico City, Mexiko"

#: patterns/cta-events-list.php:51 patterns/cta-events-list.php:89
#: patterns/cta-events-list.php:120 patterns/cta-events-list.php:158
msgid "Buy Tickets"
msgstr "Köp biljetter"

#: patterns/cta-events-list.php:45 patterns/cta-events-list.php:83
#: patterns/cta-events-list.php:114 patterns/cta-events-list.php:152
#: patterns/event-3-col.php:44 patterns/event-3-col.php:68
#: patterns/event-3-col.php:92 patterns/event-rsvp.php:37
#: patterns/event-schedule.php:35 patterns/event-schedule.php:121
msgctxt "Example event date in pattern."
msgid "Mon, Jan 1"
msgstr "1 jan, måndag"

#: patterns/cta-events-list.php:37
msgid "Atlanta, GA, USA"
msgstr "Atlanta, GA, USA"

#: patterns/cta-events-list.php:23
msgid "These are some of the upcoming events"
msgstr "Här är några av våra kommande evenemang"

#: patterns/cta-events-list.php:19
msgid "Upcoming events"
msgstr "Kommande evenemang"

#: patterns/cta-events-list.php
msgctxt "Pattern description"
msgid "A list of events with call to action."
msgstr "En lista över evenemang med CTA."

#: patterns/cta-events-list.php
msgctxt "Pattern title"
msgid "Events list"
msgstr "Evenemangslista"

#: patterns/banner-intro-image.php:42 patterns/cta-centered-heading.php:28
#: patterns/hero-full-width-image.php:33
msgid "Learn more"
msgstr "Läs mer"

#: patterns/cta-centered-heading.php:22
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "På samma sätt som blommor kan slå ut på oväntade platser har varje berättelse en skönhet och styrka som kan avslöja dolda underverk."

#: patterns/cta-centered-heading.php:19 patterns/cta-events-list.php:33
#: patterns/cta-events-list.php:102 patterns/event-3-col.php:40
#: patterns/event-3-col.php:64 patterns/event-3-col.php:88
#: patterns/template-home-photo-blog.php:27
msgid "Tell your story"
msgstr "Berätta din historia"

#: patterns/cta-centered-heading.php
msgctxt "Pattern description"
msgid "A hero with a centered heading, paragraph and button."
msgstr "Hero-sektion med centrerad rubrik, textstycke och knapp."

#: patterns/cta-centered-heading.php
msgctxt "Pattern title"
msgid "Centered heading"
msgstr "Centrerad rubrik"

#: patterns/cta-book-locations.php:131
msgid "United Kingdom"
msgstr "Storbritannien"

#: patterns/cta-book-locations.php:119
msgid "United States"
msgstr "USA"

#: patterns/cta-book-locations.php:107
msgid "Switzerland"
msgstr "Schweiz"

#: patterns/cta-book-locations.php:95
msgid "New Zealand"
msgstr "Nya Zeeland"

#: patterns/cta-book-locations.php:79
msgid "Japan"
msgstr "Japan"

#: patterns/cta-book-locations.php:67
msgid "Canada"
msgstr "Kanada"

#: patterns/cta-book-locations.php:55
msgid "Brazil"
msgstr "Brasilien"

#: patterns/cta-book-locations.php:47 patterns/cta-book-locations.php:59
#: patterns/cta-book-locations.php:71 patterns/cta-book-locations.php:83
#: patterns/cta-book-locations.php:99 patterns/cta-book-locations.php:111
#: patterns/cta-book-locations.php:123 patterns/cta-book-locations.php:135
msgid "Book Store"
msgstr "Bokhandel"

#: patterns/cta-book-locations.php:43
msgid "Australia"
msgstr "Australien"

#: patterns/cta-book-locations.php:27
msgid "The Stories Book will be available from these international retailers."
msgstr "The Stories Book kommer att erbjudas av dessa internationella återförsäljare."

#: patterns/cta-book-locations.php:23
msgid "International editions"
msgstr "Internationella utgåvor"

#: patterns/cta-book-locations.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in the most popular locations."
msgstr "CTA-sektion med länkar för att skaffa boken på de populäraste platserna."

#: patterns/cta-book-locations.php
msgctxt "Pattern title"
msgid "Call to action with locations"
msgstr "CTA med platser"

#: patterns/cta-book-links.php:57
#: patterns/hero-overlapped-book-cover-with-links.php:100
msgctxt "Pattern placeholder text with link."
msgid "Outside Europe? View <a href=\"#\" rel=\"nofollow\">international editions</a>."
msgstr "Utanför Europa? Kolla in våra <a href=\"#\" rel=\"nofollow\">internationella utgåvor</a>."

#: patterns/cta-book-links.php:51
msgctxt "Example brand name."
msgid "Simon &amp; Schuster"
msgstr "Stora Varuhuset"

#: patterns/cta-book-links.php:47
msgctxt "Example brand name."
msgid "BAM!"
msgstr "Lilla Varuhuset"

#: patterns/cta-book-links.php:43
msgctxt "Example brand name."
msgid "Spotify"
msgstr "Spotify"

#: patterns/cta-book-links.php:39
msgctxt "Example brand name."
msgid "Bookshop.org"
msgstr "Nätbokhandeln"

#: patterns/cta-book-links.php:35
#: patterns/hero-overlapped-book-cover-with-links.php:62
msgctxt "Example brand name."
msgid "Apple Books"
msgstr "Apple Books"

#: patterns/cta-book-links.php:31
#: patterns/hero-overlapped-book-cover-with-links.php:84
msgctxt "Example brand name."
msgid "Barnes &amp; Noble"
msgstr "Boklådan"

#: patterns/cta-book-links.php:27
#: patterns/hero-overlapped-book-cover-with-links.php:77
msgctxt "Example brand name."
msgid "Audible"
msgstr "Ljudboksspecialisten"

#: patterns/cta-book-links.php:23
#: patterns/hero-overlapped-book-cover-with-links.php:55
msgctxt "Example brand name."
msgid "Amazon"
msgstr "Amazon"

#: patterns/cta-book-links.php:17
msgid "Buy your copy of The Stories Book"
msgstr "Köp ditt eget exemplar av The Stories Book"

#: patterns/cta-book-links.php
msgctxt "Pattern description"
msgid "A call to action section with links to get the book in different websites."
msgstr "CTA-sektion med länkar för att köpa boken på olika webbplatser."

#: patterns/cta-book-links.php
msgctxt "Pattern title"
msgid "Call to action with book links"
msgstr "CTA med länkar till bok"

#: patterns/contact-location-and-link.php:36
msgid "The business location"
msgstr "Verksamhetens plats"

#: patterns/contact-location-and-link.php:26
msgid "Get directions"
msgstr "Få vägbeskrivning"

#: patterns/contact-location-and-link.php:22
msgid "Visit us at 123 Example St. Manhattan, NY 10300, United States"
msgstr "Besök oss på Huvudgatan 1, Stockholm"

#: patterns/contact-location-and-link.php
msgctxt "Pattern description"
msgid "Contact section with a location address, a directions link, and an image of the location."
msgstr "Kontaktavsnitt med besöksadress, länk för vägbeskrivning och en bild från platsen."

#: patterns/contact-location-and-link.php
msgctxt "Pattern title"
msgid "Contact location and link"
msgstr "Besöksadresser och länk"

#: patterns/contact-info-locations.php:86
msgid "Portland"
msgstr "Karlstad"

#: patterns/contact-info-locations.php:74
msgid "Salt Lake City"
msgstr "Göteborg"

#: patterns/contact-info-locations.php:62
msgid "San Diego"
msgstr "Malmö"

#: patterns/contact-info-locations.php:54
#: patterns/contact-info-locations.php:66
#: patterns/contact-info-locations.php:78
#: patterns/contact-info-locations.php:90
msgid "123 Example St. Manhattan, NY 10300 United States"
msgstr "Huvudgatan 1, 111 31 Stockholm"

#: patterns/contact-info-locations.php:51
msgid "New York"
msgstr "Stockholm"

#: patterns/contact-info-locations.php:41
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/contact-info-locations.php:38
msgid "Email"
msgstr "E-post"

#: patterns/contact-info-locations.php:35
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:44
#: patterns/page-link-in-bio-with-tight-margins.php:56
msgid "TikTok"
msgstr "TikTok"

#: patterns/contact-info-locations.php:34 patterns/footer-social.php:21
msgid "Facebook"
msgstr "Facebook"

#: patterns/contact-info-locations.php:33 patterns/footer-social.php:22
#: patterns/media-instagram-grid.php:24 patterns/page-cv-bio.php:47
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:36
#: patterns/page-link-in-bio-with-tight-margins.php:48
msgid "Instagram"
msgstr "Instagram"

#: patterns/contact-info-locations.php:32 patterns/footer-social.php:23
#: patterns/page-link-in-bio-heading-paragraph-links-image.php:40
#: patterns/page-link-in-bio-with-tight-margins.php:52
msgctxt "Refers to the social media platform formerly known as Twitter."
msgid "X"
msgstr "X"

#: patterns/contact-info-locations.php:29
#: patterns/contact-info-locations.php:31 patterns/footer-social.php:20
msgid "Social media"
msgstr "Sociala medier"

#: patterns/contact-info-locations.php:21
msgid "How to get in touch with us"
msgstr "Så kontaktar du oss"

#: patterns/contact-info-locations.php
msgctxt "Pattern description"
msgid "Contact section with social media links, email, and multiple location details."
msgstr "Kontaktavsnitt med länkar till sociala medier, e-postadress och information om flera platser."

#: patterns/contact-info-locations.php
msgctxt "Pattern title"
msgid "Contact, info and locations"
msgstr "Kontaktuppgifter, information och platser"

#: patterns/contact-centered-social-link.php:21
msgctxt "Heading of the Contact social link pattern"
msgid "Got questions? <br><a href=\"#\" rel=\"nofollow\">Feel free to reach out.</a>"
msgstr "Undrar du över något? <br><a href=\"#\" rel=\"nofollow\">Kontakta oss gärna.</a>"

#: patterns/contact-centered-social-link.php
msgctxt "Pattern description"
msgid "Centered contact section with a prominent message and social media links."
msgstr "Centrerad kontaktsektion med ett framträdande budskap och länkar till sociala medier."

#: patterns/contact-centered-social-link.php
msgctxt "Pattern title"
msgid "Centered link and social links"
msgstr "Centrerad länk och sociala länkar"

#: patterns/comments.php:18
msgid "Comments"
msgstr "Kommentarer"

#: patterns/comments.php
msgctxt "Pattern description"
msgid "Comments area with comments list, pagination, and comment form."
msgstr "Kommentarsområde med kommentarslista, siduppdelning och kommentarsformulär."

#: patterns/comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Kommentarer"

#: patterns/binding-format.php
msgctxt "Pattern description"
msgid "Prints the name of the post format with the help of the Block Bindings API."
msgstr "Skriver ut namnet på inläggsformatet med hjälp av Block Bindings API."

#: patterns/binding-format.php
msgctxt "Pattern title"
msgid "Post format name"
msgstr "Namn på inläggsformat"

#: patterns/banner-with-description-and-images-grid.php:48
#: patterns/overlapped-images.php:26
msgid "Black and white photography close up of a flower."
msgstr "Svartvitt fotografi närbild av blomma."

#: patterns/banner-with-description-and-images-grid.php:42
#: patterns/overlapped-images.php:21
msgid "Photography close up of a red flower."
msgstr "Fotografi närbild av röd blomma."

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-with-description-and-images-grid.php:31
#: patterns/overlapped-images.php:47
msgid "%s is a flower delivery and subscription business. Based in the EU, our mission is not only to deliver stunning flower arrangements across but also foster knowledge and enthusiasm on the beautiful gift of nature: flowers."
msgstr "%s är ett företag som levererar blommor och blomprenumerationer. Vi finns i EU och vårt uppdrag är inte bara att leverera fantastiska blomsterarrangemang överallt utan också att främja kunskap och entusiasm om naturens vackra gåva: blommor."

#: patterns/banner-with-description-and-images-grid.php:23
#: patterns/overlapped-images.php:37
msgid "About Us"
msgstr "Om oss"

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern description"
msgid "A banner with a short paragraph, and two images displayed in a grid layout."
msgstr "En banner med ett kort textstycke och två bilder i en rutnätslayout."

#: patterns/banner-with-description-and-images-grid.php
msgctxt "Pattern title"
msgid "Banner with description and images grid"
msgstr "Banner med beskrivning och rutnät med bilder"

#: patterns/banner-poster.php:59
msgid "#stories"
msgstr "#historier"

#: patterns/banner-poster.php:51
msgid "Let’s hear them."
msgstr "Låt oss lyssna på dem."

#: patterns/banner-poster.php:39
msgid "Fuego Bar, Mexico City"
msgstr "Fuego Bar, Mexico City"

#: patterns/banner-poster.php:39
msgctxt "Example event date in pattern."
msgid "Aug 08—10 2025"
msgstr "8–10 aug 2025"

#. translators: This string contains the word "Stories" in four different
#. languages with the first item in the locale's language.
#: patterns/banner-poster.php:28 patterns/cta-events-list.php:68
#: patterns/cta-events-list.php:137 patterns/event-rsvp.php:30
msgctxt "Placeholder heading in four languages."
msgid "“Stories, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"
msgstr "”Berättelser, <span lang=\"es\">historias</span>, <span lang=\"uk\">iсторії</span>, <span lang=\"el\">iστορίες</span>”"

#: patterns/banner-poster.php:15
msgid "Picture of a historical building in ruins."
msgstr "Bild med ruin av en historisk byggnad."

#: patterns/banner-poster.php
msgctxt "Pattern description"
msgid "A section that can be used as a banner or a landing page to announce an event."
msgstr "En sektion som kan användas som banner eller landningssida för att berätta om ett evenemang."

#: patterns/banner-poster.php
msgctxt "Pattern title"
msgid "Poster-like section"
msgstr "Affischliknande sektion"

#: patterns/banner-intro.php:22
#: patterns/banner-with-description-and-images-grid.php:32
#: patterns/footer-columns.php:46 patterns/overlapped-images.php:48
msgctxt "Example brand name."
msgid "Fleurs"
msgstr "Blomman"

#. translators: %s is the brand name, e.g., 'Fleurs'.
#: patterns/banner-intro.php:21
msgctxt "Pattern placeholder text."
msgid "We're %s, our mission is to deliver exquisite flower arrangements that not only adorn living spaces but also inspire a deeper appreciation for natural beauty."
msgstr "Vi är %s. Vårt uppdrag är att leverera utsökta blomsterarrangemang som inte bara smyckar levande utrymmen utan även inspirerar till en djupare uppskattning av naturlig skönhet."

#: patterns/banner-intro.php
msgctxt "Pattern description"
msgid "A large left-aligned heading with a brand name emphasized in bold."
msgstr "En stor, vänsterjusterad rubrik med ett varumärke i fetstil."

#: patterns/banner-intro.php
msgctxt "Pattern title"
msgid "Intro with left-aligned description"
msgstr "Introduktion med vänsterjusterad beskrivning"

#: patterns/banner-intro-image.php:35
msgctxt "Sample description for banner with flower."
msgid "Like flowers that bloom in unexpected places, every story unfolds with beauty and resilience, revealing hidden wonders."
msgstr "På samma sätt som blommor kan slå ut på oväntade platser har varje berättelse en skönhet och styrka som kan avslöja dolda underverk."

#: patterns/banner-intro-image.php:31
msgctxt "Heading for banner pattern."
msgid "New arrivals"
msgstr "Nyinkommet"

#: patterns/banner-intro-image.php:22
msgctxt "Alt text for intro picture."
msgid "Picture of a flower"
msgstr "Bild av en blomma"

#: patterns/banner-intro-image.php
msgctxt "Pattern description"
msgid "A Intro pattern with Short heading, paragraph and image on the left."
msgstr "Ett introduktionsmönster med kort rubrik, textstycke och bild till vänster."

#: patterns/banner-intro-image.php
msgctxt "Pattern title"
msgid "Short heading and paragraph and image on the left"
msgstr "Kort rubrik, textstycke samt bild till vänster"

#: patterns/banner-cover-big-heading.php:27 patterns/footer-columns.php:33
#: patterns/footer-columns.php:35 patterns/footer-newsletter.php:20
#: patterns/template-home-photo-blog.php:22
msgid "Stories"
msgstr "Berättelser"

#: patterns/banner-cover-big-heading.php:20
#: patterns/media-instagram-grid.php:36 patterns/page-coming-soon.php:19
msgid "Photo of a field full of flowers, a blue sky and a tree."
msgstr "Foto av ett fält fullt av blommor, en blå himmel och ett träd."

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern description"
msgid "A full-width cover section with a large background image and an oversized heading."
msgstr "En fullbred omslagssektion med stor bakgrundsbild och en överdimensionerad rubrik."

#: patterns/banner-cover-big-heading.php
msgctxt "Pattern title"
msgid "Cover with big heading"
msgstr "Omslag med stor rubrik"

#: patterns/banner-about-book.php:34
msgid "Image of a book"
msgstr "Bild av en bok"

#: patterns/banner-about-book.php:26
msgctxt "Pattern placeholder text."
msgid "This exquisite compilation showcases a diverse array of photographs that capture the essence of different eras and cultures, reflecting the unique styles and perspectives of each artist. Fleckenstein’s evocative imagery, Strand’s groundbreaking modernist approach, and Kōno’s meticulous documentation of Japanese life come together in a harmonious blend that celebrates the art of photography. Each image in “The Stories Book” is accompanied by insightful commentary, providing historical context and revealing the stories behind the photographs. This collection is not only a visual feast but also a tribute to the power of photography to preserve and narrate the multifaceted experiences of humanity."
msgstr "Denna utsökta samling demonstrerar en mängd olika fotografier som fångar det centrala för olika tidsepoker och kulturer, samt återspeglar varje konstnärs unika stil och perspektiv. Fleckensteins suggestiva bildspråk, Strands banbrytande modernistiska tillvägagångssätt och Kōnos omsorgsfulla dokumentation av det japanska livet möts i en harmonisk blandning som hyllar fotokonsten. Varje bild i ”The Stories Book” åtföljs av insiktsfulla kommentarer som ger ett historiskt sammanhang och berättar historierna bakom varje fotografi. Den här samlingen är inte bara en visuell fest utan också en hyllning till fotografins förmåga att bevara och berätta om mänsklighetens mångfacetterade upplevelser."

#: patterns/banner-about-book.php:22
msgid "About the book"
msgstr "Om boken"

#: patterns/banner-about-book.php
msgctxt "Pattern description"
msgid "Banner with book description and accompanying image for promotion."
msgstr "Banner med beskrivning av bok och kompletterande bild för marknadsföring."

#: patterns/banner-about-book.php
msgctxt "Pattern title"
msgid "Banner with book description"
msgstr "Banner med beskrivning av bok"

#: functions.php:134
msgctxt "Label for the block binding placeholder in the editor"
msgid "Post format name"
msgstr "Namn på inläggsformat"

#: functions.php:114
msgid "A collection of post format patterns."
msgstr "En samling mönster för olika inläggsformat."

#: functions.php:113
msgid "Post formats"
msgstr "Inläggsformat"

#: functions.php:106
msgid "A collection of full page layouts."
msgstr "En samling helsideslayouter."

#: functions.php:105
msgid "Pages"
msgstr "Sidor"

#: functions.php:76
msgid "Checkmark"
msgstr "Avbockningstecken"

#. Author URI of the theme
#: style.css patterns/footer-centered.php:34 patterns/footer-columns.php:74
#: patterns/footer-newsletter.php:50 patterns/footer-social.php:36
#: patterns/footer.php:83
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://sv.wordpress.org/"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress-teamet"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfive/"
msgstr "https://sv.wordpress.org/themes/twentytwentyfive/"