# Translation of Plugins - Health Check &amp; Troubleshooting - Stable (latest release) in Swedish
# This file is distributed under the same license as the Plugins - Health Check &amp; Troubleshooting - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-26 20:41:26+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: sv_SE\n"
"Project-Id-Version: Plugins - Health Check &amp; Troubleshooting - Stable (latest release)\n"

#. translators: %s: The date and time the screenshot was taken.
#: pages/screenshots.php:114
msgid "Published on %s"
msgstr "Publicerat den %s"

#: pages/screenshots.php:100
msgid "Delete screenshot"
msgstr "Ta bort skärmdump"

#: pages/screenshots.php:80
msgid "Copy forum markup"
msgstr "Kopiera märkkod för forum"

#: pages/screenshots.php:64
msgid "View screenshot"
msgstr "Visa skärmdump"

#: pages/screenshots.php:49
msgid "You have not taken any screenshots, return here when you have to view them."
msgstr "Du har inte tagit några skärmdumpar, återkom hit när du vill se dem."

#: pages/screenshots.php:33
msgctxt "Description of the button label"
msgid "Take Screenshot"
msgstr "Ta en skärmdump"

#. translators: %s: The label of the "Take screenshot" button.
#: pages/screenshots.php:30
msgid "To take a screenshot for sharing with support users, you may click the %s button, found at the top of every page, and they will be stored securely on your site until you wish to share them."
msgstr "För att skapa en skärmdump som du kan visa till andra användare på supporten kan du klicka på knappen %s som visas längst upp på varje sida. Skärmdumparna kommer att sparas på ett säkert sätt på din webbplats tills du väljer att dela dem."

#: pages/screenshots.php:22
msgid "This is a beta-feature, and some inconsistencies in screenshots is to be expected, please keep that, and the fact that your pages may show sensitive information in mind when sharing screenshots."
msgstr "Detta är en beta-funktion och vissa inkonsekvenser i skärmdumpar kan finnas, ha i åtanke att dina sidor kan visa känslig information när du delar skärmdumpar."

#: mu-plugin/health-check-troubleshooting-mode.php:1304
msgid "If you wish to troubleshoot as another user, or as an anonymous site visitor, the <a href=\"https://wordpress.org/plugins/user-switching/\">User Switcher plugin</a> allows for all of this while also being compatible with Troubleshooting Mode."
msgstr "Om du vill felsöka som en annan användare, eller som en anonym besökare, gör tillägget <a href=\"https://wordpress.org/plugins/user-switching/\">User Switcher</a> allt detta samtidigt som det också är kompatibelt med felsökningsläget."

#: HealthCheck/Tools/class-health-check-robotstxt.php:37
msgid "Your site is using the virtual <code>robots.txt</code> file which is generated by WordPress."
msgstr "Din webbplats använder den virtuella <code>robots.txt</code>-filen som genereras av WordPress."

#: HealthCheck/Tools/class-health-check-robotstxt.php:21
msgid "The <code>robots.txt</code> file tells search engines which directories are allowed to be crawled and which not. WordPress generates a virtual file if there is no physical file. If there is a non-virtual file, the content will be displayed here."
msgstr "Filen <code>robots.txt</code> talar om för sökmotorer vilka kataloger som får genomsökas och vilka som inte får det. WordPress genererar en virtuell fil om det inte finns någon fysisk fil. Om det finns en icke-virtuell fil visas dess innehåll här."

#: HealthCheck/Tools/class-health-check-robotstxt.php:20
msgid "robots.txt Viewer"
msgstr "robots.txt-visare"

#: HealthCheck/Tools/class-health-check-mail-check.php:95
msgid "The e-mail took a while to send; this may indicate that your server is really busy, or that the sending of emails may be experiencing other unexpected issues. If you experience continued issues, consider reaching out to your hosting provider."
msgstr "Det tog en stund att skicka e-postmeddelandet. Detta kan tyda på att din server är överbelastad eller att det finns andra oväntade problem med att skicka e-postmeddelanden. Om problemen fortsätter kan du kontakta ditt webbhotell."

#: HealthCheck/Tools/class-health-check-files-integrity.php:135
#: HealthCheck/Tools/class-health-check-files-integrity.php:147
msgid "This is an unknown file"
msgstr "Detta är en okänd fil"

#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:61
msgid "Debug log contents"
msgstr "Innehåll i felsökningsloggen"

#. translators: The localized URL to the Debugging in WordPress article, if
#. available.
#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:56
msgid "https://wordpress.org/documentation/article/debugging-in-wordpress/#wp_debug_log"
msgstr "https://wordpress.org/documentation/article/debugging-in-wordpress/#wp_debug_log"

#. translators: %s: The URL to the Debugging in WordPress article.
#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:54
msgid "You can read more about the <code>WP_DEBUG_LOG</code> constant, and how to enable it, in the <a href=\"%s\">Debugging in WordPress</a> article."
msgstr "Du kan läsa mer om <code>WP_DEBUG_LOG</code>-konstanten, och hur du aktiverar den, i artikeln <a href=\"%s\">Felsökning i WordPress</a>."

#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:48
msgid "Because the <code>WP_DEBUG_LOG</code> constant is not set to allow logging of errors and warnings, and there is therefore no more details here."
msgstr "Eftersom konstanten <code>WP_DEBUG_LOG</code> inte är satt, vilket skulle tillåta loggning av fel och varningar finns det inte några fler uppgifter här."

#. translators: %s: The path to the debug log file.
#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:36
msgid "The debug log file found at `%s`, could not be read."
msgstr "Felsökningsloggfilen som finns i `%s` kunde inte läsas."

#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:7
msgid "The details below are gathered from your <code>debug.log</code> file, and is displayed because the <code>WP_DEBUG_LOG</code> constant has been set to allow logging of warnings and errors."
msgstr "Uppgifterna nedan har hämtats från filen <code>debug.log</code> och visas eftersom konstanten <code>WP_DEBUG_LOG</code> har ställts in för att tillåta loggning av varningar och fel."

#: HealthCheck/Tools/class-health-check-debug-log-viewer.php:6
msgid "Debug logs"
msgstr "Felsökningsloggar"

#: HealthCheck/Tools/class-health-check-beta-features.php:63
msgid "Disable beta features"
msgstr "Inaktivera betafunktioner"

#: HealthCheck/Tools/class-health-check-beta-features.php:57
msgid "Enable beta features"
msgstr "Aktivera betafunktioner"

#: HealthCheck/Tools/class-health-check-beta-features.php:21
msgid "The plugin may contain beta features, which you as the site owner can enable or disable as you wish."
msgstr "Tillägget kan innehålla betafunktioner, som du som webbplatsägare kan aktivera eller inaktivera som du vill."

#: HealthCheck/Tools/class-health-check-beta-features.php:20
msgid "Beta features"
msgstr "Betafunktioner"

#: HealthCheck/class-health-check-screenshots.php:248
msgid "Take screenshot"
msgstr "Ta en skärmdump"

#: HealthCheck/class-health-check-screenshots.php:147
msgid "Screenshot"
msgstr "Skärmdump"

#: HealthCheck/class-health-check-screenshots.php:132
#: HealthCheck/class-health-check-screenshots.php:146 pages/screenshots.php:17
msgid "Screenshots"
msgstr "Skärmdumpar"

#: mu-plugin/health-check-troubleshooting-mode.php:1619
msgid "Cancel"
msgstr "Avbryt"

#: mu-plugin/health-check-troubleshooting-mode.php:1618
msgid "Confirm"
msgstr "Bekräfta"

#: mu-plugin/health-check-troubleshooting-mode.php:1611
msgid "You were attempting to perform an action that requires a security token, which was either not present in your request, or was considered invalid. Please verify that the following action is intentional, or feel free to cancel the action and nothing will change."
msgstr "Du försökte utföra en åtgärd som kräver en engångs säkerhetskod (security token) där denna kod antingen saknades i din begäran, eller betraktades som ogiltig. Bekräfta att följande åtgärd är avsiktlig. Du kan även avbryta, i så fall ändras ingenting."

#: mu-plugin/health-check-troubleshooting-mode.php:1607
msgid "Troubleshooting Mode - Security check"
msgstr "Felsökningsläge – säkerhetskontroll"

#. translators: The theme being activated.
#: mu-plugin/health-check-troubleshooting-mode.php:953
msgid "You were attempting to <strong>change the active theme</strong> to %s while troubleshooting."
msgstr "Du försökte att <strong>byta ditt aktiva tema</strong> till %s under pågående felsökning."

#. translators: The plugin being affected.
#: mu-plugin/health-check-troubleshooting-mode.php:868
msgid "You were attempting to <strong>disable</strong> the %s plugin while troubleshooting."
msgstr "Du försökte att <strong>inaktivera</strong> tillägget %s under pågående felsökning."

#. translators: The plugin being affected.
#: mu-plugin/health-check-troubleshooting-mode.php:782
msgid "You were attempting to <strong>enable</strong> the %s plugin while troubleshooting."
msgstr "Du försökte att <strong>aktivera</strong> tillägget %s under pågående felsökning."

#: mu-plugin/health-check-troubleshooting-mode.php:753
msgid "You were attempting to <strong>dismiss all notices</strong>."
msgstr "Du försökte att <strong>avfärda alla aviseringar</strong>."

#: mu-plugin/health-check-troubleshooting-mode.php:726
msgid "You were attempting to <strong>disable Troubleshooting Mode</strong>."
msgstr "Du försökte att <strong>inaktivera felsökningsläget</strong>."

#: mu-plugin/health-check-troubleshooting-mode.php:196
msgid "Install the latest classic default theme"
msgstr "Installera det senaste klassiska standardtemat (inte blockbaserat)"

#: mu-plugin/health-check-troubleshooting-mode.php:187
msgid "Install the latest default theme"
msgstr "Installera det senaste standardtemat"

#: HealthCheck/Tools/class-health-check-htaccess.php:44
msgid "Your site is not using <code>.htaccess</code> to handle permalinks. This means that your .htaccess file is not being used to handle requests, and they are most likely handled directly by your web-server software."
msgstr "Din webbplats använder inte <code>.htaccess</code> för hantering av permalänkar. Det innebär att din .htaccess-fil inte används för hanteringen inkommande begäranden utan detta troligtvis sköts direkt av webbserver-programvaran."

#: HealthCheck/Tools/class-health-check-htaccess.php:38
msgid "Your site is using <code>.htaccess</code> rules to handle permalinks, but no .htaccess file was found. This means that your .htaccess file is not being used to handle requests."
msgstr "Din webbplats använder regler i filen <code>.htaccess</code> för hanteringen av permalänkar, men ingen .htaccess-fil hittades. Det innebär att filen .htaccess inte används för hanteringen av begäranden."

#: HealthCheck/Tools/class-health-check-htaccess.php:21
msgid "The <code>.htaccess</code> file tells your server (if supported) how to handle links and file requests. This file usually requires direct server access to view, but if your system supports these files, you can verify its content here."
msgstr "Filen <code>.htaccess</code> (om den stöds av systemet) instruerar din webbserver hur den ska hantera länkar och begäranden om filer. För att kunna se detta behöver man normalt direktåkomst till servern, men om ditt system har stöd för dessa filer kan du granska filens innehåll här."

#: HealthCheck/Tools/class-health-check-htaccess.php:20
msgid ".htaccess Viewer"
msgstr ".htaccess-granskare"

#: pages/site-status.php:42
msgid "The plugin itself still offers you the ability to troubleshoot issues with your installation, and various tools associated with this functionality."
msgstr "Själva tillägget erbjuder fortfarande möjligheten att felsöka problem i din WordPress-installation samt olika relaterade verktyg."

#: pages/site-status.php:36
msgid "Due to this, some functionality has been removed from the plugin, you can find these features in a more recent version of WordPress it self."
msgstr "Därför har vissa funktioner tagits bort ur tillägget. I nyare versioner av WordPress finns funktionerna i själva WordPress."

#. translators: %s: The current WordPress version used on this site.
#: pages/site-status.php:27
msgid "You are running an older version of WordPress, version %s. The Site Health features from this plugin were added to version 5.2 of WordPress."
msgstr "Du använder en äldre version av WordPress (%s). Funktionerna för hälsokontroll i detta tillägg finns i WordPress-kärnan i version 5.2."

#: pages/site-status.php:20
msgid "WordPress update needed!"
msgstr "Uppdatering av WordPress krävs!"

#: mu-plugin/health-check-troubleshooting-mode.php:1300
msgid "The Health Check plugin will attempt to disable cache solutions on your site, but if you are using a custom caching solution, you may need to disable it manually when troubleshooting."
msgstr "Tillägget Health Check kommer att försöka inaktivera cache-lösningarna på din webbplats. Om du använder någon specialanpassad cache-lösning kan du behöva inaktivera den manuellt för att underlätta felsökningen."

#: mu-plugin/health-check-troubleshooting-mode.php:1005
msgid "Switch anyway"
msgstr "Byt ändå"

#. translators: %s: Plugin name.
#: mu-plugin/health-check-troubleshooting-mode.php:1001
msgid "Force-switch to the %s theme, even though the loopback checks failed."
msgstr "Tvinga byte till temat ”%s” trots att testet av återanrop misslyckades."

#: mu-plugin/health-check-troubleshooting-mode.php:922
msgid "Disable anyway"
msgstr "Inaktivera ändå"

#. translators: %s: Plugin name.
#: mu-plugin/health-check-troubleshooting-mode.php:918
msgid "Force-disable the plugin, %s, even though the loopback checks failed."
msgstr "Tvinga inaktivering av tillägget ”%s” trots att testet av återanrop misslyckades."

#: mu-plugin/health-check-troubleshooting-mode.php:837
msgid "Enable anyway"
msgstr "Aktivera ändå"

#. translators: %s: Plugin name.
#: mu-plugin/health-check-troubleshooting-mode.php:833
msgid "Force-enable the plugin, %s, even though the loopback checks failed."
msgstr "Tvinga aktivering av tillägget ”%s” trots att testet av återanrop misslyckades."

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:73
msgid "The slug, or version, is missing from the request."
msgstr "Slug eller version saknas i begäran."

#: HealthCheck/Tools/class-health-check-phpinfo.php:19
msgid "PHP Info"
msgstr "PHP-info"

#: HealthCheck/BackCompat/class-wp-debug-data.php:815
msgid "Database collation"
msgstr "Databaskollationering"

#: HealthCheck/BackCompat/class-wp-debug-data.php:809
msgid "Database charset"
msgstr "Databasens teckenuppsättning"

#: HealthCheck/BackCompat/class-wp-debug-data.php:803
msgid "Table prefix"
msgstr "Tabellprefix"

#: HealthCheck/BackCompat/class-wp-debug-data.php:785
msgid "Database username"
msgstr "Användarnamn för databas"

#: HealthCheck/BackCompat/class-wp-debug-data.php:715
msgid "Are pretty permalinks supported?"
msgstr "Stöds snygga permalänkar?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:519
msgid "Max number of files allowed"
msgstr "Maximalt antal tillåtna filer"

#: HealthCheck/BackCompat/class-wp-debug-data.php:515
msgid "Max effective file size"
msgstr "Maximal faktisk filstorlek"

#: HealthCheck/BackCompat/class-wp-debug-data.php:511
msgid "Max size of an uploaded file"
msgstr "Maximal storlek för en uppladdad fil"

#: HealthCheck/BackCompat/class-wp-debug-data.php:507
msgid "Max size of post data allowed"
msgstr "Maximal tillåten storlek på postdata"

#: HealthCheck/BackCompat/class-wp-debug-data.php:502
msgid "File uploads"
msgstr "Filuppladdningar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:485
msgid "File upload settings"
msgstr "Inställningar för filuppladdning"

#. translators: %s: wp-content directory name.
#: HealthCheck/BackCompat/class-wp-debug-data.php:148
msgid "Drop-ins are single files, found in the %s directory, that replace or enhance WordPress features in ways that are not possible for traditional plugins."
msgstr "Insticksprogram är enstaka filer i katalogen %s, som ersätter eller förbättrar WordPress-funktioner på sätt som inte är möjliga för vanliga tillägg."

#: HealthCheck/BackCompat/class-wp-debug-data.php:129
msgid "Environment type"
msgstr "Typ av driftsmiljö"

#: HealthCheck/BackCompat/class-wp-debug-data.php:119
msgid "Is this site discouraging search engines?"
msgstr "Ber webbplatsen sökmotorer att inte indexera den?"

#. translators: %s: WP_ENVIRONMENT_TYPES
#: compat.php:56
msgid "The %s constant is no longer supported."
msgstr "Konstanten %s stöds inte längre."

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:40
msgid "Highest supported PHP"
msgstr "Högsta PHP-version som stöds"

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:39
msgid "Minimum PHP"
msgstr "Lägsta PHP-version"

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:37
msgid "Plugin"
msgstr "Tillägg"

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:101
msgid "Could not be determined"
msgstr "Gick inte att avgöra"

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:65
msgid "Check plugins"
msgstr "Kontrollera tilläggen"

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:10
msgid "The compatibility check will need to send requests to the <a href=\"https://wptide.org\">WPTide</a> project to fetch the test results for each of your plugins."
msgstr "Kompatibilitetstestet behöver även skicka förfrågningar till projektet <a href=\"https://wptide.org\">WPTide</a> för att hämta testresultat för vart och ett av dina tillägg."

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:9
msgid "Attempt to identify the compatibility of your plugins before upgrading PHP, note that a compatibility check may not always be accurate, and you may want to contact the plugin author to confirm that things will continue working."
msgstr "Försök att kontrollera kompatibiliteten hos dina tillägg innan du uppgraderar PHP. Tänk på att en kompatibilitetstest inte alltid ger korrekt resultat och att du kanske behöver kontakta tilläggets författare för att kolla om det kommer att fungera även i fortsättningen."

#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:6
msgid "Plugin compatibility"
msgstr "Kompatibilitet hos tillägg"

#: HealthCheck/BackCompat/class-wp-debug-data.php:165
msgid "Inactive Themes"
msgstr "Inaktiva teman"

#: HealthCheck/BackCompat/class-wp-debug-data.php:160
msgid "Parent Theme"
msgstr "Huvudtema"

#: HealthCheck/BackCompat/class-wp-debug-data.php:85
msgid "Timezone"
msgstr "Tidszon"

#. translators: %s: URL to Site Health Status page.
#: pages/debug-data.php:38
msgid "This page can show you every detail about the configuration of your WordPress website. For any improvements that could be made, see the <a href=\"%s\">Site Health Status</a> page."
msgstr "Sidan kan visa dig all information om hur din WordPress-webbplats är konfigurerad. Eventuella förbättringar som kan göras beskrivs på sidan <a href=\"%s\">Hälsostatus för webbplats</a>."

#. translators: %d: Amount of hidden themes.
#: mu-plugin/health-check-troubleshooting-mode.php:1480
msgid "Show %d remaining theme"
msgid_plural "Show %d remaining themes"
msgstr[0] "Visa %d återståendе tema"
msgstr[1] "Visa %d återstående teman"

#. translators: %d: Amount of hidden plugins.
#: mu-plugin/health-check-troubleshooting-mode.php:1394
msgid "Show %d remaining plugin"
msgid_plural "Show %d remaining plugins"
msgstr[0] "Visa %d återstående tillägg"
msgstr[1] "Visa %d återstående tillägg"

#: mu-plugin/health-check-troubleshooting-mode.php:315
msgid "When bulk-disabling plugins, a site failure occurred. Because of this the change was automatically reverted."
msgstr "Vid samtidig inaktivering av flera tillägg inträffade ett fel på webbplatsen. På grund av detta backades ändringen tillbaka automatiskt."

#: mu-plugin/health-check-troubleshooting-mode.php:290
msgid "When bulk-enabling plugins, a site failure occurred. Because of this the change was automatically reverted."
msgstr "Vid samtidig aktivering av flera tillägg inträffade ett fel på webbplatsen. På grund av detta backades ändringen tillbaka automatiskt."

#: mu-plugin/health-check-troubleshooting-mode.php:265
msgid "When enabling troubleshooting on the selected plugins, a site failure occurred. Because of this the selected plugins were kept disabled while troubleshooting mode started."
msgstr "Vid aktivering av felsökning för de valda tilläggen inträffade ett fel på webbplatsen. Av denna anledning fortsatta de valda tilläggen att vara inaktiverade när felsökningsläget startades."

#: pages/phpinfo.php:17
msgid "Extended PHP Information"
msgstr "Utökad PHP-information"

#: pages/debug-data.php:19 pages/site-health-header.php:56
msgid "The Site Health check requires JavaScript."
msgstr "Kontrollen av webbplatsens hälsa kräver JavaScript för att fungera."

#: pages/site-health-header.php:21
msgid "Secondary menu"
msgstr "Sekundärmeny"

#: pages/site-health-header.php:17
msgid "Site Health"
msgstr "Hälsokontroll för webbplatser"

#: HealthCheck/Tools/class-health-check-phpinfo.php:78
msgid "View extended PHP information"
msgstr "Visa utökad PHP-information"

#: pages/debug-data.php:50 pages/screenshots.php:81
msgid "Copied!"
msgstr "Kopierat!"

#: pages/debug-data.php:48
msgid "Copy site info to clipboard"
msgstr "Kopiera information om webbplatsen till urklipp"

#: pages/debug-data.php:42
msgid "If you want to export a handy list of all the information on this page, you can use the button below to copy it to the clipboard. You can then paste it in a text file and save it to your device, or paste it in an email exchange with a support engineer or theme/plugin developer for example."
msgstr "Om du vill exportera en bekväm lista med all information som finns på denna sida kan du använda knapparna nedan för att kopiera texten till urklipp. Sedan kan du klistra in det i en textfil och spara på hårddisken, eller klistra in det i ett e-postmeddelande till, exempelvis, någon supportperson eller utvecklare av något tema eller tillägg."

#: pages/debug-data.php:32
msgid "Site Health Info"
msgstr "Information om webbplatsens hälsa"

#: pages/troubleshoot.php:21
msgid "Troubleshooting mode"
msgstr "Felsökningsläge"

#: HealthCheck/BackCompat/class-wp-debug-data.php:978
#: HealthCheck/BackCompat/class-wp-debug-data.php:1024
msgid "Theme directory location"
msgstr "Plats för temats filkatalog"

#: HealthCheck/BackCompat/class-wp-debug-data.php:974
msgid "Theme features"
msgstr "Temafunktioner"

#: HealthCheck/BackCompat/class-wp-debug-data.php:940
msgid "None"
msgstr "Inget"

#: HealthCheck/BackCompat/class-wp-debug-data.php:697
msgid "Is SUHOSIN installed?"
msgstr "Är SUHOSIN installerat?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:628
msgid "PHP version"
msgstr "PHP-version"

#: HealthCheck/BackCompat/class-wp-debug-data.php:623
msgid "Web server"
msgstr "Webbserver"

#: HealthCheck/BackCompat/class-wp-debug-data.php:479
msgid "ImageMagick version string"
msgstr "Versionssträng för ImageMagick"

#: HealthCheck/BackCompat/class-wp-debug-data.php:474
msgid "ImageMagick version number"
msgstr "Versionsnummer för ImageMagick"

#: HealthCheck/BackCompat/class-wp-debug-data.php:350
msgid "Shows whether WordPress is able to write to the directories it needs access to."
msgstr "Visar om WordPress kan skriva till de kataloger där skrivåtkomst behövs."

#: HealthCheck/BackCompat/class-wp-debug-data.php:251
msgid "These settings alter where and how parts of WordPress are loaded."
msgstr "Dessa inställningar förändrar var och hur vissa delar av WordPress läses in."

#: HealthCheck/BackCompat/class-wp-debug-data.php:138
msgid "Directories and Sizes"
msgstr "Filkataloger och deras storlekar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:125
msgctxt "comment status"
msgid "Closed"
msgstr "Stängt"

#: HealthCheck/BackCompat/class-wp-debug-data.php:125
msgctxt "comment status"
msgid "Open"
msgstr "Öppet"

#: HealthCheck/BackCompat/class-wp-debug-data.php:100
msgid "No permalink structure set"
msgstr "Ingen permalänkstruktur är inställd"

#: HealthCheck/BackCompat/class-wp-debug-data.php:81
msgid "User Language"
msgstr "Användarspråk"

#: HealthCheck/BackCompat/class-wp-debug-data.php:77
msgid "Site Language"
msgstr "Webbplatsspråk"

#: compat.php:159
msgctxt "localized PHP upgrade information page"
msgid "https://wordpress.org/support/update-php/"
msgstr "https://sv.wordpress.org/support/update-php/"

#: HealthCheck/Tools/class-health-check-files-integrity.php:197
msgid "Error"
msgstr "Fel"

#: HealthCheck/BackCompat/class-wp-site-health.php:30
msgid "Once weekly"
msgstr "En gång i veckan"

#: HealthCheck/class-health-check.php:341
msgid "Info"
msgstr "Info"

#: HealthCheck/BackCompat/class-wp-site-health.php:112
msgctxt "Page title"
msgid "Site Health"
msgstr "Hälsokontroll"

#. translators: %s: Critical issue counter, if any.
#: HealthCheck/BackCompat/class-wp-site-health.php:106
msgctxt "Menu Title"
msgid "Site Health %s"
msgstr "Hälsostatus %s"

#: HealthCheck/BackCompat/class-wp-site-health.php:99
msgctxt "Issue counter label for the admin menu"
msgid "Critical issues"
msgstr "Kritiska problem"

#. translators: %d: The HTTP response code returned.
#: HealthCheck/class-health-check-loopback.php:95
msgid "The loopback request returned an unexpected http status code, %d, it was not possible to determine if this will prevent features from working as expected."
msgstr "Begäran om återanrop fick en oväntad http-svarskod, %d. Det gick inte att avgöra om detta kan hindra vissa funktioner att fungera som förväntat."

#: HealthCheck/class-health-check-loopback.php:79
msgid "The loopback request to your site failed, this means features relying on them are not currently working as expected."
msgstr "Återanropet till din webbplats misslyckades. Det betyder att funktioner som är beroende av dem för närvarande inte fungerar som förväntat."

#. translators: %d: The amount of notices that are visible.
#: mu-plugin/health-check-troubleshooting-mode.php:1507
msgid "Notices (%d)"
msgstr "Notiser (%d)"

#. translators: %d: The amount of available themes.
#: mu-plugin/health-check-troubleshooting-mode.php:1422
msgid "Available themes (%d)"
msgstr "Tillgängliga teman (%d)"

#. translators: %d: The amount of available plugins.
#: mu-plugin/health-check-troubleshooting-mode.php:1317
msgid "Available plugins (%d)"
msgstr "Tillgängliga tillägg (%d)"

#: mu-plugin/health-check-troubleshooting-mode.php:1268
msgid "enabled"
msgstr "aktiverat"

#. translators: %s: The running status of Troubleshooting Mode.
#: mu-plugin/health-check-troubleshooting-mode.php:1265
msgid "Troubleshooting Mode - %s"
msgstr "Felsökningsläge – %s"

#: mu-plugin/health-check-troubleshooting-mode.php:1175
msgctxt "Prefix for inactive themes in troubleshooting mode"
msgid "Switch to"
msgstr "Växla till"

#: mu-plugin/health-check-troubleshooting-mode.php:1175
msgctxt "Prefix for the active theme in troubleshooting mode"
msgid "Active:"
msgstr "Aktiv:"

#: HealthCheck/Tools/class-health-check-files-integrity.php:242
#: HealthCheck/Tools/class-health-check-files-integrity.php:251
msgid "You do not have access to this file."
msgstr "Du saknar åtkomst till denna fil."

#: HealthCheck/BackCompat/class-wp-debug-data.php:600
msgid "(Does not support 64bit values)"
msgstr "(Stöder inte 64-bitarsvärden)"

#: HealthCheck/BackCompat/class-wp-debug-data.php:624
msgid "Unable to determine what web server software is used"
msgstr "Det går inte att avgöra vilken programvara webbservern använder"

#. translators: %s: The custom message that may be included with the email.
#: HealthCheck/Tools/class-health-check-mail-check.php:74
msgid "Additional message from admin: %s"
msgstr "Extra meddelande från admin: %s"

#. translators: %1$s: website name. %2$s: website url. %3$s: The date the
#. message was sent. %4$s: The time the message was sent.
#: HealthCheck/Tools/class-health-check-mail-check.php:63
msgid "Hi! This test message was sent by the Health Check plugin from %1$s (%2$s) on %3$s at %4$s. Since you’re reading this, it obviously works."
msgstr "Hej! Det här är ett provmeddelande som skickades från tillägget Health Check på ”%1$s” (%2$s) den %3$s kl. %4$s. Det måste ha fungerat, eftersom du läser detta nu."

#. Plugin Name of the plugin
#: health-check.php
msgid "Health Check & Troubleshooting"
msgstr "Health Check & Troubleshooting"

#: HealthCheck/class-health-check-troubleshoot.php:172
msgid "You have successfully enabled Troubleshooting Mode, all plugins will appear inactive until you disable Troubleshooting Mode, or log out and back in again."
msgstr "Du har startat felsökningsläget och alla tillägg kommer att verka vara inaktiverade tills du antingen avslutar felsökningsläget eller loggar ut och sedan loggar in igen."

#. translators: %s: The active theme name.
#: HealthCheck/class-health-check-loopback.php:196
msgid "Active theme: %s"
msgstr "Aktivt tema: %s"

#: HealthCheck/class-health-check-loopback.php:188
#: HealthCheck/class-health-check-loopback.php:199
msgid "Waiting..."
msgstr "Väntar..."

#: mu-plugin/health-check-troubleshooting-mode.php:1459
msgid "Switch to this theme"
msgstr "Byt till detta tema"

#. translators: %s: Theme name.
#: mu-plugin/health-check-troubleshooting-mode.php:1455
msgid "Switch the active theme to %s"
msgstr "Byt aktivt tema till %s"

#: mu-plugin/health-check-troubleshooting-mode.php:1383
msgid "Enable"
msgstr "Aktivera"

#. translators: %s: Plugin name.
#: mu-plugin/health-check-troubleshooting-mode.php:1379
msgid "Enable the plugin, %s, while troubleshooting."
msgstr "Aktivera tillägget %s inom felsökningsläget."

#: mu-plugin/health-check-troubleshooting-mode.php:1362
msgid "Disable"
msgstr "Inaktivera"

#. translators: %s: Plugin name.
#: mu-plugin/health-check-troubleshooting-mode.php:1358
msgid "Disable the plugin, %s, while troubleshooting."
msgstr "Inaktivera tillägget %s inom felsökningsläget."

#: mu-plugin/health-check-troubleshooting-mode.php:1564
msgid "Dismiss notices"
msgstr "Avfärda notiser"

#: mu-plugin/health-check-troubleshooting-mode.php:1527
msgid "Plugin actions, such as activating and deactivating, are not available while in Troubleshooting Mode."
msgstr "Åtgärder med tillägg, såsom att aktivera eller inaktivera dem, är inte möjliga medan felsökningsläget är aktivt."

#: mu-plugin/health-check-troubleshooting-mode.php:1519
msgid "There are no notices to show."
msgstr "Det finns inga notiser att visa."

#: mu-plugin/health-check-troubleshooting-mode.php:1296
msgid "Here you can enable individual plugins or themes, helping you to find out what might be causing strange behaviors on your site. Do note that <strong>any changes you make to settings will be kept</strong> when you disable Troubleshooting Mode."
msgstr "Här kan du aktivera enstaka tillägg eller teman, vilket kan hjälpa dig att ta reda på vad som får webbplatsen att bete sig underligt. Observera att <strong>alla ändringar du gör i webbplatsens inställningar kommer att finnas kvar</strong> när felsökningsläget avslutas."

#: mu-plugin/health-check-troubleshooting-mode.php:1292
msgid "Your site is currently in Troubleshooting Mode. This has <strong>no effect on your site visitors</strong>, they will continue to view your site as usual, but for you it will look as if you had just installed WordPress for the first time."
msgstr "Din webbplats är just nu i felsökningsläge. Detta har <strong>ingen effekt på besökarna till din webbplats</strong>, för dem ser webbplatsen helt normal ut, men för dig ser det ut som om du just installerat WordPress för första gången."

#. Translators: %1$s: The theme slug that was switched to.. %2$s: The
#. force-enable link markup.
#: mu-plugin/health-check-troubleshooting-mode.php:984
msgid "When switching the active theme to %1$s, a site failure occurred. Because of this we reverted the theme to the one you used previously. %2$s"
msgstr "Vid byte av aktivt tema till %1$s inträffade ett fel på webbplatsen. Därför har temat återställts till det tema som användes innan. %2$s"

#. Translators: %1$s: The plugin slug that was disabled. %2$s: The
#. force-disable link markup.
#: mu-plugin/health-check-troubleshooting-mode.php:901
msgid "When disabling the plugin, %1$s, a site failure occurred. Because of this the change was automatically reverted. %2$s"
msgstr "Vid inaktivering av tillägget %1$s inträffade ett fel på webbplatsen. Därför backades ändringen automatiskt till ogjord. %2$s"

#. Translators: %1$s: The link-button markup to force enable the plugin. %2$s:
#. The force-enable link markup.
#: mu-plugin/health-check-troubleshooting-mode.php:816
msgid "When enabling the plugin, %1$s, a site failure occurred. Because of this the change was automatically reverted. %2$s"
msgstr "Vid aktivering av tillägget %1$s inträffade ett fel på webbplatsen. Därför backades ändringen automatiskt till ogjord. %2$s"

#: HealthCheck/class-health-check-troubleshoot.php:98
msgid "We could not remove the old must-use plugin."
msgstr "Det gick inte att ta bort det tidigare permanentaktiverade tillägget."

#: HealthCheck/Tools/class-health-check-mail-check.php:153
msgid "Send test mail"
msgstr "Skicka testmeddelande"

#: HealthCheck/Tools/class-health-check-mail-check.php:147
msgid "Additional message"
msgstr "Ytterligare meddelande"

#. translators: %s: website url.
#: HealthCheck/Tools/class-health-check-mail-check.php:59
msgid "Health Check – Test Message from %s"
msgstr "Health Check – Testmeddelande från %s"

#: HealthCheck/Tools/class-health-check-files-integrity.php:181
msgid "One possible reason for this may be that your installation contains translated versions. An easy way to clear this is to reinstall WordPress. Don't worry. This will only affect WordPress' own files, not your themes, plugins or uploaded media."
msgstr "En möjlig anledning till detta kan vara att din installation innehåller översatta versioner av några filer. Ett enkelt sätt att lösa detta är att installera om WordPress. Du behöver inte vara orolig, det berör bara WordPress egna filer och inte dina teman, tillägg eller uppladdade mediefiler."

#. translators: %s: .htaccess
#: HealthCheck/BackCompat/class-wp-debug-data.php:734
msgid "Your %s file contains only core WordPress features."
msgstr "Filen ”%s” innehåller endast WordPress egna grundläggande funktioner."

#. translators: %s: .htaccess
#: HealthCheck/BackCompat/class-wp-debug-data.php:731
msgid "Custom rules have been added to your %s file."
msgstr "Anpassade regler har lagts till filen ”%s”."

#: HealthCheck/BackCompat/class-wp-debug-data.php:738
msgid ".htaccess rules"
msgstr ".htaccess-regler"

#: HealthCheck/BackCompat/class-wp-debug-data.php:448
#: HealthCheck/BackCompat/class-wp-debug-data.php:470
msgid "Not available"
msgstr "Inte tillgänglig"

#. translators: %s: Latest WordPress version number.
#. translators: %s: Latest plugin version number.
#. translators: %s: Latest theme version number.
#: HealthCheck/BackCompat/class-wp-debug-data.php:58
#: HealthCheck/BackCompat/class-wp-debug-data.php:889
#: HealthCheck/BackCompat/class-wp-debug-data.php:921
#: HealthCheck/BackCompat/class-wp-debug-data.php:993
#: HealthCheck/BackCompat/class-wp-debug-data.php:1073
msgid "(Latest version: %s)"
msgstr "(Senaste version: %s)"

#. translators: Prefix for the active theme in a listing.
#: mu-plugin/health-check-troubleshooting-mode.php:1465
msgid "Active:"
msgstr "Aktivt:"

#: mu-plugin/health-check-troubleshooting-mode.php:1159
msgid "Themes"
msgstr "Teman"

#: HealthCheck/Tools/class-health-check-files-integrity.php:264
msgid "Modified"
msgstr "Ändrad version"

#: HealthCheck/Tools/class-health-check-files-integrity.php:262
msgid "Original"
msgstr "Original"

#: HealthCheck/Tools/class-health-check-mail-check.php:141
msgid "Email"
msgstr "E-post"

#: HealthCheck/Tools/class-health-check-mail-check.php:23
msgid "The Mail Check will invoke the <code>wp_mail()</code> function and check if it succeeds. We will use the E-mail address you have set up, but you can change it below if you like."
msgstr "Kontrollen av e-post kommer att anropa funktionen <code>wp_mail()</code> och kontrollera om detta lyckas. Vi använder den e-postadress du har angett men om du vill kan du ändra den här nedan."

#: HealthCheck/Tools/class-health-check-mail-check.php:22
msgid "Mail Check"
msgstr "Kontroll av e-post"

#: HealthCheck/Tools/class-health-check-files-integrity.php:285
msgid "Check the Files Integrity"
msgstr "Kontrollera av att filer är intakta"

#: HealthCheck/Tools/class-health-check-files-integrity.php:21
msgid "The File Integrity checks all the core files with the <code>checksums</code> provided by the WordPress API to see if they are intact. If there are changes you will be able to make a Diff between the files hosted on WordPress.org and your installation to see what has been changed."
msgstr "Kontrollen att filer är intakta kontrollerar alla filer i kärnan mot <code>kontrollsummor</code> från WordPress-API för att bekräfta att de är oskadade. Om ändringar detekteras kan du skapa en jämförelsefil mellan filen som finns på WordPress.org och filen i din installation för att kunna se vad som är ändrat."

#: HealthCheck/Tools/class-health-check-files-integrity.php:20
msgid "File integrity"
msgstr "Intakta filer"

#: HealthCheck/BackCompat/class-wp-debug-data.php:682
#: HealthCheck/BackCompat/class-wp-debug-data.php:687
msgid "cURL version"
msgstr "cURL-version"

#: HealthCheck/Tools/class-health-check-mail-check.php:100
msgid "It seems there was a problem sending the e-mail."
msgstr "Det verkar vara problem med att skicka e-posten."

#: HealthCheck/Tools/class-health-check-mail-check.php:89
msgid "We have just sent an e-mail using <code>wp_mail()</code> and it seems to work. Please check your inbox and spam folder to see if you received it."
msgstr "Vi har just skickat ett e-postmeddelande med hjälp av <code>wp_mail()</code> och det verkar fungera. Kontrollera din inkorg och skräpmeddelandena för att se om det kommit fram."

#: HealthCheck/class-health-check-loopback.php:171
msgid "Result from testing without any plugins active and a default theme"
msgstr "Resultat från test utan några aktiva tillägg och ett standardtema"

#: HealthCheck/Tools/class-health-check-files-integrity.php:187
#: HealthCheck/Tools/class-health-check-files-integrity.php:193
msgid "Reason"
msgstr "Orsak"

#: HealthCheck/Tools/class-health-check-files-integrity.php:185
#: HealthCheck/Tools/class-health-check-files-integrity.php:191
msgid "File"
msgstr "Fil"

#: HealthCheck/class-health-check.php:340
#: HealthCheck/Tools/class-health-check-files-integrity.php:183
#: HealthCheck/Tools/class-health-check-files-integrity.php:189
msgid "Status"
msgstr "Status"

#: HealthCheck/Tools/class-health-check-files-integrity.php:180
msgid "It appears as if some files may have been modified."
msgstr "Det verkar som om vissa filer har ändrats."

#: HealthCheck/Tools/class-health-check-files-integrity.php:176
msgid "All files passed the check. Everything seems to be ok!"
msgstr "Alla filer klarade kontrollen. Allt verkar vara OK!"

#: HealthCheck/Tools/class-health-check-files-integrity.php:108
msgid "File not found"
msgstr "Filen hittades inte"

#: HealthCheck/Tools/class-health-check-files-integrity.php:105
msgid "(View Diff)"
msgstr "(Visa ändringar)"

#: HealthCheck/Tools/class-health-check-files-integrity.php:105
msgid "Content changed"
msgstr "Innehållet ändrat"

#: HealthCheck/class-health-check.php:343
#: HealthCheck/class-health-check.php:352 pages/tools.php:21
msgid "Tools"
msgstr "Verktyg"

#: HealthCheck/class-health-check-troubleshoot.php:244
#: mu-plugin/health-check-troubleshooting-mode.php:1198
#: mu-plugin/health-check-troubleshooting-mode.php:1286
msgid "Disable Troubleshooting Mode"
msgstr "Avsluta felsökningsläget"

#: mu-plugin/health-check-troubleshooting-mode.php:178
msgid "You don't have any of the default themes installed. A default theme helps you determine if your current theme is causing conflicts."
msgstr "Du har inget standardtema installerat. Ett standardtema kan hjälpa dig att kontrollera om ditt nuvarande tema orsakar konflikter."

#: HealthCheck/class-health-check.php:332
msgctxt "Menu, Section and Page Title"
msgid "Health Check"
msgstr "Hälsokontroll"

#. Translators: %s: Plugin slug.
#: mu-plugin/health-check-troubleshooting-mode.php:1127
msgid "Enable %s"
msgstr "Aktivera %s"

#. Translators: %s: Plugin slug.
#: mu-plugin/health-check-troubleshooting-mode.php:1110
msgid "Disable %s"
msgstr "Inaktivera %s"

#: mu-plugin/health-check-troubleshooting-mode.php:1071
msgid "Manage active plugins"
msgstr "Hantera aktiverade tillägg"

#: mu-plugin/health-check-troubleshooting-mode.php:332
#: mu-plugin/health-check-troubleshooting-mode.php:406
msgid "Enable while troubleshooting"
msgstr "Aktivera i samband med felsökning"

#: mu-plugin/health-check-troubleshooting-mode.php:333
#: mu-plugin/health-check-troubleshooting-mode.php:392
msgid "Disable while troubleshooting"
msgstr "Inaktivera i samband med felsökning"

#: pages/troubleshoot.php:34
msgid "A Troubleshooting Mode menu is added to your admin bar, which will allow you to enable plugins individually, switch back to your current theme, and disable Troubleshooting Mode."
msgstr "En meny för felsökningsläget läggs till i adminmenyn, där du kan aktivera enstaka tillägg, växla tillbaka till ditt vanliga tema och avsluta felsökningsläget."

#: pages/troubleshoot.php:30
msgid "By enabling the Troubleshooting Mode, all plugins will appear inactive and your site will switch to the default theme only for you. All other users will see your site as usual."
msgstr "När du startar felsökningsläget kommer alla tillägg att verka vara inaktiverade och din webbplats kommer att växla till standardtemat, men bara för dig. För alla övriga användare kommer din webbplats att se ut som vanligt."

#: pages/troubleshoot.php:25
msgid "When troubleshooting issues on your site, you are likely to be told to disable all plugins and switch to the default theme."
msgstr "I samband med att du felsöker problem på din webbplats är det troligt att man ber dig inaktivera alla tillägg och växla till standardtemat."

#. translators: %1$d: The HTTP response code. %2$s: The error message returned.
#: HealthCheck/class-health-check-loopback.php:82
msgid "Error encountered: (%1$d) %2$s"
msgstr "Ett fel har uppstått: (%1$d) %2$s"

#: HealthCheck/BackCompat/class-wp-debug-data.php:797
msgid "Database name"
msgstr "Databasens namn"

#: HealthCheck/BackCompat/class-wp-debug-data.php:665
msgid "Max input time"
msgstr "Maximal inmatningstid"

#: HealthCheck/BackCompat/class-wp-debug-data.php:580
msgid "Ghostscript version"
msgstr "Ghostscript-version"

#: HealthCheck/BackCompat/class-wp-debug-data.php:575
msgid "Unable to determine if Ghostscript is installed"
msgstr "Det gick inte att avgöra om Ghostscript är installerat"

#: HealthCheck/BackCompat/class-wp-debug-data.php:559
msgid "GD version"
msgstr "GD-version"

#: HealthCheck/BackCompat/class-wp-debug-data.php:545
msgid "Imagick Resource Limits"
msgstr "Resursbegränsningar för Imagick"

#: HealthCheck/BackCompat/class-wp-debug-data.php:460
msgid "Active editor"
msgstr "Aktiv redigerare"

#: HealthCheck/BackCompat/class-wp-debug-data.php:189
msgid "Media Handling"
msgstr "Hantering av media"

#: HealthCheck/class-health-check.php:314
#: mu-plugin/health-check-troubleshooting-mode.php:326
msgid "Troubleshoot"
msgstr "Felsök"

#: HealthCheck/class-health-check-troubleshoot.php:176
msgid "Return to the Dashboard"
msgstr "Återgå till adminpanelen"

#: HealthCheck/class-health-check-troubleshoot.php:149
msgid "We were unable to replace the plugin file required to enable the Troubleshooting Mode."
msgstr "Det gick inte att byta ut tilläggsfilen som behövs för att aktivera felsökningsläget."

#: HealthCheck/class-health-check-troubleshoot.php:255
msgid "Enable Troubleshooting Mode"
msgstr "Starta felsökningsläge"

#: mu-plugin/health-check-troubleshooting-mode.php:1080
msgid "Plugins"
msgstr "Tillägg"

#: mu-plugin/health-check-troubleshooting-mode.php:1061
msgid "Troubleshooting Mode"
msgstr "Felsökningsläge"

#: pages/troubleshoot.php:38
msgid "Please note, that due to how Must Use plugins work, any such plugin will not be disabled for the troubleshooting session."
msgstr "Observera! Beroende på hur permanentaktiverade tillägg (MU-tillägg) fungerar, kan inga sådana tillägg inaktiveras i samband med felsökningen."

#: pages/troubleshoot.php:26
msgid "Understandably, you do not wish to do so as it may affect your site visitors, leaving them with lost functionality."
msgstr "Det är förståeligt att du kanske drar dig för att göra det, eftersom det kan påverka dina webbplatsbesökare som kan komma att bli utan vissa funktioner."

#: HealthCheck/class-health-check-troubleshoot.php:105
msgid "We were unable to copy the plugin file required to enable the Troubleshooting Mode."
msgstr "Det gick inte att kopiera tilläggsfilen som behövs för att aktivera felsökningsläget."

#: HealthCheck/class-health-check-troubleshoot.php:90
msgid "We were unable to create the mu-plugins directory."
msgstr "Det gick inte att skapa katalogen ”mu-plugins”."

#: HealthCheck/class-health-check.php:342
#: HealthCheck/class-health-check.php:351
msgid "Troubleshooting"
msgstr "Felsökning"

#: HealthCheck/class-health-check-loopback.php:103
msgid "The loopback request to your site completed successfully."
msgstr "Egenanrop till din egen webbplats lyckades."

#. translators: %s: Plugin version number.
#. translators: %s: Theme version number.
#: HealthCheck/BackCompat/class-wp-debug-data.php:843
#: HealthCheck/BackCompat/class-wp-debug-data.php:882
#: HealthCheck/BackCompat/class-wp-debug-data.php:1066
msgid "Version %s"
msgstr "Version %s"

#. translators: %s: Plugin author name.
#. translators: %s: Theme author name.
#: HealthCheck/BackCompat/class-wp-debug-data.php:837
#: HealthCheck/BackCompat/class-wp-debug-data.php:876
#: HealthCheck/BackCompat/class-wp-debug-data.php:1060
msgid "By %s"
msgstr "Av %s"

#: HealthCheck/BackCompat/class-wp-debug-data.php:827
#: HealthCheck/BackCompat/class-wp-debug-data.php:866
#: HealthCheck/BackCompat/class-wp-debug-data.php:1050
msgid "No version or author information is available."
msgstr "Ingen information om författaren är tillgänglig."

#: HealthCheck/BackCompat/class-wp-debug-data.php:600
msgid "(Supports 64bit values)"
msgstr "(Stöder 64-bitsvärden)"

#. Author of the plugin
#: health-check.php
msgid "The WordPress.org community"
msgstr "WordPress.org-gemenskapen"

#. Description of the plugin
#: health-check.php
msgid "Checks the health of your WordPress install."
msgstr "Kontrollerar hälsan hos din WordPress-installation."

#. Plugin URI of the plugin
#. Author URI of the plugin
#: health-check.php
msgid "https://wordpress.org/plugins/health-check/"
msgstr "https://sv.wordpress.org/plugins/health-check/"

#: HealthCheck/Tools/class-health-check-phpinfo.php:24
msgid "Some scenarios require you to look up more detailed server configurations than what is normally required. The PHP Info page allows you to view all available configuration options for your PHP setup. Please be advised that WordPress does not guarantee that any information shown on that page may not be considered sensitive."
msgstr "Vissa scenarier kräver att du kontrollerar mer detaljerad information om serverkonfigurationen än vad som normalt behövs. Sidan med PHP-info låter dig se alla tillgängliga konfigurationsalternativ för din PHP-konfiguration. WordPress kan inte kan garantera att denna sida inte innehåller någon information som kan anses vara privat."

#: HealthCheck/Tools/class-health-check-phpinfo.php:22 pages/phpinfo.php:26
msgid "The phpinfo() function has been disabled by your host. Please contact the host if you need more information about your setup."
msgstr "Ditt webbhotell har inaktiverat funktionen phpinfo(). Kontakta webbhotellet om du behöver mer information om serverkonfigurationen."

#. translators: 1: Theme name. 2: Theme slug.
#: HealthCheck/BackCompat/class-wp-debug-data.php:930
#: HealthCheck/BackCompat/class-wp-debug-data.php:949
#: HealthCheck/BackCompat/class-wp-debug-data.php:1004
#: HealthCheck/BackCompat/class-wp-debug-data.php:1080
msgid "%1$s (%2$s)"
msgstr "%1$s (%2$s)"

#: HealthCheck/BackCompat/class-wp-debug-data.php:969
msgid "Parent theme"
msgstr "Huvudtema"

#: HealthCheck/BackCompat/class-wp-debug-data.php:964
#: HealthCheck/BackCompat/class-wp-debug-data.php:1019
msgid "Author website"
msgstr "Författarens webbplats"

#: HealthCheck/BackCompat/class-wp-debug-data.php:960
#: HealthCheck/BackCompat/class-wp-debug-data.php:1015
msgid "Author"
msgstr "Författare"

#: HealthCheck/BackCompat/class-wp-debug-data.php:946
#: HealthCheck/BackCompat/class-wp-debug-data.php:1001
msgid "Name"
msgstr "Namn"

#. translators: 1: Plugin version number. 2: Plugin author name.
#. translators: 1: Theme version number. 2: Theme author name.
#: HealthCheck/BackCompat/class-wp-debug-data.php:832
#: HealthCheck/BackCompat/class-wp-debug-data.php:871
#: HealthCheck/BackCompat/class-wp-debug-data.php:1055
msgid "Version %1$s by %2$s"
msgstr "Version %1$s av %2$s"

#: HealthCheck/BackCompat/class-wp-debug-data.php:791
msgid "Database host"
msgstr "Databasvärd"

#: HealthCheck/BackCompat/class-wp-debug-data.php:780
msgid "Client version"
msgstr "Klientversion"

#: HealthCheck/BackCompat/class-wp-debug-data.php:775
msgid "Server version"
msgstr "Serverversion"

#: HealthCheck/BackCompat/class-wp-debug-data.php:770
msgid "Extension"
msgstr "Tillägg"

#: HealthCheck/BackCompat/class-wp-debug-data.php:706
msgid "Is the Imagick library available?"
msgstr "Är biblioteket Imagick tillgängligt?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:673
msgid "PHP post max size"
msgstr "Största storlek för PHP post"

#: HealthCheck/BackCompat/class-wp-debug-data.php:669
msgid "Upload max filesize"
msgstr "Max filstorlek för filuppladdning"

#: HealthCheck/BackCompat/class-wp-debug-data.php:660
msgid "PHP memory limit"
msgstr "PHP-minnesgräns"

#: HealthCheck/BackCompat/class-wp-debug-data.php:655
msgid "PHP time limit"
msgstr "PHP-tidsgräns"

#: HealthCheck/BackCompat/class-wp-debug-data.php:651
msgid "PHP max input variables"
msgstr "Maxvärden för PHP inmatning"

#. translators: %s: ini_get()
#: HealthCheck/BackCompat/class-wp-debug-data.php:488
#: HealthCheck/BackCompat/class-wp-debug-data.php:644
msgid "Unable to determine some settings, as the %s function has been disabled."
msgstr "Kan inte fastställa vissa inställningar, eftersom funktionen %s har inaktiverats."

#: HealthCheck/BackCompat/class-wp-debug-data.php:641
msgid "Server settings"
msgstr "Serverinställningar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:634
msgid "Unable to determine PHP SAPI"
msgstr "Det går inte att avgöra PHP SAPI"

#: HealthCheck/BackCompat/class-wp-debug-data.php:633
msgid "PHP SAPI"
msgstr "PHP SAPI"

#: HealthCheck/BackCompat/class-wp-debug-data.php:607
msgid "Unable to determine PHP version"
msgstr "Det går inte att avgöra PHP-versionen"

#: HealthCheck/BackCompat/class-wp-debug-data.php:619
msgid "Unable to determine server architecture"
msgstr "Det går inte att avgöra serverarkitekturen"

#: HealthCheck/BackCompat/class-wp-debug-data.php:618
msgid "Server architecture"
msgstr "Server-arkitektur"

#. translators: 1: The IP address WordPress.org resolves to. 2: The error
#. returned by the lookup.
#: HealthCheck/BackCompat/class-wp-debug-data.php:433
msgid "Unable to reach WordPress.org at %1$s: %2$s"
msgstr "Det går inte att nå WordPress.org på %1$s: %2$s"

#: HealthCheck/BackCompat/class-wp-debug-data.php:425
msgid "WordPress.org is reachable"
msgstr "WordPress.org är nåbar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:424
#: HealthCheck/BackCompat/class-wp-debug-data.php:430
msgid "Communication with WordPress.org"
msgstr "Kommunikation med WordPress.org"

#: HealthCheck/BackCompat/class-wp-debug-data.php:407
msgid "Network count"
msgstr "Antal nätverk"

#: HealthCheck/BackCompat/class-wp-debug-data.php:402
msgid "Site count"
msgstr "Antal webbplatser"

#: HealthCheck/BackCompat/class-wp-debug-data.php:397
#: HealthCheck/BackCompat/class-wp-debug-data.php:414
msgid "User count"
msgstr "Antal användare"

#: HealthCheck/BackCompat/class-wp-debug-data.php:1094
msgid "The must use plugins directory"
msgstr "Katalogen för MU-tillägg (som alltid körs)"

#: HealthCheck/BackCompat/class-wp-debug-data.php:373
msgid "The themes directory"
msgstr "Katalogen themes"

#: HealthCheck/BackCompat/class-wp-debug-data.php:368
msgid "The plugins directory"
msgstr "Katalogen plugins"

#: HealthCheck/BackCompat/class-wp-debug-data.php:363
msgid "The uploads directory"
msgstr "Katalogen uploads"

#: HealthCheck/BackCompat/class-wp-debug-data.php:358
msgid "The wp-content directory"
msgstr "Katalogen wp-content"

#: HealthCheck/BackCompat/class-wp-debug-data.php:354
#: HealthCheck/BackCompat/class-wp-debug-data.php:359
#: HealthCheck/BackCompat/class-wp-debug-data.php:364
#: HealthCheck/BackCompat/class-wp-debug-data.php:369
#: HealthCheck/BackCompat/class-wp-debug-data.php:374
#: HealthCheck/BackCompat/class-wp-debug-data.php:1095
msgid "Not writable"
msgstr "Ej skrivbar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:354
#: HealthCheck/BackCompat/class-wp-debug-data.php:359
#: HealthCheck/BackCompat/class-wp-debug-data.php:364
#: HealthCheck/BackCompat/class-wp-debug-data.php:369
#: HealthCheck/BackCompat/class-wp-debug-data.php:374
#: HealthCheck/BackCompat/class-wp-debug-data.php:1095
msgid "Writable"
msgstr "Skrivbar"

#: HealthCheck/BackCompat/class-wp-debug-data.php:353
msgid "The main WordPress directory"
msgstr "Den huvudsakliga WordPress katalogen"

#: HealthCheck/BackCompat/class-wp-debug-data.php:349
msgid "Filesystem Permissions"
msgstr "Rättigheter i filsystemet"

#: HealthCheck/BackCompat/class-wp-debug-data.php:205
#: HealthCheck/BackCompat/class-wp-debug-data.php:215
#: HealthCheck/BackCompat/class-wp-debug-data.php:224
#: HealthCheck/BackCompat/class-wp-debug-data.php:233
#: HealthCheck/BackCompat/class-wp-debug-data.php:242
#: HealthCheck/BackCompat/class-wp-debug-data.php:286
#: HealthCheck/BackCompat/class-wp-debug-data.php:291
#: HealthCheck/BackCompat/class-wp-debug-data.php:301
#: HealthCheck/BackCompat/class-wp-debug-data.php:306
#: HealthCheck/BackCompat/class-wp-debug-data.php:503
msgid "Disabled"
msgstr "Inaktiverat"

#: HealthCheck/BackCompat/class-wp-debug-data.php:210
#: HealthCheck/BackCompat/class-wp-debug-data.php:215
#: HealthCheck/BackCompat/class-wp-debug-data.php:224
#: HealthCheck/BackCompat/class-wp-debug-data.php:233
#: HealthCheck/BackCompat/class-wp-debug-data.php:242
#: HealthCheck/BackCompat/class-wp-debug-data.php:286
#: HealthCheck/BackCompat/class-wp-debug-data.php:291
#: HealthCheck/BackCompat/class-wp-debug-data.php:301
#: HealthCheck/BackCompat/class-wp-debug-data.php:306
#: HealthCheck/BackCompat/class-wp-debug-data.php:503
msgid "Enabled"
msgstr "Aktiverat"

#: HealthCheck/BackCompat/class-wp-debug-data.php:218
#: HealthCheck/BackCompat/class-wp-debug-data.php:227
#: HealthCheck/BackCompat/class-wp-debug-data.php:236
#: HealthCheck/BackCompat/class-wp-debug-data.php:245
#: HealthCheck/BackCompat/class-wp-debug-data.php:260
#: HealthCheck/BackCompat/class-wp-debug-data.php:265
#: HealthCheck/BackCompat/class-wp-debug-data.php:331
#: HealthCheck/BackCompat/class-wp-debug-data.php:336
#: HealthCheck/BackCompat/class-wp-debug-data.php:965
#: HealthCheck/BackCompat/class-wp-debug-data.php:1020
msgid "Undefined"
msgstr "Odefinierat"

#: HealthCheck/BackCompat/class-wp-debug-data.php:250
msgid "WordPress Constants"
msgstr "WordPress-konstanter"

#: HealthCheck/BackCompat/class-wp-debug-data.php:200
msgid "Database"
msgstr "Databas"

#: HealthCheck/BackCompat/class-wp-debug-data.php:195
msgid "The options shown below relate to your server setup. If changes are required, you may need your web host&#8217;s assistance."
msgstr "Alternativen som visas nedan avser din serverkonfiguration.Om något behöver ändras kan du behöva hjälp från ditt webbhotell."

#: HealthCheck/BackCompat/class-wp-debug-data.php:194
msgid "Server"
msgstr "Server"

#: HealthCheck/BackCompat/class-wp-debug-data.php:183
msgid "Inactive Plugins"
msgstr "Inaktiva tillägg"

#: HealthCheck/BackCompat/class-wp-debug-data.php:177
msgid "Active Plugins"
msgstr "Aktiva tillägg"

#: HealthCheck/BackCompat/class-wp-debug-data.php:171
msgid "Must Use Plugins"
msgstr "Permanentaktiverade tillägg (must use)"

#: HealthCheck/BackCompat/class-wp-debug-data.php:155
msgid "Active Theme"
msgstr "Aktivt tema"

#: HealthCheck/BackCompat/class-wp-debug-data.php:144
msgid "Drop-ins"
msgstr "Drop-in"

#: HealthCheck/BackCompat/class-wp-debug-data.php:109
msgid "Is this a multisite?"
msgstr "Är detta en nätverksinstallation (multisite)?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:124
msgid "Default comment status"
msgstr "Standardinställning för kommentarer"

#: HealthCheck/BackCompat/class-wp-debug-data.php:114
msgid "Can anyone register on this site?"
msgstr "Kan vem som helst registrera sig på denna webbplats?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:105
#: HealthCheck/BackCompat/class-wp-debug-data.php:110
#: HealthCheck/BackCompat/class-wp-debug-data.php:115
#: HealthCheck/BackCompat/class-wp-debug-data.php:120
#: HealthCheck/BackCompat/class-wp-debug-data.php:698
#: HealthCheck/BackCompat/class-wp-debug-data.php:707
#: HealthCheck/BackCompat/class-wp-debug-data.php:716
msgid "No"
msgstr "Nej"

#: HealthCheck/BackCompat/class-wp-debug-data.php:105
#: HealthCheck/BackCompat/class-wp-debug-data.php:110
#: HealthCheck/BackCompat/class-wp-debug-data.php:115
#: HealthCheck/BackCompat/class-wp-debug-data.php:120
#: HealthCheck/BackCompat/class-wp-debug-data.php:698
#: HealthCheck/BackCompat/class-wp-debug-data.php:707
#: HealthCheck/BackCompat/class-wp-debug-data.php:716
msgid "Yes"
msgstr "Ja"

#: HealthCheck/BackCompat/class-wp-debug-data.php:104
msgid "Is this site using HTTPS?"
msgstr "Använder denna webbplats HTTPS?"

#: HealthCheck/BackCompat/class-wp-debug-data.php:99
msgid "Permalink structure"
msgstr "Permalänks-struktur"

#: HealthCheck/BackCompat/class-wp-debug-data.php:94
msgid "Site URL"
msgstr "Webbplatsens URL"

#: HealthCheck/BackCompat/class-wp-debug-data.php:89
msgid "Home URL"
msgstr "Startsidans URL"

#: HealthCheck/BackCompat/class-wp-debug-data.php:72
#: HealthCheck/BackCompat/class-wp-debug-data.php:955
#: HealthCheck/BackCompat/class-wp-debug-data.php:1010
#: HealthCheck/Tools/class-health-check-plugin-compatibility.php:38
msgid "Version"
msgstr "Version"

#: HealthCheck/BackCompat/class-wp-debug-data.php:69
msgid "WordPress"
msgstr "WordPress"