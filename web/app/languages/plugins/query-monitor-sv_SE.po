# Translation of Plugins - Query Monitor &#8211; The developer tools panel for WordPress - Stable (latest release) in Swedish
# This file is distributed under the same license as the Plugins - Query Monitor &#8211; The developer tools panel for WordPress - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-21 14:58:51+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: sv_SE\n"
"Project-Id-Version: Plugins - Query Monitor &#8211; The developer tools panel for WordPress - Stable (latest release)\n"

#. translators: An HTTP API request redirected to another URL
#: output/html/http.php:205
msgid "This HTTP request was redirected to:"
msgstr "Denna HTTP-förfrågan omdirigerades till:"

#. translators: %s: The name of a filter that short-circuited an HTTP API
#. request
#: output/html/http.php:194
msgid "This HTTP request was short-circuited by the %s filter and was not sent"
msgstr "Denna HTTP-förfrågan fångades upp av filtret %s och skickades aldrig"

#: output/html/environment.php:281
msgid "Basic Auth"
msgstr "Grundläggande autentisering"

#: output/html/assets.php:53
msgid "Module"
msgstr "Modul"

#: collectors/logger.php:173
msgid "Assertion failed"
msgstr "Påstående misslyckades"

#. translators: %s: Assertion message
#: collectors/logger.php:169
msgid "Assertion failed: %s"
msgstr "Påstående misslyckades: %s"

#: collectors/logger.php:161
msgid "Assertion passed"
msgstr "Påstående godkänt"

#. translators: %s: Assertion message
#: collectors/logger.php:157
msgid "Assertion passed: %s"
msgstr "Påstående godkänt: %s"

#: classes/Collector.php:124 output/html/environment.php:240
msgid "empty string"
msgstr "tom sträng"

#. translators: %s: Drop-in plugin file name
#: classes/Component_Dropin.php:12
msgid "Drop-in: %s"
msgstr "Insticksprogram: %s"

#: output/html/hooks.php:36
msgid "Hooks, Actions, & Filters"
msgstr "Åtgärds-hookar, åtgärder och filter"

#: output/html/environment.php:245
msgid "Development Mode"
msgstr "Utvecklingsläge"

#. translators: %s: Total number of Doing it Wrong occurrences
#: output/html/doing_it_wrong.php:118
msgctxt "Total Doing it Wrong occurrences"
msgid "Total: %s"
msgstr "Totalt: %s"

#: output/html/doing_it_wrong.php:57
msgid "No occurrences."
msgstr "Inga förekomster."

#. translators: %s: Total number of Doing it Wrong occurrences
#: output/html/doing_it_wrong.php:43
msgctxt "Doing it Wrong"
msgid "Doing it Wrong (%s)"
msgstr "Gör det fel (%s)"

#: output/html/doing_it_wrong.php:41
msgid "Doing it Wrong occurrences"
msgstr "”Gör det fel”-förekomster"

#. translators: %s: Total number of Doing it Wrong occurrences
#: output/html/doing_it_wrong.php:40
msgctxt "Doing it Wrong"
msgid "Total: %s"
msgstr "Totalt: %s"

#: output/html/doing_it_wrong.php:31
msgid "Doing it Wrong"
msgstr "Gör det fel"

#. translators: %s: Name of WordPress filter
#: dispatchers/Html.php:608
msgid "The file link format for your editor is set by the %s filter."
msgstr "Länkformatet för filen för din redigerare är angivet av filtret %s."

#. translators: 1: WordPress hook name, 2: Version number, 3: Optional message
#. regarding the change.
#: classes/Deprecated_Hook_Run.php:38
msgid "Hook %1$s is deprecated since version %2$s with no alternative available. %3$s"
msgstr "Åtgärds-hooken %1$s är markerad som föråldrad sedan version %2$s utan något ersättningsalternativ. %3$s"

#. translators: 1: WordPress hook name, 2: Version number, 3: Alternative hook
#. name, 4: Optional message regarding the change.
#: classes/Deprecated_Hook_Run.php:28
msgid "Hook %1$s is deprecated since version %2$s! Use %3$s instead. %4$s"
msgstr "Åtgärds-hooken %1$s är markerad som föråldrad sedan version %2$s! Använd %3$s i stället. %4$s"

#. translators: 1: PHP function name, 2: Version number.
#: classes/Deprecated_Argument_Run.php:35
msgid "Function %1$s was called with an argument that is deprecated since version %2$s with no alternative available."
msgstr "Funktionen %1$s anropades med ett argument som är taget ur bruk sedan version %2$s utan något ersättningsalternativ."

#. translators: 1: PHP function name, 2: Version number, 3: Optional message
#. regarding the change.
#: classes/Deprecated_Argument_Run.php:26
msgid "Function %1$s was called with an argument that is deprecated since version %2$s! %3$s"
msgstr "Funktionen %1$s anropades med ett argument som är taget ur bruk sedan version %2$s! %3$s"

#. translators: 1: PHP file name, 2: Version number, 3: Optional message
#. regarding the change.
#: classes/Deprecated_File_Included.php:38
msgid "File %1$s is deprecated since version %2$s with no alternative available. %3$s"
msgstr "Filen %1$s är markerad som föråldrad sedan version %2$s utan något ersättningsalternativ. %3$s"

#. translators: 1: PHP file name, 2: Version number, 3: Alternative file name,
#. 4: Optional message regarding the change.
#: classes/Deprecated_File_Included.php:28
msgid "File %1$s is deprecated since version %2$s! Use %3$s instead. %4$s"
msgstr "Filen %1$s är markerad som föråldrad sedan version %2$s! Använd %3$s i stället. %4$s"

#. translators: 1: PHP class name, 2: PHP parent class name, 3: Version number,
#. 4: __construct() method.
#: classes/Deprecated_Constructor_Run.php:26
msgid "The called constructor method for %1$s class in %2$s is deprecated since version %3$s! Use %4$s instead."
msgstr "Den anropade konstruktormetoden för klassen %1$s i %2$s är tagen ur bruk sedan version %3$s! Använd %4$s i stället."

#. translators: 1: PHP class name, 2: Version number, 3: __construct() method.
#: classes/Deprecated_Constructor_Run.php:36
msgid "The called constructor method for %1$s class is deprecated since version %2$s! Use %3$s instead."
msgstr "Den anropade konstruktormetoden för klassen %1$s är tagen ur bruk sedan version %2$s! Använd %3$s i stället."

#. translators: 1: PHP function name, 2: Version number, 3: Alternative
#. function name.
#: classes/Deprecated_Function_Run.php:26
msgid "Function %1$s is deprecated since version %2$s! Use %3$s instead."
msgstr "Funktionen %1$s är markerad som föråldrad sedan version %2$s! Använd %3$s i stället."

#. translators: 1: PHP function name, 2: Version number.
#: classes/Deprecated_Function_Run.php:35
msgid "Function %1$s is deprecated since version %2$s with no alternative available."
msgstr "Funktionen %1$s är markerad som föråldrad sedan version %2$s utan något ersättningsalternativ."

#. translators: Developer debugging message. 1: PHP function name, 2:
#. Explanatory message, 3: WordPress version number.
#: classes/Doing_It_Wrong_Run.php:33
msgid "Function %1$s was called incorrectly. %2$s %3$s"
msgstr "Funktionen %1$s anropades felaktigt. %2$s %3$s"

#. translators: %s: Version number.
#: classes/Doing_It_Wrong_Run.php:26
msgid "(This message was added in version %s.)"
msgstr "(Detta meddelande lades till i version %s.)"

#. translators: %s: Number of PHP errors
#: output/html/php_errors.php:201
msgctxt "PHP error count"
msgid "Total: %s"
msgid_plural "Total: %s"
msgstr[0] "Totalt: %s"
msgstr[1] "Totalt: %s"

#: output/html/theme.php:84
msgid "Block Template"
msgstr "Blockmall"

#: output/html/timing.php:216
msgid "Timings"
msgstr "Tider"

#. translators: %s: Link to help article
#: output/html/timing.php:45
msgid "No data logged. <a href=\"%s\">Read about timing and profiling in Query Monitor</a>."
msgstr "Inga data loggade. <a href=\"%s\">Läs om tider och profilering i Query Monitor</a>."

#: output/html/multisite.php:52
msgid "Site Switch"
msgstr "Växla webbplats"

#: output/html/multisite.php:51
msgid "Function"
msgstr "Funktion"

#: output/html/multisite.php:37
msgid "No data logged."
msgstr "Inga data loggade."

#. translators: A closure is an anonymous PHP function
#: classes/Util.php:421
msgid "Unknown closure"
msgstr "Okänd ”closure” (anonym PHP-funktion)"

#. translators: %s: Name of a multilingual plugin
#: output/html/languages.php:62 output/html/languages.php:75
msgid "%s Language"
msgstr "%s-språk"

#: dispatchers/Html.php:624
msgctxt "colour scheme"
msgid "Dark"
msgstr "Mörkt"

#: dispatchers/Html.php:623
msgctxt "colour scheme"
msgid "Light"
msgstr "Ljust"

#: dispatchers/Html.php:622
msgctxt "colour scheme"
msgid "Auto"
msgstr "Auto"

#: dispatchers/Html.php:619
msgid "Your browser color scheme is respected by default. You can override it here."
msgstr "Din webbläsares färgschema respekteras som standard. Du kan åsidosätta det här."

#: dispatchers/Html.php:617
msgid "Appearance"
msgstr "Utseende"

#. translators: %s: Memory usage in megabytes with a decimal fraction. Note the
#. space between value and unit symbol.
#: output/headers/overview.php:40 output/html/overview.php:392
msgid "%s MB"
msgstr "%s MB"

#: output/html/overview.php:361
msgid "Speak to your web host about enabling an opcode cache such as OPcache."
msgstr "Be ditt webbhotell att aktivera en opcode-cache, te.x. OPcache."

#: output/html/overview.php:358
msgid "Opcode cache not in use"
msgstr "Opcode-cache används inte"

#: output/html/overview.php:342
msgid "Opcode Cache"
msgstr "Opcode-cache"

#: output/html/overview.php:325
msgid "Speak to your web host about enabling an object cache extension such as Redis or Memcached."
msgstr "Be ditt webbhotell att aktivera en utökning för objektcache, t.ex. Redis eller Memcached."

#. translators: 1: PHP extension name, 2: URL to plugin directory
#: output/html/overview.php:308
msgid "The %1$s object cache extension for PHP is installed but is not in use by WordPress. You should <a href=\"%2$s\" target=\"_blank\" class=\"qm-external-link\">install a %1$s plugin</a>."
msgstr "Objektcacheutökningen %1$s för PHP är installerad men används inte av WordPress. Du bör <a href=\"%2$s\" target=\"_blank\" class=\"qm-external-link\">installera ett tillägg för %1$s</a>."

#: output/html/overview.php:293
msgid "Persistent object cache plugin not in use"
msgstr "Tillägg för beständig objektcachelagring används inte"

#: output/html/overview.php:286
msgid "Persistent object cache plugin in use"
msgstr "Tillägg för beständig objektcachelagring används"

#. translators: 1: Memory used in bytes, 2: Memory used in megabytes
#: output/html/overview.php:147
msgid "%1$s bytes (%2$s MB)"
msgstr "%1$s bytes (%2$s MB)"

#. translators: %s: A time in seconds with a decimal fraction. No space between
#. value and unit symbol.
#: output/html/db_queries.php:489 output/html/overview.php:105
#: output/html/overview.php:190 output/html/overview.php:238
#: output/html/overview.php:387
msgctxt "Time in seconds"
msgid "%ss"
msgstr "%ss"

#. translators: %s: Total number of items in a list
#: output/html/logger.php:75
msgid "All (%d)"
msgstr "Alla (%d)"

#: output/html/environment.php:280
msgid "Architecture"
msgstr "Arkitektur"

#. translators: %s: Number of database queries. Note the space between value
#. and unit symbol.
#: output/html/db_queries.php:494 output/html/db_queries.php:508
msgid "%s Q"
msgid_plural "%s Q"
msgstr[0] "%s Q"
msgstr[1] "%s Q"

#: output/html/assets_styles.php:38
msgid "No CSS files were enqueued."
msgstr "Inga CSS-filer sattes i kö."

#: output/html/assets_scripts.php:38
msgid "No JavaScript files were enqueued."
msgstr "Inga JavaScript-filer sattes i kö."

#: output/Html.php:324
msgid "Non-WordPress Core"
msgstr "Inte WordPress kärna"

#: dispatchers/Html.php:664
msgid "Allow the wp-content/db.php file symlink to be put into place during activation. Set to false to prevent the symlink creation."
msgstr "Tillåt att symbollänken för wp-content/db.php-filen placeras på plats under aktiveringen. Ange ”false” för att förhindra att symbollänk skapas."

#. translators: %s: Number of hooks
#: dispatchers/Html.php:388
msgid "Hooks in Use (%s)"
msgstr "Åtgärds-hookar som används (%s)"

#: dispatchers/Html.php:293
msgid "Data collection ceased"
msgstr "Datainsamlingen upphörde"

#: classes/Component_Core.php:10 output/Html.php:319 output/html/hooks.php:96
msgid "WordPress Core"
msgstr "WordPress kärna"

#: output/html/overview.php:246
msgctxt "HTTP API calls"
msgid "Total"
msgstr "Totalt"

#: output/html/block_editor.php:64
msgid "Context"
msgstr "Sammanhang"

#. translators: 1: Percentage of memory limit used, 2: Memory limit in
#. megabytes
#: output/headers/overview.php:47 output/html/overview.php:161
msgid "%1$s%% of %2$s MB server limit"
msgstr "%1$s %% av serverns begränsning på %2$s MB"

#: classes/QueryMonitor.php:84
msgctxt "verb"
msgid "Sponsor"
msgstr "Sponsor"

#. translators: %s: File name
#: output/html/db_queries.php:194
msgid "Extended query information such as the component and affected rows is not available. Query Monitor was unable to symlink its %s file into place."
msgstr "Utökad info om databasfrågan, såsom berörd komponent och kodrad är inte tillgänglig. Query Monitor kunde inte använda symbolisk länk för inplacering av sin fil %s."

#. translators: 1: File name, 2: Configuration constant name
#: output/html/db_queries.php:191
msgid "Extended query information such as the component and affected rows is not available. Query Monitor was prevented from symlinking its %1$s file into place by the %2$s constant."
msgstr "Utökad info om databasfrågan, såsom berörd komponent och kodrad är inte tillgänglig. På grund av konstanten %2$s kunde Query Monitor inte använda symbolisk länk för inplacering av sin fil %1$s."

#. translators: %s: File name
#: output/html/db_queries.php:188
msgid "Extended query information such as the component and affected rows is not available. A conflicting %s file is present."
msgstr "Utökad info om databasfrågan, såsom berörd komponent och kodrad är inte tillgänglig. Krock med befintlig fil %s."

#. translators: %s: Dependency name
#: classes/Component_Altis_Vendor.php:12
msgid "Dependency: %s"
msgstr "Beroende: %s"

#. translators: %s: Link to help article
#: output/html/logger.php:46
msgid "No data logged. <a href=\"%s\">Read about logging variables in Query Monitor</a>."
msgstr "Inga data loggade. <a href=\"%s\">Läs om loggningsvariabler i Query Monitor</a>."

#: output/html/environment.php:226
msgid "Environment Type"
msgstr "Typ av driftsmiljö"

#: dispatchers/Html.php:652
msgid "Hide Query Monitor itself from various panels. Set to false if you want to see how Query Monitor hooks into WordPress."
msgstr "Dölj själva Query Monitor i olika paneler. Ange värdet false om du vill se hur Query Monitor via åtgärds-hookar integreras mot WordPress."

#: dispatchers/Html.php:264
msgid "PHP Fatal Error"
msgstr "Allvarligt PHP-fel"

#. translators: 1: CLI command to run, 2: plugin directory name
#: dispatchers/Html.php:211
msgid "Asset files for Query Monitor need to be built. Run %1$s from the %2$s directory."
msgstr "Tillgångsfiler för Query Monitor måste byggas. Kör %1$s från katalogen %2$s."

#. translators: %s: Plugin or theme name
#: dispatchers/WP_Die.php:120
msgid "This message was triggered by %s."
msgstr "Detta meddelande utlöses av %s."

#: dispatchers/Html.php:604
msgid "Saved! Reload to apply changes."
msgstr "Sparat! Ladda om för att tillämpa ändringar."

#: dispatchers/Html.php:600
msgid "Set editor cookie"
msgstr "Spara cookie för programutvecklingsmiljö"

#: dispatchers/Html.php:576
msgid "You can set your editor here, so that when you click on stack trace links the file opens in your editor."
msgstr "Här kan du ställa in den programutvecklingsmiljö du använder, så att den automatiskt öppnar de stackspårslänkar du klickar på."

#: dispatchers/Html.php:573
msgid "Editor"
msgstr "Programutvecklingsmiljö"

#. translators: %s: Number of logs that are available
#: output/html/logger.php:219
msgid "Logs (%s)"
msgstr "Loggar (%s)"

#: output/html/request.php:197
msgid "Requested URL"
msgstr "Begärd URL"

#: output/html/request.php:196
msgid "HTTP method"
msgstr "HTTP-metod"

#: output/html/request.php:195
msgid "Remote IP"
msgstr "Motpartens IP-adress"

#: output/html/admin.php:75
msgid "Global Variable"
msgstr "Global variabel"

#: output/html/admin.php:71
msgid "Globals"
msgstr "Globala variabler"

#: output/html/headers.php:119
msgid "Response Headers"
msgstr "Headers i svar"

#: output/html/headers.php:118
msgid "Request Headers"
msgstr "Headers i förfrågan"

#: output/html/headers.php:102
msgid "Note that header names are not case-sensitive."
msgstr "Observera att header-namn inte är skiftlägeskänsliga."

#: output/html/headers.php:69
msgid "Response Header Name"
msgstr "Header-namn i svar"

#: output/html/headers.php:54
msgid "Request Header Name"
msgstr "Header-namn i förfrågan"

#: output/html/timing.php:61
msgid "Stopped"
msgstr "Stoppat"

#: output/html/timing.php:60
msgid "Started"
msgstr "Startat"

#. translators: %s: Number of database queries
#: output/html/db_queries.php:290
msgctxt "Query count"
msgid "Total: %s"
msgid_plural "Total: %s"
msgstr[0] "Totalt: %s"
msgstr[1] "Totalt: %s"

#: output/html/overview.php:84
msgid "Unknown HTTP Response Code"
msgstr "Okänd HTTP-svarskod"

#: output/html/headers.php:34 output/html/request.php:191
msgid "Request Data"
msgstr "Data i förfrågan"

#. translators: %s: Default value for a PHP constant
#: dispatchers/Html.php:707
msgid "Default value: %s"
msgstr "Standardvärde: %s"

#: output/html/theme.php:181
msgid "Not Loaded"
msgstr "Ej laddat"

#. translators: 1: Query Monitor, 2: Required PHP version number, 3: Current
#. PHP version number, 4: URL of PHP update help page
#: classes/PHP.php:32
msgid "The %1$s plugin requires PHP version %2$s or higher. This site is running PHP version %3$s. <a href=\"%4$s\">Learn about updating PHP</a>."
msgstr "Tillägget %1$s kräver PHP-version %2$s eller senare. Denna webbplats kör PHP-version %3$s. <a href=\"%4$s\">Läs mer om hur man uppdaterar PHP</a>."

#. translators: %s: Total number of enqueued scripts
#: output/html/assets_scripts.php:37
msgctxt "Enqueued scripts"
msgid "Scripts (%s)"
msgstr "Skript (%s)"

#. translators: %s: Total number of enqueued styles
#: output/html/assets_styles.php:37
msgctxt "Enqueued styles"
msgid "Styles (%s)"
msgstr "Stilar (%s)"

#: output/html/overview.php:331
msgid "Object cache statistics are not available"
msgstr "Statistik för objekts-cache är inte tillgänglig"

#: output/Html.php:393
msgid "Toggle more information"
msgstr "Växla läge för att visa mer information"

#: output/Html.php:380
msgid "Sort data by this column"
msgstr "Sortera data efter denna kolumn"

#: output/Html.php:177 output/html/hooks.php:59
msgid "Callback"
msgstr "Återanrop"

#. translators: %s: Panel name
#: output/Html.php:167
msgid "%s: Related Hooks with Filters or Actions Attached"
msgstr "%s: Relaterade åtgärds-hookar med filter eller åtgärder bifogade"

#: output/Html.php:147 output/html/hooks.php:62 output/html/hooks.php:160
msgid "Filter"
msgstr "Filtrera"

#: output/Html.php:146
msgid "Related Hooks with Filters Attached"
msgstr "Relevanta hooks med tillkopplade filter"

#: output/Html.php:142
msgid "Related Hooks with Actions Attached"
msgstr "Relevanta hooks med tillkopplade åtgärder"

#: output/html/languages.php:95
msgid "Translation File"
msgstr "Översättningsfil"

#. translators: %s: Name of cache driver
#: output/html/overview.php:349
msgid "Opcode cache in use: %s"
msgstr "Opcode-cache som används: %s"

#: output/html/overview.php:212
msgctxt "database queries"
msgid "Total"
msgstr "Totalt"

#: output/html/block_editor.php:206
msgid "None (Classic block)"
msgstr "Ingen (klassiskt block)"

#: output/html/block_editor.php:70
msgid "Render Time"
msgstr "Tidsåtgång för rendering"

#: output/html/theme.php:205
msgid "Twig Template Files"
msgstr "Twig-mallfiler"

#: dispatchers/Html.php:491
msgid "Toggle panel position"
msgstr "Växla panelens position"

#. translators: %1$s: Erroneous post type name, %2$s: WordPress attachment post
#. type name
#: output/html/block_editor.php:172
msgid "Referenced media is of type %1$s instead of %2$s."
msgstr "De refererade mediafilerna är av typen %1$s och inte %2$s."

#: output/html/block_editor.php:164
msgid "Referenced media does not exist."
msgstr "De refererade mediafilerna saknas."

#. translators: %s: Total number of content blocks used
#: output/html/block_editor.php:103
msgctxt "Content blocks used"
msgid "Total: %s"
msgid_plural "Total: %s"
msgstr[0] "Totalt: %s"
msgstr[1] "Totalt: %s"

#: classes/QueryMonitor.php:61
msgid "Add-ons"
msgstr "Utökningar"

#. translators: 1: Name of the PHP directive, 2: Value of the PHP directive
#: output/html/overview.php:171
msgid "No memory limit. The %1$s PHP configuration directive is set to %2$s."
msgstr "Det finns ingen gräns för minnesanvändningen. Konfigurationsregeln %1$s för PHP är satt till %2$s."

#. translators: 1: Name of the PHP directive, 2: Value of the PHP directive
#: output/html/overview.php:129
msgid "No execution time limit. The %1$s PHP configuration directive is set to %2$s."
msgstr "Det finns ingen gräns för körtiden. Konfigurationsregeln %1$s för PHP är satt till %2$s."

#: classes/Collector_Assets.php:469
msgid "Insecure content"
msgstr "Osäkert innehåll"

#. translators: %1$s: Erroneous post type name, %2$s: WordPress block post type
#. name
#: output/html/block_editor.php:141
msgid "Referenced post is of type %1$s instead of %2$s."
msgstr "Det refererade inlägget är av typen %1$s och inte %2$s."

#: output/html/block_editor.php:133
msgid "Referenced block does not exist."
msgstr "Det refererade blocket finns inte."

#: output/html/block_editor.php:73
msgid "Inner HTML"
msgstr "Inre HTML"

#: output/html/block_editor.php:67
msgid "Render Callback"
msgstr "Återanrop för rendering"

#: output/html/block_editor.php:61
msgid "Attributes"
msgstr "Egenskaper"

#: output/html/block_editor.php:60
msgid "Block Name"
msgstr "Blocknamn"

#: output/html/block_editor.php:47
msgid "This post contains no blocks."
msgstr "Detta inlägg innehåller inga block."

#. translators: %s: The number of times that a template part file was included
#. in the page
#: output/html/theme.php:166
msgctxt "template parts"
msgid "Included %s time"
msgid_plural "Included %s times"
msgstr[0] "Inkluderad %s gång"
msgstr[1] "Inkluderad %s gånger"

#: output/html/block_editor.php:30 output/html/block_editor.php:339
msgid "Blocks"
msgstr "Block"

#: classes/QueryMonitor.php:217
msgctxt "Human readable label for the user capability required to view Query Monitor."
msgid "View Query Monitor"
msgstr "Visa Query Monitor"

#: output/html/transients.php:129
msgid "No transients set."
msgstr "Inga transientvariabler är satta."

#: output/html/overview.php:62
msgid "PHP errors were triggered during an Ajax request. See your browser developer console for details."
msgstr "PHP-fel inträffade i samband med en begäran via Ajax. Mer information finns i utvecklarkonsolen i din webbläsare."

#: output/html/logger.php:221 output/raw/logger.php:21
msgid "Logs"
msgstr "Loggar"

#: output/html/http.php:367
msgid "No HTTP API calls."
msgstr "Inga anrop till HTTP-API."

#. translators: %s: Number of HTTP API requests
#: output/html/http.php:355
msgctxt "HTTP API calls"
msgid "Total: %s"
msgid_plural "Total: %s"
msgstr[0] "Totalt: %s"
msgstr[1] "Totalt: %s"

#: output/html/environment.php:158
msgid "Client Version"
msgstr "Klientversion"

#: output/html/environment.php:157
msgid "Extension"
msgstr "Utökning"

#: output/html/environment.php:156
msgid "Server Version"
msgstr "Serverversion"

#. translators: %s: Number of user capability checks
#: output/html/caps.php:198
msgctxt "User capability checks"
msgid "Total: %s"
msgid_plural "Total: %s"
msgstr[0] "Totalt: %s"
msgstr[1] "Totalt: %s"

#: output/html/assets.php:78
msgid "Source"
msgstr "Källa"

#. translators: %s: Total number of enqueued styles
#: output/html/assets_styles.php:34
msgctxt "Enqueued styles"
msgid "Total: %s"
msgstr "Totalt: %s"

#. translators: %s: Total number of enqueued scripts
#: output/html/assets_scripts.php:34
msgctxt "Enqueued scripts"
msgid "Total: %s"
msgstr "Totalt: %s"

#: output/Html.php:374
msgid "Sequence"
msgstr "Sekvens"

#. translators: %s: Current value for a PHP constant
#: dispatchers/Html.php:721
msgid "Current value: %s"
msgstr "Nuvarande värde: %s"

#. translators: %s: Name of the config file
#: dispatchers/Html.php:687
msgid "The following PHP constants can be defined in your %s file in order to control the behavior of Query Monitor:"
msgstr "Följande PHP-konstanter kan definieras i filen %s för att styra hur Query Monitor uppträder:"

#: dispatchers/Html.php:683
msgid "Configuration"
msgstr "Konfiguration"

#: dispatchers/Html.php:660
msgid "In the Hooks & Actions panel, show every hook that has an action or filter attached (instead of every action hook that fired during the request)."
msgstr "I panelen ”Åtgärds-hookar och åtgärder” visa varje åtgärds-hook som har en åtgärd eller ett filter kopplat (i stället för varje åtgärds-hook som körts för denna begäran)."

#: dispatchers/Html.php:656
msgid "Don't specify jQuery as a dependency of Query Monitor. If jQuery isn't enqueued then Query Monitor will still operate, but with some reduced functionality."
msgstr "Ange inte jQuery som ett beroende för Query Monitor. Om jQuery inte ställs i kö kommer Query Monitor fortfarande att fungera, men med nedsatt funktionalitet."

#: dispatchers/Html.php:648
msgid "Hide WordPress core on the Hooks & Actions panel."
msgstr "Dölj WordPress-kärnan i panelen ”Åtgärds-hookar och åtgärder”."

#: dispatchers/Html.php:644
msgid "Enable the Capability Checks panel."
msgstr "Aktivera panelen för behörighetskontroller."

#: dispatchers/Html.php:640
msgid "Disable the handling of PHP errors."
msgstr "Inaktivera hantering av PHP-fel."

#: dispatchers/Html.php:636
msgid "Disable Query Monitor entirely."
msgstr "Inaktivera Query Monitor helt."

#: dispatchers/Html.php:632
msgid "If an individual database query takes longer than this time to execute, it's considered \"slow\" and triggers a warning."
msgstr "Om en enskild databasfråga pågår längre tid än detta, betraktas den som ”långsam”, vilket aktiverar en varning."

#: dispatchers/Html.php:567
msgid "Authentication cookie is set"
msgstr "Cookien för autentisering har sparats"

#: dispatchers/Html.php:560
msgid "You can set an authentication cookie which allows you to view Query Monitor output when you&rsquo;re not logged in, or when you&rsquo;re logged in as a different user."
msgstr "Du kan ange en autentiserings-cookie som låter dig se utdata från Query Monitor när du inte är inloggad eller när du är inloggad som en annan användare."

#: dispatchers/Html.php:498
msgid "Query Monitor Menu"
msgstr "Meny för Query Monitor"

#: output/html/logger.php:31
msgid "Logger"
msgstr "Loggfunktion"

#. Description of the plugin
#: query-monitor.php
msgid "The developer tools panel for WordPress."
msgstr "Panelen med utvecklarverktyg för WordPress."

#. translators: %s: Number of function timing results that are available
#: output/html/timing.php:214
msgid "Timings (%s)"
msgstr "Tid (%s)"

#. translators: %s: Approximate memory used in kilobytes
#: output/html/timing.php:110 output/html/timing.php:142
msgid "~%s kB"
msgstr "~%s kB"

#: output/html/timing.php:63
msgid "Memory"
msgstr "Minne"

#: output/html/timing.php:59
msgid "Tracked Function"
msgstr "Spårad funktion"

#: output/html/theme.php:275
msgid "Template"
msgstr "Mall"

#: output/html/request.php:163
msgid "Current User"
msgstr "Nuvarande användare"

#. translators: used between list items, there is a space after the comma
#: output/html/php_errors.php:296
msgid ", "
msgstr ", "

#. translators: %s: List of PHP error types
#. translators: %s: Number of errors
#: output/html/php_errors.php:293 output/html/php_errors.php:343
msgid "PHP Errors (%s)"
msgstr "PHP-fel (%s)"

#. translators: %s: Number of PHP warnings
#: output/html/php_errors.php:246
msgctxt "PHP error level"
msgid "%s Warning"
msgid_plural "%s Warnings"
msgstr[0] "%s varning"
msgstr[1] "%s varningar"

#. translators: %s: Number of PHP notices
#: output/html/php_errors.php:244
msgctxt "PHP error level"
msgid "%s Notice"
msgid_plural "%s Notices"
msgstr[0] "%s påpekande"
msgstr[1] "%s påpekanden"

#. translators: %s: Number of strict PHP errors
#: output/html/php_errors.php:242
msgctxt "PHP error level"
msgid "%s Strict"
msgid_plural "%s Stricts"
msgstr[0] "%s strikt"
msgstr[1] "%s strikta"

#. translators: %s: Number of deprecated PHP errors
#: output/html/php_errors.php:240
msgctxt "PHP error level"
msgid "%s Deprecated"
msgid_plural "%s Deprecated"
msgstr[0] "%s föråldrat"
msgstr[1] "%s föråldrade"

#: output/html/doing_it_wrong.php:69 output/html/logger.php:85
#: output/html/php_errors.php:79
msgid "Message"
msgstr "Meddelande"

#: output/html/logger.php:83 output/html/php_errors.php:77
msgid "Level"
msgstr "Nivå"

#: output/html/overview.php:61
msgid "A JavaScript problem on the page is preventing Query Monitor from working correctly. jQuery may have been blocked from loading."
msgstr "Ett JavaScript-problem på sidan hindrar Query Monitor från att fungera korrekt. jQuery kanske inte kunde laddas."

#: output/html/languages.php:96
msgid "Size"
msgstr "Storlek"

#: output/html/environment.php:276 output/html/http.php:277
msgid "IP Address"
msgstr "IP-adress"

#: output/html/http.php:276
msgid "Response Content Type"
msgstr "Svarets typ av innehåll"

#: output/html/http.php:81
msgid "Response Size"
msgstr "Svarets storlek"

#: output/html/http.php:262
msgid "Transfer Start Time (TTFB)"
msgstr "Tid till överföring startade (TTFB)"

#: output/html/http.php:261
msgid "Connection Time"
msgstr "Anslutningstid"

#: output/html/http.php:260
msgid "DNS Resolution Time"
msgstr "Tid för DNS-uppslagning"

#: output/html/http.php:69
msgid "URL"
msgstr "URL"

#: output/html/http.php:67
msgid "Method"
msgstr "Metod"

#: output/Html.php:176 output/html/hooks.php:72
msgid "Priority"
msgstr "Prioritet"

#. translators: OS stands for Operating System
#: output/html/environment.php:279
msgid "OS"
msgstr "OS"

#: output/html/db_queries.php:78
msgid "No database queries were logged."
msgstr "Inga databasfrågor loggades."

#: output/html/conditionals.php:57
msgid "False Conditionals"
msgstr "Falska villkor"

#: output/html/conditionals.php:44
msgid "True Conditionals"
msgstr "Sanna villkor"

#. translators: %s: Configuration file name.
#: output/html/caps.php:47
msgid "For performance reasons, this panel is not enabled by default. To enable it, add the following code to your %s file:"
msgstr "Av prestandaskäl är denna panel inte aktiverad som standard. För att aktivera den lägger du till följande kod i filen %s:"

#: output/html/assets.php:69
msgid "Handle"
msgstr "Handle"

#: output/html/assets.php:76 output/html/environment.php:160
#: output/html/environment.php:277
msgid "Host"
msgstr "Server"

#: output/html/assets.php:61 output/html/assets.php:153
msgid "Other"
msgstr "Annat"

#: output/html/assets.php:52
msgid "Missing Dependencies"
msgstr "Nödvändiga delar som saknas"

#: output/html/admin.php:105
msgid "Class:"
msgstr "Klass:"

#: output/html/admin.php:102
msgid "List Table"
msgstr "Tabellista"

#: dispatchers/Html.php:493
msgid "Close Panel"
msgstr "Stäng panelen"

#: classes/QueryMonitor.php:60 dispatchers/Html.php:478
#: dispatchers/Html.php:489 dispatchers/Html.php:554
msgid "Settings"
msgstr "Inställningar"

#: collectors/timing.php:143
msgid "Timer not stopped"
msgstr "Timern har inte stoppats"

#: collectors/timing.php:81 collectors/timing.php:101
msgid "Timer not started"
msgstr "Timern har inte startats"

#: output/html/timing.php:30
msgid "Timing"
msgstr "Tider"

#. translators: %d: Multisite network ID
#: collectors/request.php:179
msgid "Current Network: #%d"
msgstr "Nuvarande nätverk: #%d"

#: output/html/php_errors.php:63
msgctxt "Silenced PHP error level"
msgid "Deprecated (Silenced)"
msgstr "Föråldrat (tystat)"

#: output/html/php_errors.php:62
msgctxt "Silenced PHP error level"
msgid "Strict (Silenced)"
msgstr "Strikt (tystat)"

#: output/html/php_errors.php:61
msgctxt "Silenced PHP error level"
msgid "Notice (Silenced)"
msgstr "Påpekande (tystat)"

#: output/html/php_errors.php:60
msgctxt "Silenced PHP error level"
msgid "Warning (Silenced)"
msgstr "Varning (tystad)"

#. translators: A non-blocking HTTP API request
#: output/html/http.php:57 output/html/http.php:119
msgid "Non-blocking"
msgstr "Icke-blockerande"

#. translators: %s: Timing lap number
#: classes/Timer.php:91
msgid "Lap %s"
msgstr "Varv %s"

#. translators: No user
#: collectors/request.php:156
msgctxt "user"
msgid "None"
msgstr "Ingen"

#. translators: %d: User ID
#: collectors/request.php:151
msgid "Current User: #%d"
msgstr "Nuvarande användare: #%d"

#: output/html/db_queries.php:218
msgid "Non-SELECT"
msgstr "Inte SELECT"

#: output/html/db_queries.php:97
msgid "Error Message"
msgstr "Felmeddelande"

#: output/html/db_queries.php:98
msgid "Error Code"
msgstr "Felkod"

#: output/html/caps.php:209
msgid "No capability checks were recorded."
msgstr "Inga behörighetskontroller har registrerats."

#: output/html/caps.php:91
msgid "Result"
msgstr "Resultat"

#: output/html/caps.php:87 output/html/environment.php:81
#: output/html/environment.php:159
msgid "User"
msgstr "Användare"

#: output/html/caps.php:79
msgid "Capability Check"
msgstr "Behörighetskontroll"

#: output/html/caps.php:30
msgid "Capability Checks"
msgstr "Behörighetskontroller"

#. translators: %s: Number of transient values that were updated
#: output/html/transients.php:148
msgid "Transient Updates (%s)"
msgstr "Uppdateringar av transientvariabler (%s)"

#: output/html/transients.php:51
msgctxt "size of transient value"
msgid "Size"
msgstr "Storlek"

#: output/html/transients.php:48
msgctxt "transient type"
msgid "Type"
msgstr "Typ"

#: output/html/transients.php:46
msgid "Updated Transient"
msgstr "Uppdaterad transientvariabel"

#: output/html/transients.php:146
msgid "Transient Updates"
msgstr "Uppdateringar av transientvariabler"

#: output/html/request.php:107
msgid "View Main Query"
msgstr "Visa huvudfrågan"

#. translators: %s: Number of calls to the HTTP API
#: output/html/http.php:406
msgid "HTTP API Calls (%s)"
msgstr "Anrop till HTTP-API (%s)"

#: output/html/http.php:72
msgid "Status"
msgstr "Status"

#: output/html/http.php:31 output/html/http.php:404
#: output/html/overview.php:229 output/raw/http.php:21
msgid "HTTP API Calls"
msgstr "Anrop till HTTP-API"

#: output/html/environment.php:126
msgid "Extensions"
msgstr "Utökningar"

#: output/html/environment.php:111
msgid "Error Reporting"
msgstr "Felrapportering"

#: output/html/hooks.php:33
msgid "Hooks & Actions"
msgstr "Åtgärds-hookar och åtgärder"

#: output/html/db_queries.php:237 output/html/db_queries.php:423
msgid "Main Query"
msgstr "Huvudfråga"

#. translators: 1: Cache hit rate percentage, 2: number of cache hits, 3:
#. number of cache misses
#: output/html/overview.php:273
msgid "%1$s%% hit rate (%2$s hits, %3$s misses)"
msgstr "%1$s %% andel cacheträffar (%2$s träffar, %3$s missar)"

#: output/html/theme.php:133
msgid "Template Hierarchy"
msgstr "Mallhierarki"

#: output/html/php_errors.php:57
msgctxt "Suppressed PHP error level"
msgid "Deprecated (Suppressed)"
msgstr "Föråldrat (undertryckt)"

#: output/html/php_errors.php:56
msgctxt "Suppressed PHP error level"
msgid "Strict (Suppressed)"
msgstr "Strikt (undertryckt)"

#: output/html/php_errors.php:55
msgctxt "Suppressed PHP error level"
msgid "Notice (Suppressed)"
msgstr "Påpekande (undertryckt)"

#: output/html/php_errors.php:54
msgctxt "Suppressed PHP error level"
msgid "Warning (Suppressed)"
msgstr "Varning (undertryckt)"

#: output/html/assets.php:68
msgid "Position"
msgstr "Position"

#: output/html/assets.php:55
msgid "Footer"
msgstr "Sidfot"

#: output/html/assets.php:54
msgid "Header"
msgstr "Sidhuvud"

#: output/html/assets.php:51
msgid "Missing"
msgstr "Saknas"

#: output/html/admin.php:54 output/html/admin.php:76 output/html/headers.php:85
msgid "Value"
msgstr "Värde"

#: output/html/admin.php:53
msgid "Property"
msgstr "Egenskap"

#. translators: 1: Post type name, 2: Post ID
#: collectors/request.php:251
msgid "Single %1$s: #%2$d"
msgstr "Enskild/enskilt %1$s: #%2$d"

#. translators: %d: Multisite site ID
#: collectors/request.php:168
msgid "Current Site: #%d"
msgstr "Nuvarande webbplats: #%d"

#: output/html/php_errors.php:51
msgctxt "PHP error level"
msgid "Deprecated"
msgstr "Föråldrat"

#: output/html/php_errors.php:50
msgctxt "PHP error level"
msgid "Strict"
msgstr "Strikt"

#: output/html/php_errors.php:49
msgctxt "PHP error level"
msgid "Notice"
msgstr "Påpekande"

#: output/html/php_errors.php:48
msgctxt "PHP error level"
msgid "Warning"
msgstr "Varning"

#. translators: An HTTP API request was not executed. %s: Hook name
#: output/html/http.php:103
msgid "Request not executed due to a filter on %s"
msgstr "Förfrågan utfördes inte på grund av ett filter mot %s"

#. translators: Undefined PHP constant
#: classes/Collector.php:122
msgid "undefined"
msgstr "odefinierad"

#: output/html/theme.php:140
msgid "Template Parts"
msgstr "Malldelar"

#: output/html/overview.php:261 output/raw/cache.php:21
msgid "Object Cache"
msgstr "Objekts-cache"

#. translators: An HTTP API request has disabled certificate verification. 1:
#. Relevant argument name
#: output/html/http.php:145
msgid "Certificate verification disabled (%s)"
msgstr "Certifikatverifiering är inaktiverad (%s)"

#. translators: %s: Number of duplicate database queries
#: output/html/db_dupes.php:154
msgid "Duplicate Queries (%s)"
msgstr "Duplicerade frågor (%s)"

#. translators: %s: Number of calls to a PHP function
#: output/html/db_dupes.php:65
msgid "%s call"
msgid_plural "%s calls"
msgstr[0] "%s anrop"
msgstr[1] "%s anrop"

#: output/html/db_dupes.php:57
msgid "Potential Troublemakers"
msgstr "Potentiella problemfaktorer"

#: output/html/db_dupes.php:55
msgid "Components"
msgstr "Komponenter"

#: output/html/db_dupes.php:53
msgid "Callers"
msgstr "Källor för anrop"

#: output/html/db_dupes.php:31
msgid "Duplicate Queries"
msgstr "Duplicerade frågor"

#. translators: %s: Plugin name
#: classes/Component_MU_Plugin.php:12 classes/Component_MU_Vendor.php:12
msgid "MU Plugin: %s"
msgstr "MU tillägg: %s"

#: output/html/php_errors.php:160
msgid "Unknown location"
msgstr "Okänd plats"

#: output/html/db_callers.php:111 output/html/languages.php:137
#: output/html/overview.php:221 output/html/overview.php:253
#: output/html/theme.php:177
msgid "None"
msgstr "Inga"

#: output/html/request.php:80
msgid "All Matching Rewrite Rules"
msgstr "Alla matchande omskrivningsregler"

#: output/html/languages.php:151
msgid "Not Found"
msgstr "Hittades inte"

#: output/html/languages.php:92
msgid "Text Domain"
msgstr "Textdomän"

#: output/html/environment.php:274
msgid "Software"
msgstr "Programvara"

#: output/html/environment.php:150 output/html/environment.php:161
msgid "Database"
msgstr "Databas"

#. translators: 1: Name of PHP constant, 2: Value of PHP constant
#: output/html/db_queries.php:73
msgid "No database queries were logged because the %1$s constant is set to %2$s."
msgstr "Inga databasfrågor loggades eftersom konstanten %1$s har värdet %2$s."

#: output/html/languages.php:30
msgid "Languages"
msgstr "Språk"

#. Author of the plugin
#: query-monitor.php
msgid "John Blackbourn"
msgstr "John Blackbourn"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: query-monitor.php
msgid "https://querymonitor.com/"
msgstr "https://querymonitor.com/"

#. translators: %s: Symlink file location
#: classes/QueryMonitor.php:185
msgid "The symlink at %s is no longer pointing to the correct location. Please remove the symlink, then deactivate and reactivate Query Monitor."
msgstr "Den symboliska länken vid %s pekar inte längre till rätt plats. Ta först bort den symboliska länken och inaktivera och aktivera sedan åter Query Monitor."

#: output/html/transients.php:50
msgid "Expiration"
msgstr "Giltighetstid"

#: output/Html.php:175 output/html/hooks.php:63 output/html/languages.php:93
msgid "Type"
msgstr "Typ"

#. translators: %s: Template file name
#: output/html/theme.php:260
msgid "Template: %s"
msgstr "Mall: %s"

#: output/html/theme.php:219
msgid "Body Classes"
msgstr "Klasser inom body"

#: output/html/theme.php:113
msgid "Template File"
msgstr "Mallfil"

#. translators: %s: Number of additional query variables
#: output/html/request.php:225
msgid "Request (+%s)"
msgstr "Begäran (+%s)"

#: output/html/request.php:149
msgid "Queried Object"
msgstr "Efterfrågat objekt"

#: output/html/multisite.php:27 output/html/request.php:175
msgid "Multisite"
msgstr "Multisite"

#: output/html/request.php:100
msgid "Query Vars"
msgstr "Variabler för databasfrågan"

#: output/html/request.php:52
msgid "Query String"
msgstr "Sträng för databasfrågan"

#: output/html/request.php:51
msgid "Matched Query"
msgstr "Matchad databasfråga"

#: output/html/request.php:50
msgid "Matched Rule"
msgstr "Matchad regel"

#: output/html/php_errors.php:80
msgid "Location"
msgstr "Plats"

#: output/html/db_dupes.php:51 output/html/php_errors.php:81
msgid "Count"
msgstr "Antal"

#. translators: %s: Memory used in kilobytes
#: output/html/http.php:226 output/html/http.php:232
#: output/html/languages.php:145 output/html/languages.php:168
msgid "%s kB"
msgstr "%s kB"

#: output/html/overview.php:139
msgid "Peak Memory Usage"
msgstr "Maximal minnesanvändning"

#: output/html/overview.php:100
msgid "Page Generation Time"
msgstr "Tid för generering av sidan"

#. translators: 1: Percentage of time limit used, 2: Time limit in seconds
#: output/headers/overview.php:32 output/html/overview.php:119
msgid "%1$s%% of %2$ss limit"
msgstr "%1$s%% av gränsen på %2$s s"

#: output/html/http.php:82
msgid "Timeout"
msgstr "Timeout"

#: output/html/request.php:148
msgid "Response"
msgstr "Svar"

#. translators: %s: Error message text
#: output/html/block_editor.php:275 output/html/hooks.php:208
msgid "Error: %s"
msgstr "Fel: %s"

#. translators: %s: Action name
#: output/html/hooks.php:152
msgid "Warning: The %s action is extremely resource intensive. Try to avoid using it."
msgstr "Varning: Åtgärden %s är kräver stora resurser. Undvik att använda den."

#: output/Html.php:143 output/html/hooks.php:55 output/html/hooks.php:61
#: output/html/hooks.php:160
msgid "Action"
msgstr "Åtgärd"

#: output/Html.php:174 output/html/hooks.php:69
msgid "Hook"
msgstr "Åtgärds-hook"

#: output/html/environment.php:271
msgid "Server"
msgstr "Server"

#: classes/QueryMonitor.php:62 output/html/environment.php:56
#: output/html/environment.php:230 output/html/environment.php:249
msgid "Help"
msgstr "Hjälp"

#. translators: %s: Number of slow database queries
#: output/html/db_queries.php:579
msgid "Slow Queries (%s)"
msgstr "Långsamma databasfrågor (%s)"

#. translators: %s: Number of database errors
#: output/html/db_queries.php:565
msgid "Database Errors (%s)"
msgstr "Databasfel (%s)"

#: output/html/db_queries.php:136 output/html/db_queries.php:265
msgid "Rows"
msgstr "Rader"

#: output/html/caps.php:93 output/html/db_callers.php:51
#: output/html/db_queries.php:95 output/html/db_queries.php:129
#: output/html/db_queries.php:243 output/html/doing_it_wrong.php:70
#: output/html/http.php:74 output/html/languages.php:94
#: output/html/logger.php:86 output/html/multisite.php:53
#: output/html/transients.php:52
msgid "Caller"
msgstr "Anrop från"

#. translators: %s: Database query time in seconds
#: output/html/db_queries.php:121
msgid "Slow Database Queries (above %ss)"
msgstr "Långsamma databasfrågor (över %s s)"

#: output/Html.php:178 output/html/caps.php:96 output/html/db_components.php:51
#: output/html/db_queries.php:96 output/html/db_queries.php:132
#: output/html/db_queries.php:254 output/html/doing_it_wrong.php:71
#: output/html/hooks.php:76 output/html/http.php:79 output/html/logger.php:89
#: output/html/multisite.php:56 output/html/php_errors.php:84
#: output/html/timing.php:64 output/html/transients.php:53
msgid "Component"
msgstr "Komponent"

#: dispatchers/WP_Die.php:127
msgid "Call stack:"
msgstr "Anropsstack:"

#: output/html/db_dupes.php:50 output/html/db_queries.php:94
#: output/html/db_queries.php:128 output/html/db_queries.php:230
msgid "Query"
msgstr "Databasfråga"

#: output/html/db_queries.php:90
msgid "Database Errors"
msgstr "Databasfel"

#: output/html/db_callers.php:60 output/html/db_components.php:60
#: output/html/db_dupes.php:52 output/html/db_queries.php:139
#: output/html/db_queries.php:270 output/html/http.php:83
#: output/html/timing.php:62
msgid "Time"
msgstr "Tid"

#. translators: %s: Name of missing script or style dependency
#: output/html/assets.php:144
msgid "%s (missing)"
msgstr "%s (saknas)"

#: output/html/request.php:65 output/html/request.php:141
#: output/html/request.php:160 output/html/request.php:171
#: output/html/transients.php:77
msgid "none"
msgstr "inget"

#: output/html/assets.php:85 output/html/environment.php:62
#: output/html/environment.php:220 output/html/environment.php:275
msgid "Version"
msgstr "Version"

#: output/html/assets.php:83
msgid "Dependents"
msgstr "Beroende"

#: output/html/assets.php:80
msgid "Dependencies"
msgstr "Beroenden"

#: output/html/assets_styles.php:25 output/html/assets_styles.php:35
msgid "Styles"
msgstr "Stilar"

#: output/html/assets_scripts.php:25 output/html/assets_scripts.php:35
msgid "Scripts"
msgstr "Skript"

#: output/html/admin.php:112
msgid "Column Action:"
msgstr "Kolumnåtgärd:"

#: output/html/admin.php:109
msgid "Column Filters:"
msgstr "Kolumnfilter:"

#: output/Html.php:316
msgctxt "\"All\" option for filters"
msgid "All"
msgstr "Alla"

#: dispatchers/Html.php:549
msgid "Clear authentication cookie"
msgstr "Rensa cookie för autentisering"

#: dispatchers/Html.php:550
msgid "Set authentication cookie"
msgstr "Spara cookie för autentisering"

#: dispatchers/Html.php:558
msgid "Authentication"
msgstr "Autentisering"

#: dispatchers/Html.php:257
msgid "PHP Errors in Ajax Response"
msgstr "PHP-fel i Ajax-svar"

#. Plugin Name of the plugin
#: query-monitor.php classes/QueryMonitor.php:199 classes/QueryMonitor.php:232
#: dispatchers/Html.php:147 dispatchers/Html.php:448 dispatchers/Html.php:841
#: dispatchers/WP_Die.php:132
msgid "Query Monitor"
msgstr "Query Monitor"

#: output/html/transients.php:30 output/raw/transients.php:21
msgid "Transients"
msgstr "Transientvariabler"

#: collectors/request.php:288
msgid "Unknown queried object"
msgstr "Okänt efterfrågat objekt"

#. translators: %s: Post type name
#: collectors/request.php:281
msgid "Post type archive: %s"
msgstr "Arkiv för inläggstyp: %s"

#. translators: %s: Taxonomy term name
#: collectors/request.php:271
msgid "Term archive: %s"
msgstr "Termarkiv: %s"

#. translators: %s: Author name
#: collectors/request.php:261
msgid "Author archive: %s"
msgstr "Författararkiv: %s"

#: output/html/request.php:30 output/html/request.php:49
#: output/html/request.php:223
msgid "Request"
msgstr "Förfrågan"

#: output/html/php_errors.php:32 output/html/php_errors.php:304
msgid "PHP Errors"
msgstr "PHP-fel"

#: dispatchers/Html.php:455 dispatchers/Html.php:504
#: output/html/overview.php:30
msgid "Overview"
msgstr "Översikt"

#: output/html/http.php:54
msgid "Error"
msgstr "Fel"

#. translators: An HTTP API request timed out
#: output/html/http.php:109
msgid "Request timed out"
msgstr "Begäran tog för lång tid"

#: output/html/environment.php:30
msgid "Environment"
msgstr "Miljö"

#: output/html/db_queries.php:38 output/html/db_queries.php:553
#: output/html/overview.php:183 output/raw/db_queries.php:26
msgid "Database Queries"
msgstr "Databasfrågor"

#: output/html/db_components.php:30 output/html/db_components.php:127
msgid "Queries by Component"
msgstr "Databasfrågor per komponent"

#: output/html/db_callers.php:30 output/html/db_callers.php:131
msgid "Queries by Caller"
msgstr "Databasfrågor per anropare"

#: output/html/conditionals.php:31 output/html/conditionals.php:107
#: output/raw/conditionals.php:21
msgid "Conditionals"
msgstr "Villkorat"

#: output/html/admin.php:30
msgid "Admin Screen"
msgstr "Admin-skärmen"

#. translators: A closure is an anonymous PHP function. 1: Line number, 2: File
#. name
#: classes/Util.php:414 output/Html.php:508
msgid "Closure on line %1$d of %2$s"
msgstr "”Closure” på rad %1$d av %2$s"

#: classes/Component_Unknown.php:10 output/html/db_queries.php:346
#: output/html/db_queries.php:442 output/html/environment.php:70
#: output/html/environment.php:77 output/html/environment.php:85
#: output/html/environment.php:171 output/html/environment.php:293
#: output/html/overview.php:143 output/html/php_errors.php:186
#: output/html/request.php:153 output/html/theme.php:128
#: output/html/theme.php:254
msgid "Unknown"
msgstr "Okänt"

#: classes/Component_Template.php:10 output/html/theme.php:64
msgid "Parent Theme"
msgstr "Huvudtema"

#: classes/Component_Stylesheet.php:14 output/html/theme.php:31
#: output/html/theme.php:48
msgid "Theme"
msgstr "Tema"

#: classes/Component_Stylesheet.php:11
msgid "Child Theme"
msgstr "Barntema"

#. translators: %s: Plugin name
#: classes/Component_VIP_Plugin.php:12
msgid "VIP Plugin: %s"
msgstr "VIP-tillägg: %s"

#. translators: %s: Plugin name
#: classes/Component_Plugin.php:12
msgid "Plugin: %s"
msgstr "Tillägg: %s"