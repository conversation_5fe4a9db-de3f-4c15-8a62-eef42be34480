# Translation of Plugins - Asgaros Forum - Stable (latest release) in Swedish
# This file is distributed under the same license as the Plugins - Asgaros Forum - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-01-21 18:33:29+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: sv_SE\n"
"Project-Id-Version: Plugins - Asgaros Forum - Stable (latest release)\n"

#: includes/forum-reports.php:227
msgid "Posted in %1$s by %2$s"
msgstr "Publicerat i %1$s av %2$s"

#: includes/forum-pagination.php:102
msgid "Page %1$s of %2$s"
msgstr "Sida %1$s av %2$s"

#: includes/forum-activity.php:100
msgid "%1$s replied in %2$s."
msgstr "%1$s svarade i %2$s."

#: admin/views/options.php:485
msgid "Users can only delete own topics without replies"
msgstr "Användare kan endast ta bort egna ämnen utan svar"

#: includes/forum-content.php:160
msgid "The minimum time between new posts didn't pass yet."
msgstr "Den minsta tiden mellan nya inlägg har inte passerat ännu."

#: admin/views/options.php:477
msgid "Time limit for deleting topics (in seconds):"
msgstr "Tidsgräns för borttagning av ämnen (i sekunder):"

#: admin/views/options.php:460
msgid "Time limit for deleting posts (in seconds):"
msgstr "Tidsgräns för borttagning av inlägg (i sekunder):"

#: admin/views/options.php:446 admin/views/options.php:463
#: admin/views/options.php:480
msgid "(0 = No limit)"
msgstr "(0 = ingen begränsning)"

#: admin/views/options.php:443
msgid "Time limit for editing posts (in seconds):"
msgstr "Tidsgräns för redigering av inlägg (i sekunder):"

#: admin/views/options.php:429
msgid "(0 = No minimum time)"
msgstr "(0 = ingen minsta tid)"

#: admin/views/options.php:426
msgid "Minimum time between new posts (in seconds):"
msgstr "Minsta tid mellan nya inlägg (i sekunder):"

#: includes/forum.php:2406
msgid "Delete all forum posts and topics owned by this user."
msgstr "Ta bort alla foruminlägg och ämnen som ägs av denna användare."

#: includes/forum.php:300
msgid "Hello ###USERNAME###,<br><br>There is a new topic in a forum you are subscribed to on %s.<br><br>Forum and Topic: ###FORUM###, ###TITLE###<br><br>Author: ###AUTHOR###<br><br>Content: ###CONTENT### <br>Visit the topic:<br>###LINK###<br><br>You will not receive notifications of any replies to this topic unless you globally subscribe to all \"New Topics & Posts\", or subscribe to this topic.<br><br>If you don't wish to receive these notification emails, please unsubscribe in Forum Page > Forum Menu > Subscriptions. There you can edit your global subscription option and unsubscribe from individual forums and topics.<br><br>Please don't reply to this email."
msgstr "Hej ###USERNAME###,<br><br>Det finns ett nytt ämne i ett forum du prenumererar på %s.<br><br>Forum och ämne: ###FORUM###, ###TITLE###<br><br>Författare: ###AUTHOR###<br><br>Innehåll: ###CONTENT### <br>Besök ämnet:<br>###LINK###<br><br>Du kommer inte att få aviseringar om några svar på detta ämne om du inte globalt prenumererar på alla ”Nya ämnen och inlägg” eller prenumererar på detta ämne.<br><br>Om du inte önskar ta emot dessa e-postaviseringar, avsluta prenumeration på Forumsida > Forummeny > Prenumerationer. Där kan du redigera dina globala prenumerationsalternativ och avsluta prenumeration från enskilda forum och ämnen.<br><br>Svara inte på detta e-postmeddelande."

#: includes/forum.php:289
msgid "Hello ###USERNAME###,<br><br>There is a new reply in a forum topic you are subscribed to on %s.<br><br>Forum and Topic: ###FORUM###, ###TITLE###<br><br>Author: ###AUTHOR###<br><br>Reply: ###CONTENT### Visit the reply:<br>###LINK###<br><br>If you don't wish to receive notification emails, please unsubscribe in Forum Page > Forum Menu > Subscriptions. There you can edit your global subscription option and unsubscribe from individual forums and topics.<br><br>Please don't reply to this email."
msgstr "Hej ###USERNAME###,<br><br>Det finns ett nytt svar i ett forumämne som du prenumererar på %s.<br><br>Forum och ämne: ###FORUM###, ###TITLE###<br><br>Författare: ###AUTHOR###<br><br>Svar: ###CONTENT### Besök svaret:<br>###LINK###<br><br>Om du inte önskar att ta emot e-postaviseringar, avsluta prenumeration på Forumsida > Forummenyn > Prenumerationer. Där kan du redigera dina globala prenumerationsalternativ och avsluta prenumeration från enskilda forum och ämnen.<br><br>Svara inte på detta e-postmeddelande."

#: includes/forum-private.php:51
msgid "Private"
msgstr "Privat"

#: includes/forum-pagination.php:107
msgid "Next"
msgstr "Nästa"

#: includes/forum-pagination.php:99
msgid "Previous"
msgstr "Föregående"

#: admin/views/options.php:895
msgid "Show names of online users in statistics"
msgstr "Visa namn på onlineanvändare i statistiken"

#: admin/views/options.php:710
msgid "Gigabyte"
msgstr "Gigabyte"

#: admin/views/options.php:709
msgid "Megabyte"
msgstr "Megabyte"

#: admin/views/options.php:708
msgid "Kilobyte"
msgstr "Kilobyte"

#: includes/forum-activity.php:31
msgid "Activity in the last %s days."
msgstr "Aktivitet under de senaste %s dagarna."

#: includes/forum-reactions.php:202
msgid "%1$s, %2$s and %3$s other users have reacted to this post."
msgstr "%1$s, %2$s och %3$s andra användare har reagerat på detta inlägg."

#: includes/forum-reactions.php:200
msgid "%1$s, %2$s and %3$s have reacted to this post."
msgstr "%1$s, %2$s och %3$s har reagerat på detta inlägg."

#: includes/forum-reactions.php:198
msgid "%1$s and %2$s have reacted to this post."
msgstr "%1$s och %2$s har reagerat på detta inlägg."

#: includes/forum-reactions.php:196
msgid "%s has reacted to this post."
msgstr "%s har reagerat på detta inlägg."

#: admin/views/options.php:612
msgid "Hide site-admins in memberslist"
msgstr "Dölj webbplatsens administratörer i medlemslistan"

#: admin/views/options.php:256
msgid "If enabled, the names of users who have reacted to a post are shown."
msgstr "Om det är aktiverat visas namnen på användare som har reagerat på ett inlägg."

#: admin/views/options.php:255
msgid "Show usernames in reactions"
msgstr "Visa användarnamnen i reaktionerna"

#: admin/views/options.php:234
msgid "Actual"
msgstr "Faktisk"

#: admin/views/options.php:233
msgid "Relative"
msgstr "Relativ"

#: admin/views/options.php:229
msgid "Defines if activity-timestamps are shown in its relative or actual format."
msgstr "Definierar om aktivitetstidsstämplar visas i sitt relativa eller faktiska format."

#: admin/views/options.php:228
msgid "Format for activity-timestamps:"
msgstr "Format för aktivitetstidsstämpel:"

#: includes/forum-pagination.php:31
msgctxt "Last topic"
msgid "Last"
msgstr "Sista"

#: includes/forum.php:1959
msgid "Link Text Missing"
msgstr "Länktext saknas"

#: includes/forum.php:683
msgid "Please %s to create posts and topics."
msgstr "%s för att skapa inlägg och ämnen."

#: includes/forum.php:681
msgid "Please %1$s or %2$s to create posts and topics."
msgstr "%1$s eller %2$s för att skapa inlägg och ämnen."

#: admin/views/options.php:905
msgid "Shows when a user got last seen inside of his profile and in the members list. This information is only gathered and shown when the \"Who is Online\" functionality is enabled."
msgstr "Visar när en användare senast sågs inuti sin profil och i medlemslistan. Denna information samlas bara in och visas när funktionen ”Vem är online” är aktiverad."

#: admin/views/options.php:904
msgid "Show \"Last seen\""
msgstr "Visa ”Senast sedd”"

#: admin/views/options.php:414
msgid "Allows you to define a custom title-separator for the forum. This setting is useful when different title-separators are shown in parts of the title - which is a common problem when using other SEO plugins."
msgstr "Tillåter dig att definiera en anpassad rubrikavgränsare för forumet. Denna inställning är användbar när olika rubrikavgränsare visas i delar av rubriken – vilket är ett vanligt problem när du använder andra SEO-tillägg."

#: admin/views/options.php:413
msgid "Title Separator"
msgstr "Rubrikavgränsare"

#: admin/admin.php:45
msgid "URLs & SEO"
msgstr "URL:er och SEO"

#: admin/admin.php:624
msgid "Forum Posts"
msgstr "Foruminlägg"

#. translators: amount of replies for a certain topic
#: includes/forum.php:1074 widgets/widget-recent-topics.php:105
msgid "%s Reply"
msgid_plural "%s Replies"
msgstr[0] "%s svar"
msgstr[1] "%s svar"

#: includes/forum.php:284
msgid "New reply: ###TITLE###"
msgstr "Nytt svar: ###TITLE###"

#: admin/views/options.php:495
msgid "Users can close their own topics"
msgstr "Användare kan stänga sina egna ämnen"

#: admin/views/options.php:490
msgid "Users can open their own topics"
msgstr "Användare kan öppna sina egna ämnen"

#: admin/views/options.php:891
msgid "Show newest member"
msgstr "Visa den nyaste medlemmen"

#: widgets/widget-recent-posts.php:149
msgid "Recent Forum Posts"
msgstr "Senaste foruminlägg"

#: widgets/widget-recent-posts.php:195 widgets/widget-recent-topics.php:184
msgid "Forum filter:"
msgstr "Forumfilter:"

#: widgets/widget-recent-topics.php:144
msgid "Recent Forum Topics"
msgstr "Senaste forumämnen"

#: includes/forum.php:2650
msgid "A topic is created in the selected forum when you save this document."
msgstr "Ett ämne skapas i det valda forumet när du sparar detta dokument."

#: includes/forum.php:2613
msgid "Create Forum Topic"
msgstr "Skapa forumämne"

#: admin/admin.php:97
msgid "Reputation"
msgstr "Rykte"

#: admin/views/options.php:870
msgid "Level 5"
msgstr "Nivå 5"

#: admin/views/options.php:866
msgid "Level 4"
msgstr "Nivå 4"

#: admin/views/options.php:862
msgid "Level 3"
msgstr "Nivå 3"

#: admin/views/options.php:858
msgid "Level 2"
msgstr "Nivå 2"

#: admin/views/options.php:854
msgid "Level 1"
msgstr "Nivå 1"

#: admin/views/options.php:849
msgid "Minimum amount of posts:"
msgstr "Minsta antal inlägg:"

#: admin/views/options.php:844
msgid "Enable Reputation"
msgstr "Aktivera rykte"

#: admin/views/options.php:334
msgid "You can use this option if you are using a custom register page."
msgstr "Du kan använda detta alternativ om du använder en anpassad sida för registrering."

#: admin/views/options.php:333
msgid "Custom Register URL"
msgstr "Anpassad URL för registrering"

#: admin/views/options.php:327
msgid "You can use this option if you are using a custom login page."
msgstr "Du kan använda detta alternativ om du använder en anpassad sida för inloggning."

#: admin/views/options.php:326
msgid "Custom Login URL"
msgstr "Anpassad URL för inloggning"

#: integrations/integration-mycred.php:487
msgid "Show ranks in posts and profiles"
msgstr "Visa rankningar i inlägg och profiler"

#: integrations/integration-mycred.php:484
msgid "Show badges in posts and profiles"
msgstr "Visa märken i inlägg och profiler"

#: integrations/integration-mycred.php:481
msgid "Show %_plural% in posts and profiles"
msgstr "Visa %_plural% i inlägg och profiler"

#: integrations/integration-mycred.php:459
msgid "Received Dislike"
msgstr "Mottagna ”Ogilla”"

#: integrations/integration-mycred.php:435
msgid "Received Like"
msgstr "Mottagna ”Gilla”"

#: integrations/integration-mycred.php:417
msgid "Deleted Post"
msgstr "Borttaget inlägg"

#: integrations/integration-mycred.php:410
msgid "Topic authors can receive %_plural% for replying to their own topic."
msgstr "Ämnesförfattare kan få %_plural% för att svara på sitt eget ämne."

#: integrations/integration-mycred.php:384
msgid "New Post"
msgstr "Nytt inlägg"

#: integrations/integration-mycred.php:366
msgid "Deleted Topic"
msgstr "Borttaget ämne"

#: integrations/integration-mycred.php:359
#: integrations/integration-mycred.php:377
#: integrations/integration-mycred.php:401
#: integrations/integration-mycred.php:428
#: integrations/integration-mycred.php:446
#: integrations/integration-mycred.php:470
msgid "required"
msgstr "obligatoriskt"

#: integrations/integration-mycred.php:358
#: integrations/integration-mycred.php:376
#: integrations/integration-mycred.php:400
#: integrations/integration-mycred.php:427
#: integrations/integration-mycred.php:445
#: integrations/integration-mycred.php:469
msgid "Log template"
msgstr "Loggmall"

#: integrations/integration-mycred.php:352
#: integrations/integration-mycred.php:394
#: integrations/integration-mycred.php:452
msgid "Limit"
msgstr "Gräns"

#: integrations/integration-mycred.php:167
msgid "Rank:"
msgstr "Ranking:"

#: integrations/integration-mycred.php:149
msgid "Badges:"
msgstr "Märken:"

#. translators: singular label of point-type for deduction when receiving forum
#. post dislike
#: integrations/integration-mycred.php:70
msgid "%singular% deduction for received forum post dislike"
msgstr "%singular% avdrag för mottaget ”Ogilla” på foruminlägg"

#: integrations/integration-mycred.php:64
msgid "%plural% for received forum post like"
msgstr "%plural% för mottagna ”Gilla” på foruminlägg"

#. translators: singular label of point-type for deduction when deleting forum
#. post
#: integrations/integration-mycred.php:60
msgid "%singular% deduction for deleted forum post"
msgstr "%singular% avdrag för borttaget foruminlägg"

#: integrations/integration-mycred.php:53
msgid "%plural% for new forum post"
msgstr "%plural% för nya foruminlägg"

#. translators: singular label of point-type for deduction when deleting forum
#. topic
#: integrations/integration-mycred.php:49
msgid "%singular% deduction for deleted forum topic"
msgstr "%singular% avdrag för borttaget forumämne"

#: integrations/integration-mycred.php:43
msgid "%plural% for new forum topic"
msgstr "%plural% för nya forumämnen"

#: integrations/integration-mycred.php:23
msgid "Awards %_plural% for Asgaros Forum actions."
msgstr "Tilldela %_plural% for Asgaros Forum-åtgärder."

#: integrations/integration-mycred.php:15
msgid "Received Dislikes (Asgaros Forum)"
msgstr "Mottagna ”Ogilla” (Asgaros Forum)"

#: integrations/integration-mycred.php:14
msgid "Received Likes (Asgaros Forum)"
msgstr "Mottagna ”Gilla” (Asgaros Forum)"

#: integrations/integration-mycred.php:13
msgid "Forum Posts (Asgaros Forum)"
msgstr "Foruminlägg (Asgaros Forum)"

#: integrations/integration-mycred.php:12
msgid "Forum Topics (Asgaros Forum)"
msgstr "Forumämnen (Asgaros Forum)"

#: admin/views/options.php:69
msgid "The description is used for meta tags."
msgstr "Beskrivningen används för metaetiketter."

#: admin/views/options.php:68
msgid "Forum description:"
msgstr "Forumbeskrivning:"

#: includes/forum-uploads.php:307
msgid "Allowed file types:"
msgstr "Tillåtna filtyper:"

#: admin/views/options.php:704 includes/forum-uploads.php:299
msgid "Maximum file size:"
msgstr "Maximal filstorlek:"

#: includes/forum-uploads.php:292
msgid "Maximum files:"
msgstr "Maximalt antal filer:"

#: admin/views/options.php:620
msgid "Available filters"
msgstr "Tillgängliga filter"

#: admin/views/options.php:802
msgid "Results visible without vote"
msgstr "Synliga resultat utan att ha röstat"

#: admin/views/options.php:582
msgid "Enable Suggestions"
msgstr "Aktivera förslag"

#: admin/views/options.php:783
msgid "Activities per page:"
msgstr "Aktiviteter per sida:"

#: includes/forum-memberslist.php:112
msgid "All Users"
msgstr "Alla användare"

#: admin/views/options.php:468
msgid "Users can delete their own topics"
msgstr "Användare kan ta bort sina egna ämnen"

#: admin/views/options.php:451
msgid "Users can delete their own posts"
msgstr "Användare kan ta bort sina egna inlägg"

#: admin/views/options.php:434
msgid "Users can edit their own posts"
msgstr "Användare kan redigera sina egna inlägg"

#: admin/views/options.php:221
msgid "Above & Below Topics"
msgstr "Över och under ämnen"

#: admin/views/options.php:220
msgid "Below Topics"
msgstr "Under ämnen"

#: admin/views/options.php:219
msgid "Above Topics"
msgstr "Över ämnen"

#: admin/views/options.php:215
msgid "Location of subforums:"
msgstr "Plats för underforum:"

#: admin/admin.php:61
msgid "Mentioning"
msgstr "Omnämnande"

#: admin/admin.php:49
msgid "Permissions"
msgstr "Behörigheter"

#: admin/views/appearance.php:69
msgid "Unread indicator color:"
msgstr "Indikatorfärg för ”Oläst”:"

#: admin/views/appearance.php:65
msgid "Read indicator color:"
msgstr "Indikatorfärg för ”Läst”:"

#: admin/views/usergroups.php:34
msgid "Set an optional icon for the usergroup."
msgstr "Ställ in en valfri ikon för användargruppen."

#: admin/views/options.php:340
msgid "URL-slugs for views:"
msgstr "URL-slugs för visningar:"

#: admin/views/options.php:751
msgid "Logged in users & Moderators"
msgstr "Inloggade användare och moderatorer"

#: admin/views/options.php:748
msgid "Who can use signatures:"
msgstr "Vem kan använda signaturer:"

#: includes/forum.php:1048
msgid "This topic contains a poll"
msgstr "Detta ämne innehåller en omröstning"

#: includes/forum.php:1044
msgid "This topic is closed"
msgstr "Detta ämne är stängt"

#: includes/forum.php:1040
msgid "This topic is pinned"
msgstr "Detta ämne är klistrat"

#: admin/views/options.php:282
msgid "The Font Awesome v4 compatibility library is required, if your theme or another plugin uses the Font Awesome v4 icon library. If the Font Awesome v4 icon library is not used on your website, you can disable this option."
msgstr "Font Awesome v4-kompatibilitetsbiblioteket krävs om ditt tema eller ett annat tillägg använder Font Awesome v4-ikonbiblioteket. Om Font Awesome v4-ikonbiblioteket inte används på din webbplats kan du inaktivera detta alternativ."

#: admin/views/options.php:281
msgid "Load Font Awesome v4 compatibility library"
msgstr "Ladda Font Awesome v4-kompatibilitetsbibliotek"

#: includes/forum-polls.php:466
msgid "%s Participant"
msgid_plural "%s Participants"
msgstr[0] "%s deltagare"
msgstr[1] "%s deltagare"

#: includes/forum-polls.php:449
msgid "%s Vote"
msgid_plural "%s Votes"
msgstr[0] "%s röst"
msgstr[1] "%s röster"

#: includes/forum-polls.php:474
msgid "Vote"
msgstr "Rösta"

#: includes/forum-polls.php:134
msgid "To ensure the integrity of existing votes it is not possible to add or remove answers. However, it is still possible to make text-changes to existing answers and to poll-settings. If major changes for a poll are required, you must delete the existing poll and create a new one for this topic."
msgstr "För att säkerställa integriteten för befintliga röster är det inte möjligt att lägga till eller ta bort svar. Det är dock fortfarande möjligt att göra textändringar till befintliga svar och inställningar för omröstningar. Om större ändringar för en omröstning krävs, måste du ta bort den befintliga omröstningen och skapa en ny för detta ämne."

#: includes/forum-polls.php:103 includes/forum-polls.php:128
msgid "An answer ..."
msgstr "Ett svar …"

#: includes/forum-polls.php:94 includes/forum-polls.php:142
msgid "Remove Poll"
msgstr "Ta bort omröstning"

#: includes/forum-polls.php:92 includes/forum-polls.php:140
msgid "Allow multiple answers"
msgstr "Tillåt flera svar"

#: includes/forum-polls.php:86
msgid "After creating a poll it will be no longer possible to add or remove answers. This ensures the integrity of existing votes. However, it will be still possible to make text-changes to existing answers and to poll-settings. If major changes for a poll are required, you must delete the existing poll and create a new one for this topic."
msgstr "Efter att ha skapat en omröstning är det inte längre möjligt att lägga till eller ta bort svar. Detta garanterar integriteten av befintliga röster. Det är dock fortfarande möjligt att göra textändringar till befintliga svar och inställningar för omröstningar. Om större ändringar för en omröstning krävs, måste du ta bort den befintliga omröstningen och skapa en ny för detta ämne."

#: includes/forum-polls.php:78
msgid "Add another answer"
msgstr "Lägg till ett annat svar"

#: includes/forum-polls.php:70 includes/forum-polls.php:121
msgid "Enter your question here"
msgstr "Ange din fråga här"

#: includes/forum-polls.php:65 includes/forum-polls.php:116
msgid "Add Poll"
msgstr "Lägg till omröstning"

#: admin/views/options.php:826
msgid "Enable spoilers"
msgstr "Aktivera spoilers"

#: admin/admin.php:93
msgid "Spoilers"
msgstr "Spoilers"

#: admin/views/options.php:807
msgid "Who can create polls:"
msgstr "Vem kan skapa omröstningar:"

#: admin/views/options.php:797
msgid "Enable Polls"
msgstr "Aktivera omröstningar"

#: admin/admin.php:89
msgid "Polls"
msgstr "Omröstningar"

#: admin/views/options.php:315
msgid "Define if the slug or the ID should be used in URLs for profiles. This setting is useful if you want to hide the unique nicename of users from the public."
msgstr "Definiera om slug eller ID ska användas i URL-adresser för profiler. Denna inställning är användbar om du inte vill visa användarens unika smeknamn offentligt."

#: admin/views/options.php:314
msgid "URL mode for profiles:"
msgstr "URL-läge för profiler:"

#: admin/views/options.php:307 admin/views/options.php:319
msgid "Slug"
msgstr "Slug"

#: admin/views/options.php:303
msgid "Define if the slug or the ID should be used in URLs for forums and topics. This setting is useful if you encounter problems when your slugs include special characters."
msgstr "Definiera om slug eller ID ska användas i URL:er för forum och ämnen. Denna inställning är användbar om du stöter på problem när dina slugs inkluderar specialtecken."

#: admin/views/options.php:302
msgid "URL mode for forums & topics:"
msgstr "URL-läge för forum och ämnen:"

#: admin/views/options.php:275
msgid "You can disable loading the built-in Font Awesome v6 icon library to reduce traffic if your theme or another plugin already loads this library."
msgstr "Du kan inaktivera laddning av det inbyggda Font Awesome v6-ikonbiblioteket för att minska trafiken om ditt tema eller ett annat tillägg redan laddar detta bibliotek."

#: admin/views/options.php:274
msgid "Load Font Awesome v6 icon library"
msgstr "Ladda ikonbiblioteket Font Awesome v6"

#: includes/forum.php:2397
msgid "Reassign all forum posts to:"
msgstr "Tilldela om alla foruminlägg till:"

#: includes/forum.php:2393
msgid "Do not reassign forum posts."
msgstr "Tilldela inte om foruminlägg."

#: includes/forum.php:2385
msgid "What should be done with forum posts owned by these users?"
msgstr "Vad ska göras med foruminlägg som ägs av dessa användare?"

#: includes/forum.php:2383
msgid "What should be done with forum posts owned by this user?"
msgstr "Vad ska göras med foruminlägg som ägs av denna användare?"

#: includes/forum.php:1574 includes/forum.php:1616
msgid "Last post:"
msgstr "Sista inlägget:"

#: includes/forum-profile.php:351
msgid "Likes Received"
msgstr "Gilla-markeringar mottagna"

#: includes/forum-profile.php:335
msgid "Member Activity"
msgstr "Medlemsaktivitet"

#: admin/views/options.php:246
msgid "Enable Avatars"
msgstr "Aktivera profilbilder"

#: admin/views/options.php:141
msgid "Automatically embed content in posts"
msgstr "Bädda in innehåll automatiskt i inlägg"

#: widgets/widget-recent-posts.php:186 widgets/widget-recent-topics.php:180
msgid "Show excerpt"
msgstr "Visa utdrag"

#: includes/forum-reports.php:267
msgid "Show Post"
msgstr "Visa inlägg"

#: includes/forum-reports.php:229
msgid "Reported by:"
msgstr "Rapporterad av:"

#: includes/forum-reports.php:217
msgid "There are no reports."
msgstr "Det finns inga rapporter."

#: includes/forum-reports.php:204
msgid "There are reports."
msgstr "Det finns rapporter."

#: widgets/widget-recent-posts.php:125 widgets/widget-recent-topics.php:120
msgid "Read More"
msgstr "Läs mer"

#: includes/forum.php:1714
msgid "The topic will be pinned to all forums."
msgstr "Ämnet kommer vara klistrat i alla forum."

#: includes/forum.php:1711
msgid "Global Sticky"
msgstr "Globalt klistrat"

#: includes/forum.php:1704
msgid "The topic will be pinned to the current forum."
msgstr "Ämnet kommer vara klistrat i det nuvarande forumet."

#: includes/forum.php:1692
msgid "Select Sticky Mode:"
msgstr "Välj klistrat läge:"

#: includes/forum.php:1834
msgid "Approve"
msgstr "Godkänn"

#: includes/forum.php:637 includes/forum.php:644 includes/forum.php:665
msgid "Sorry, you cannot access this area."
msgstr "Du kan inte komma åt detta område."

#: includes/forum-approval.php:235
msgid "There are no unapproved topics."
msgstr "Det finns inga ej godkända ämnen."

#: includes/forum-approval.php:177
msgid "There are unapproved topics."
msgstr "Det finns ej godkända ämnen."

#: includes/forum-approval.php:160
msgid "Thank you for your topic. Your topic will be visible as soon as it gets approved."
msgstr "Tack för ditt ämne. Ditt ämne kommer att synas så fort det blir godkänt."

#: includes/forum-approval.php:114
msgid "Hello ###USERNAME###,<br><br>You received this message because there is a new unapproved forum-topic.<br><br>Topic:<br>###TITLE###<br><br>Author:<br>###AUTHOR###<br><br>Text:<br>###CONTENT###<br><br>Link:<br>###LINK###"
msgstr "Hej ###USERNAME###,<br><br>Du får detta meddelande eftersom det finns ett nytt ej godkänt forumämne.<br><br>Ämne:<br>###TITLE###<br><br>Författare:<br>###AUTHOR###<br><br>Text:<br>###CONTENT###<br><br>Länk:<br>###LINK###"

#: includes/forum-approval.php:100
msgid "New unapproved topic"
msgstr "Nytt ej godkänt ämne"

#: includes/forum-approval.php:20 includes/forum.php:781
msgid "Unapproved Topics"
msgstr "Ej godkända ämnen"

#: includes/forum-notifications.php:542
msgid "You will automatically get notified about new topics because you are a receiver of administrative notifications."
msgstr "Du kommer automatiskt bli aviserad om nya ämnen eftersom du är mottagare av administrativa aviseringar."

#: includes/forum-activity.php:30
msgid "Activity in the last day."
msgstr "Aktivitet den senaste dagen."

#: includes/forum-spoilers.php:42
msgid "Sorry, only logged-in users can see spoilers."
msgstr "Endast inloggade användare kan se spoilers."

#: includes/forum-spoilers.php:26 includes/forum-spoilers.php:32
msgid "Spoiler"
msgstr "Spoiler"

#: admin/views/options.php:779
msgid "Days of activity to show:"
msgstr "Dagar av aktivitet att visa:"

#: admin/views/options.php:730
msgid "Notify receivers of administrative notifications about new reports"
msgstr "Avisera mottagare av administrativa aviseringar om nya rapporter"

#: admin/views/options.php:546
msgid "Notify receivers of administrative notifications about new topics"
msgstr "Avisera mottagare av administrativa aviseringar om nya ämnen"

#: admin/views/options.php:541
msgid "The subscription-functionality is only available for logged-in users."
msgstr "Prenumerationsfunktionen är endast tillgänglig för inloggade användare."

#: admin/views/options.php:540
msgid "Enable subscriptions"
msgstr "Aktivera prenumerationer"

#: admin/views/options.php:534
msgid "A comma-separated list of mail-addresses which can receive administrative notifications (new reports, unapproved topics, and more)."
msgstr "En kommaseparerad lista med e-postadresser som kan ta emot administrativa aviseringar (nya rapporter, ej godkända ämnen och mer)."

#: admin/views/options.php:533
msgid "Receivers of administrative notifications:"
msgstr "Mottagare av administrativa aviseringar:"

#: admin/views/options.php:208
msgid "Guests & Normal Users"
msgstr "Gäster och vanliga användare"

#: admin/views/options.php:207
msgid "Guests"
msgstr "Gäster"

#: admin/views/options.php:203
msgid "This setting only affects forums that require approval for new topics."
msgstr "Denna inställning påverkar bara forum som kräver godkännande för nya ämnen."

#: admin/views/options.php:202
msgid "Approval needed for new topics from:"
msgstr "Godkännande behövs för nya ämnen från:"

#: admin/views/options.php:830
msgid "Hide spoilers from logged-out users"
msgstr "Dölj spoilers från utloggade användare"

#: admin/tables/admin-structure-table.php:75 admin/views/structure.php:123
msgid "Approval"
msgstr "Godkännande"

#: includes/forum.php:622
msgid "Sorry, only logged-in users can access the forum."
msgstr "Endast inloggade användare kan komma åt forumet."

#: admin/views/appearance.php:73
msgid "Custom CSS:"
msgstr "Anpassad CSS:"

#: admin/views/options.php:173
msgid "Guests can see topics but need to log in to access the posts they contain."
msgstr "Gäster kan se ämnen men måste logga in för att få åtkomst till de inlägg som de innehåller."

#: admin/views/options.php:172
msgid "Hide posts from logged-out users"
msgstr "Dölj inlägg från utloggade användare"

#: admin/views/options.php:166
msgid "Hide forum from logged-out users"
msgstr "Dölj forum från utloggade användare"

#: widgets/widget-recent-posts.php:191
msgid "Group posts by topic"
msgstr "Gruppinlägg efter ämne"

#: includes/forum-usergroups.php:443
msgid "Forum Usergroups"
msgstr "Forum användargrupper"

#: includes/forum.php:1904
msgid "Forum Navigation"
msgstr "Forumnavigering"

#: includes/forum.php:310
msgid "Hello ###USERNAME###,<br><br>You have been mentioned in a forum-post.<br><br>Topic:<br>###TITLE###<br><br>Author:<br>###AUTHOR###<br><br>Text:<br>###CONTENT###<br><br>Link:<br>###LINK###"
msgstr "Hej ###USERNAME###,<br><br>Du har nämnts i ett foruminlägg.<br><br>Ämne:<br>###TITLE###<br><br>Författare:<br>###AUTHOR###<br><br>Text:<br>###CONTENT###<br><br>Länk:<br>###LINK###"

#: includes/forum.php:295
msgid "New topic: ###TITLE###"
msgstr "Nytt ämne: ###TITLE###"

#: includes/forum-unread.php:311
msgid "There are no unread topics."
msgstr "Det finns inga olästa ämnen."

#: includes/forum-unread.php:236
msgid "Show Unread Topics"
msgstr "Visa olästa ämnen"

#: includes/forum-unread.php:94 includes/forum.php:779
msgid "Unread Topics"
msgstr "Olästa ämnen"

#: includes/forum-reports.php:93
msgid "Hello ###USERNAME###,<br><br>There is a new report.<br><br>Topic:<br>###TITLE###<br><br>Post:<br>###CONTENT###<br><br>Post Author:<br>###AUTHOR###<br><br>Reporter:<br>###REPORTER###<br><br>Link:<br>###LINK###"
msgstr "Hej ###USERNAME###,<br><br>Det finns en ny rapport.<br><br>Ämne:<br>###TITLE###<br><br>Inlägg:<br>###CONTENT###<br><br>Inläggsförfattare:<br>###AUTHOR###<br><br>Rapportör:<br>###REPORTER###<br><br>Länk:<br>###LINK###"

#: includes/forum-reports.php:43
msgid "Click to report post."
msgstr "Klicka för att rapportera inlägg."

#: includes/forum-reactions.php:30
msgid "Click for thumbs up."
msgstr "Klicka för tumme upp."

#: includes/forum-reactions.php:25
msgid "Click for thumbs down."
msgstr "Klicka för tumme ner."

#: includes/forum-permissions.php:762
msgid "Forum role assigned."
msgstr "Forumroll tilldelad."

#: includes/forum-permissions.php:715 includes/forum-permissions.php:716
#: includes/forum-permissions.php:717 includes/forum-permissions.php:718
msgid "Assign forum role:"
msgstr "Tilldela forumroll:"

#: includes/forum-permissions.php:660
msgid "Forum Roles:"
msgstr "Forumroller:"

#: includes/forum-online.php:226
msgid "Seen %s ago"
msgstr "Sågs för %s sedan"

#: includes/forum-memberslist.php:208
msgid "No users found!"
msgstr "Inga användare hittades!"

#: admin/views/options.php:632 includes/forum-memberslist.php:138
#: includes/forum-permissions.php:677
msgid "Administrators"
msgstr "Administratörer"

#: admin/views/options.php:628 includes/forum-memberslist.php:128
#: includes/forum-permissions.php:671
msgid "Moderators"
msgstr "Moderatorer"

#: includes/forum-memberslist.php:110
msgid "Roles:"
msgstr "Roller:"

#: includes/forum-memberslist.php:98 includes/forum-memberslist.php:108
msgid "Hide Filters"
msgstr "Dölj filter"

#: includes/forum-memberslist.php:93 includes/forum-memberslist.php:108
msgid "Show Filters"
msgstr "Visa filter"

#: includes/forum-breadcrumbs.php:97
msgid "Forum breadcrumbs - You are here:"
msgstr "Synliga sökvägar i forum – Du är här:"

#: admin/views/options.php:590
msgid "Mentioning notification message:"
msgstr "Aviseringsmeddelande för omnämnande:"

#: admin/views/options.php:586
msgid "Mentioning notification subject:"
msgstr "Ämne vid avisering för omnämnande:"

#: admin/views/options.php:564
msgid "New topic notification message:"
msgstr "Nytt ämnesmeddelande:"

#: admin/views/options.php:560
msgid "New topic notification subject:"
msgstr "Ämne vid avisering om nytt ämne:"

#: admin/views/options.php:555
msgid "New post notification message:"
msgstr "Aviseringsmeddelande vid nytt inlägg:"

#: admin/views/options.php:551
msgid "New post notification subject:"
msgstr "Ämne vid avisering om nytt inlägg:"

#: admin/views/options.php:106
msgid "Dont create topics"
msgstr "Skapa inte ämnen"

#: admin/views/appearance.php:57
msgid "Background color (Second):"
msgstr "Bakgrundsfärg (andra):"

#: admin/views/appearance.php:53
msgid "Background color (First):"
msgstr "Bakgrundsfärg (första):"

#: admin/views/appearance.php:49
msgid "Link color:"
msgstr "Länkfärg:"

#: admin/views/appearance.php:45
msgid "Text color light:"
msgstr "Textfärg ljus:"

#: includes/forum.php:1781
msgid "Unsticky"
msgstr "Klistra inte"

#: includes/forum-usergroups.php:698
msgid "Forum Usergroups:"
msgstr "Forum användargrupper:"

#: includes/forum-profile.php:375
msgid "Ban User"
msgstr "Stäng av användare"

#: includes/forum-profile.php:371
msgid "Unban User"
msgstr "Ta bort avstängning för användare"

#: includes/forum-approval.php:227 includes/forum-unread.php:300
#: includes/forum.php:1061
msgid "In"
msgstr "I"

#: includes/forum-feed.php:55 includes/forum-feed.php:60
msgid "RSS Feed"
msgstr "RSS-flöde"

#: admin/views/options.php:265
msgid "Enable RSS Feeds"
msgstr "Aktivera RSS-flöde"

#: admin/views/options.php:62
msgid "Forum title:"
msgstr "Forumrubrik:"

#: admin/admin.php:602
msgid "Documentation"
msgstr "Dokumentation"

#: admin/views/options.php:761
msgid "Allowed HTML tags:"
msgstr "Tillåtna HTML-taggar:"

#: admin/views/options.php:757
msgid "Allow HTML tags in signatures"
msgstr "Tillåt HTML-taggar i signaturer"

#: admin/views/options.php:744
msgid "Enable signatures"
msgstr "Aktivera signaturer"

#: admin/admin.php:81
msgid "Signatures"
msgstr "Signaturer"

#: admin/views/options.php:910
msgid "Count topic views"
msgstr "Räkna ämnesvisningar"

#: admin/admin.php:166
msgid "HTML tags are not allowed in signatures."
msgstr "HTML-taggar är inte tillåtna i signaturer."

#: admin/admin.php:162
msgid "You can use the following HTML tags in signatures:"
msgstr "Du kan använda följande HTML-taggar i signaturer:"

#: includes/forum-profile.php:220
msgid "In:"
msgstr "I:"

#: includes/forum-profile.php:200
msgid "No posts made by this user."
msgstr "Inga inlägg gjorda av denna användare."

#: includes/forum-profile.php:43 includes/forum-profile.php:69
#: includes/forum-profile.php:133 includes/forum-profile.php:135
msgid "Post History"
msgstr "Inläggshistorik"

#: admin/views/appearance.php:37
msgid "Accent color:"
msgstr "Accentfärg:"

#: includes/forum-activity.php:55
msgid "Yesterday"
msgstr "Igår"

#: includes/forum-activity.php:53
msgid "Today"
msgstr "Idag"

#: admin/views/usergroups.php:48
msgid "Add new users automatically:"
msgstr "Lägg till nya användare automatiskt:"

#: admin/views/options.php:297
msgid "Enable SEO-friendly URLs"
msgstr "Aktivera SEO-vänliga URL:er"

#: admin/tables/admin-usergroups-table.php:90
msgid "Automatically Add:"
msgstr "Lägg till automatiskt:"

#: admin/tables/admin-usergroups-table.php:73
msgid "No"
msgstr "Nej"

#: admin/tables/admin-usergroups-table.php:71
msgid "Yes"
msgstr "Ja"

#: admin/tables/admin-usergroups-table.php:55 includes/forum-online.php:180
msgid "%s User"
msgid_plural "%s Users"
msgstr[0] "%s användare"
msgstr[1] "%s användare"

#: admin/tables/admin-structure-table.php:58
msgid "No description yet ..."
msgstr "Ingen beskrivning ännu …"

#: admin/admin.php:144
msgid "Notify me when I get mentioned"
msgstr "Avisera mig när jag blir omnämnd"

#: includes/forum-notifications.php:651
msgid "You get notified about <b>all</b> new posts."
msgstr "Du blir aviserad om <b>alla</b> nya inlägg."

#: includes/forum-notifications.php:649
msgid "You get notified about <b>all</b> new topics."
msgstr "Du blir aviserad om <b>alla</b> nya ämnen."

#: includes/forum-notifications.php:583
msgid "Notify about new posts in:"
msgstr "Avisera om nya inlägg i:"

#: includes/forum-notifications.php:575
msgid "You get notified about all new topics and posts."
msgstr "Du blir aviserad om alla nya ämnen och inlägg."

#: includes/forum-notifications.php:569
msgid "You get notified about all new topics."
msgstr "Du blir aviserad om alla nya ämnen."

#: includes/forum-notifications.php:563
msgid "You get notified about activity in forums and topics you are subscribed to."
msgstr "Du blir aviserad om aktivitet i forum och ämnen som du prenumererar på."

#: admin/admin.php:85 includes/forum-activity.php:22
#: includes/forum-activity.php:189 includes/forum.php:777
msgid "Activity"
msgstr "Aktivitet"

#: includes/forum-activity.php:109
msgid "No activity yet!"
msgstr "Ingen aktivitet ännu!"

#: includes/forum-activity.php:92
msgid "New topic %1$s created by %2$s."
msgstr "Nytt ämne %1$s skapad av %2$s."

#: admin/views/usergroups.php:44
msgid "Hide usergroup:"
msgstr "Dölj användargrupp:"

#: admin/views/options.php:775
msgid "Enable Activity Feed"
msgstr "Aktivera aktivitetsflöde"

#: admin/tables/admin-usergroups-table.php:89
msgid "Visibility:"
msgstr "Synlighet:"

#: admin/tables/admin-usergroups-table.php:65
msgid "Visible"
msgstr "Synlig"

#: admin/tables/admin-usergroups-table.php:63
msgid "Hidden"
msgstr "Dold"

#: includes/forum.php:1900
msgid "Menu"
msgstr "Meny"

#: includes/forum-notifications.php:662
msgid "Unsubscribe"
msgstr "Avsluta prenumeration"

#: includes/forum-notifications.php:573
msgid "New Topics & Posts"
msgstr "Nya ämnen och inlägg"

#: includes/forum-notifications.php:567
msgid "New Topics"
msgstr "Nya ämnen"

#: includes/forum-notifications.php:561
msgid "Individual Subscriptions"
msgstr "Enskilda prenumerationer"

#: includes/forum-notifications.php:554
msgid "Subscription Settings"
msgstr "Prenumerationsinställningar"

#: includes/forum-notifications.php:82
msgid "You are subscribed to <b>all</b> forums."
msgstr "Du prenumererar på <b>alla</b> forum."

#: includes/forum-notifications.php:54 includes/forum-notifications.php:115
msgid "You are subscribed to <b>all</b> topics."
msgstr "Du prenumererar på <b>alla</b> ämnen."

#: admin/views/options.php:513
msgid "Show category name in breadcrumbs"
msgstr "Visa kategorinamn i synliga sökvägar"

#: admin/admin.php:53
msgid "Breadcrumbs"
msgstr "Synliga sökvägar"

#: includes/forum-online.php:216
msgid "Currently online"
msgstr "Online just nu"

#: includes/forum-profile.php:292
msgid "Last seen:"
msgstr "Senast sedd:"

#: includes/forum.php:306
msgid "You have been mentioned!"
msgstr "Du har nämnts!"

#: includes/forum-database.php:332
msgid "My first forum."
msgstr "Mitt första forum."

#: includes/forum-database.php:331
msgid "First Forum"
msgstr "Första forum"

#: includes/forum-database.php:323
msgid "Example Category"
msgstr "Exempelkategori"

#: admin/views/structure.php:99
msgid "Parent:"
msgstr "Överordnad:"

#: admin/views/options.php:578
msgid "Enable Mentioning"
msgstr "Aktivera omnämnande"

#: includes/forum-online.php:193
msgid "%s Guest"
msgid_plural "%s Guests"
msgstr[0] "%s gäst"
msgstr[1] "%s gäster"

#: widgets/widget-search.php:24 widgets/widget-search.php:58
msgid "Forum Search"
msgstr "Forumsök"

#: widgets/widget-search.php:13
msgid "Asgaros Forum: Search"
msgstr "Asgaros Forum: Sök"

#: widgets/widget-search.php:11
msgid "A search form for Asgaros Forum."
msgstr "Ett sökformulär för Asgaros Forum."

#: includes/forum-reports.php:92
msgid "New report"
msgstr "Ny rapport"

#: includes/forum-reports.php:47
msgid "You reported this post."
msgstr "Du rapporterade detta inlägg."

#: includes/forum-reports.php:41
msgid "Report Post"
msgstr "Rapportera inlägg"

#: includes/forum-reports.php:33
msgid "Are you sure that you want to report this post?"
msgstr "Är du säker på att du vill rapportera detta inlägg?"

#: includes/forum-profile.php:362
msgid "Edit Profile"
msgstr "Redigera profil"

#: admin/views/options.php:916
msgid "Save Settings"
msgstr "Spara inställningar"

#: admin/views/options.php:726
msgid "Enable reports"
msgstr "Aktivera rapporter"

#: admin/views/options.php:683
msgid "Who can upload files:"
msgstr "Vem kan ladda upp filer:"

#: admin/views/options.php:671
msgid "Allow uploads"
msgstr "Tillåt uppladdningar"

#: admin/views/options.php:250
msgid "Enable reactions"
msgstr "Aktivera reaktioner"

#: admin/views/options.php:11
msgid "Settings updated."
msgstr "Inställningar uppdaterade."

#: admin/views/appearance.php:79
msgid "Save Appearance"
msgstr "Spara utseende"

#: admin/views/appearance.php:29
msgid "Font size:"
msgstr "Textstorlek:"

#: admin/views/appearance.php:25
msgid "Font:"
msgstr "Typsnitt:"

#: admin/views/appearance.php:11
msgid "Appearance updated."
msgstr "Utseende uppdaterad."

#: includes/forum-reports.php:262
msgid "Delete Report"
msgstr "Ta bort rapport"

#: admin/admin.php:246 admin/views/options.php:10
msgid "Settings"
msgstr "Inställningar"

#: admin/admin.php:77 includes/forum-reports.php:20 includes/forum.php:783
msgid "Reports"
msgstr "Rapporter"

#: includes/forum-memberslist.php:31 includes/forum-memberslist.php:75
#: includes/forum.php:775
msgid "Members"
msgstr "Medlemmar"

#: admin/views/appearance.php:61
msgid "Border color:"
msgstr "Ramfärg:"

#: admin/views/options.php:616
msgid "Members per page:"
msgstr "Medlemmar per sida:"

#: admin/views/options.php:608
msgid "Show members list to logged-in users only"
msgstr "Visa medlemslistan till endast inloggade användare"

#: admin/views/options.php:604
msgid "Enable members list"
msgstr "Aktivera medlemslista"

#: admin/admin.php:65
msgid "Members List"
msgstr "Medlemslista"

#: includes/forum-database.php:275
msgid "Example Usergroup"
msgstr "Exempel användargrupp"

#: admin/views/usergroups.php:96
msgid "Deleting this category will also permanently delete all usergroups inside it. Are you sure you want to delete this category?"
msgstr "Om du tar bort denna kategori tas alla användargrupper bort permanent inuti den. Är du säker på att du vill ta bort denna kategori?"

#: includes/forum-database.php:255
msgid "Custom Usergroups"
msgstr "Anpassade användargrupper"

#: admin/views/options.php:101
msgid "Create topics for new blog posts in the following forum:"
msgstr "Skapa ämnen för nya blogginlägg i följande forum:"

#: includes/forum-profile.php:316
msgid "Signature:"
msgstr "Signatur:"

#: includes/forum-profile.php:306
msgid "Biographical Info:"
msgstr "Biografisk information:"

#: admin/admin.php:607
msgid "Donate"
msgstr "Donera"

#: admin/admin.php:597
msgid "Official Support Forum"
msgstr "Officiellt supportforum"

#: admin/views/options.php:191
msgid "Show register button"
msgstr "Visa registreringsknapp"

#: admin/views/options.php:186
msgid "Show logout button"
msgstr "Visa utloggningsknapp"

#: admin/views/options.php:181
msgid "Show login button"
msgstr "Visa inloggningsknapp"

#: includes/forum.php:2007
msgid "Logout"
msgstr "Logga ut"

#: includes/forum-unread.php:227
msgid "Nothing new"
msgstr "Ingenting nytt"

#: includes/forum-profile.php:236 includes/forum-profile.php:381
msgid "This user does not exist."
msgstr "Denna användare finns inte."

#: includes/forum-profile.php:346
msgid "Replies Created"
msgstr "Svar skapade"

#: includes/forum-profile.php:341
msgid "Topics Started"
msgstr "Ämnen startade"

#: includes/forum-profile.php:299
msgid "Member Since:"
msgstr "Medlem sedan:"

#: includes/forum-profile.php:284
msgid "Website:"
msgstr "Webbplats:"

#: includes/forum-profile.php:265
msgid "First Name:"
msgstr "Förnamn:"

#: admin/admin.php:130 includes/forum-permissions.php:111
#: includes/forum-permissions.php:716
msgid "Moderator"
msgstr "Moderator"

#: admin/admin.php:131 includes/forum-permissions.php:109
#: includes/forum-permissions.php:717
msgid "Administrator"
msgstr "Administratör"

#: admin/admin.php:129 includes/forum-notifications.php:460
#: includes/forum-permissions.php:115 includes/forum-permissions.php:715
msgid "User"
msgstr "Användare"

#: admin/admin.php:125 includes/forum-permissions.php:647
msgid "Forum Role"
msgstr "Forumroll"

#: includes/forum-profile.php:191 includes/forum-profile.php:253
msgid "You need to login to have access to profiles."
msgstr "Du måste logga in för att få åtkomst till profiler."

#: includes/forum-profile.php:37 includes/forum-profile.php:63
#: includes/forum-profile.php:126 includes/forum-profile.php:128
#: includes/forum-profile.php:444
msgid "Profile"
msgstr "Profil"

#: admin/views/options.php:657
msgid "Show profiles to logged-in users only"
msgstr "Visa profiler endast för inloggade användare"

#: admin/views/options.php:653
msgid "Enable profiles"
msgstr "Aktivera profiler"

#: admin/admin.php:69
msgid "Profiles"
msgstr "Profiler"

#: admin/views/options.php:156
msgid "Show author posts counter"
msgstr "Visa författarens inläggsräknare"

#: includes/forum-notifications.php:654
msgid "No subscriptions yet!"
msgstr "Inga prenumerationer än!"

#: includes/forum-notifications.php:22 includes/forum-notifications.php:521
#: includes/forum.php:769
msgid "Subscriptions"
msgstr "Prenumerationer"

#: admin/views/options.php:161
msgid "Show description in forum"
msgstr "Visa beskrivning i forumet"

#: includes/forum.php:2043
msgid "Register"
msgstr "Registrera"

#: admin/views/structure.php:109 admin/views/usergroups.php:39
msgid "List of available icons."
msgstr "Lista på tillgängliga ikoner."

#: admin/views/structure.php:105 admin/views/usergroups.php:33
msgid "Icon:"
msgstr "Ikon:"

#: includes/forum-content.php:151
msgid "You are not allowed to upload files with that file size."
msgstr "Du har inte behörighet att ladda upp filer med den filstorleken."

#: includes/forum-content.php:145
msgid "You are not allowed to upload files with that file extension."
msgstr "Du har inte behörighet att ladda upp filer med den filändelsen."

#: admin/views/options.php:528
msgid "Sender mail:"
msgstr "Avsändarens e-post:"

#: admin/views/options.php:524
msgid "Sender name:"
msgstr "Avsändarens namn:"

#: includes/views/post-element.php:169
msgid "Last edited on %1$s by %2$s"
msgstr "Senast redigerad den %1$s av %2$s"

#: includes/forum-usergroups.php:814
msgid "Usergroups assignments updated."
msgstr "Användargruppernas tilldelning uppdaterad."

#: includes/forum-usergroups.php:756
msgid "Remove from"
msgstr "Ta bort från"

#: includes/forum-usergroups.php:752
msgid "Add to"
msgstr "Lägg till i"

#: includes/forum-usergroups.php:545
msgid "When usergroups are selected, only users of the selected usergroups will have access to the category."
msgstr "När användargrupper är valda, kommer endast användare av de valda användargrupperna att ha åtkomst till kategorin."

#: includes/forum-memberslist.php:172 includes/forum-profile.php:275
#: includes/forum-usergroups.php:515 includes/forum-usergroups.php:532
msgid "Usergroups:"
msgstr "Användargrupper:"

#: includes/forum-online.php:199
msgid "Currently nobody is online."
msgstr "För närvarande är ingen online."

#: includes/forum-online.php:160
msgid "Currently Online:"
msgstr "För närvarande online:"

#: includes/forum-online.php:153
msgid "Newest Member:"
msgstr "Nyaste medlemmen:"

#: admin/views/usergroups.php:140 admin/views/usergroups.php:142
msgid "Add Usergroup"
msgstr "Lägg till användargrupp"

#: admin/views/usergroups.php:64
msgid "Are you sure you want to delete this usergroup?"
msgstr "Är du säker på att du vill ta bort denna användargrupp?"

#: admin/views/usergroups.php:10
msgid "Usergroups updated."
msgstr "Användargrupper uppdaterade."

#: admin/views/usergroups.php:28
msgid "Color:"
msgstr "Färg:"

#: admin/tables/admin-usergroups-table.php:81
msgid "Edit Usergroup"
msgstr "Redigera användargrupp"

#: admin/tables/admin-usergroups-table.php:79
msgid "Delete Usergroup"
msgstr "Ta bort användargrupp"

#: admin/admin.php:242 admin/views/usergroups.php:9
#: includes/forum-usergroups.php:624
msgid "Usergroups"
msgstr "Användargrupper"

#: widgets/widget-recent-posts.php:181 widgets/widget-recent-topics.php:175
msgid "Show avatars"
msgstr "Visa profilbilder"

#: includes/forum.php:406
msgid "Page"
msgstr "Sida"

#: admin/views/structure.php:234 admin/views/structure.php:237
msgid "Add Forum"
msgstr "Lägg till forum"

#: admin/views/structure.php:222 admin/views/usergroups.php:127
msgid "Delete Category"
msgstr "Ta bort kategori"

#: admin/views/structure.php:177 admin/views/structure.php:180
#: admin/views/structure.php:244 admin/views/structure.php:246
#: admin/views/usergroups.php:106 admin/views/usergroups.php:109
#: admin/views/usergroups.php:148 admin/views/usergroups.php:150
msgid "Add Category"
msgstr "Lägg till kategori"

#: admin/views/structure.php:153
msgid "Deleting this category will also permanently delete all forums, sub-forums, topics and posts inside it. Are you sure you want to delete this category?"
msgstr "Borttagning av denna kategori kommer även att permanent ta bort alla underforum, ämnen och inlägg i den. Är du säker på att du vill ta bort denna kategori?"

#: admin/views/structure.php:10
msgid "Structure updated."
msgstr "Struktur uppdaterad."

#: admin/admin.php:240 admin/views/structure.php:9
msgid "Structure"
msgstr "Struktur"

#: admin/tables/admin-structure-table.php:110
#: admin/tables/admin-usergroups-table.php:91
msgid "Actions:"
msgstr "Åtgärder:"

#: admin/tables/admin-structure-table.php:108 admin/views/structure.php:114
msgid "Status:"
msgstr "Status:"

#: admin/tables/admin-structure-table.php:97
#: admin/tables/admin-structure-table.php:98
msgid "Add Sub-Forum"
msgstr "Lägg till underforum "

#: admin/tables/admin-structure-table.php:87
#: admin/tables/admin-structure-table.php:88
msgid "Delete Forum"
msgstr "Ta bort forum"

#: admin/tables/admin-structure-table.php:73 admin/views/structure.php:121
msgid "Normal"
msgstr "Normal"

#: admin/tables/admin-structure-table.php:74 admin/views/structure.php:122
msgid "Closed"
msgstr "Stängd"

#: admin/views/options.php:675
msgid "Show thumbnails"
msgstr "Visa miniatyrer"

#: includes/forum-online.php:134
msgid "Online"
msgstr "Online"

#: admin/views/options.php:899
msgid "Show who is online"
msgstr "Visa vem som är online"

#: admin/views/options.php:509
msgid "Enable breadcrumbs"
msgstr "Aktivera synliga sökvägar"

#: admin/admin.php:41
msgid "Features"
msgstr "Funktioner"

#: admin/tables/admin-structure-table.php:53
#: admin/tables/admin-usergroups-table.php:50 admin/views/options.php:308
#: admin/views/options.php:320 admin/views/structure.php:204
msgid "ID"
msgstr "ID"

#: admin/views/options.php:85
msgid "Page which contains the [forum]-shortcode."
msgstr "Sidan som innehållet kortkoden [forum]."

#: admin/views/options.php:700 admin/views/options.php:712
msgid "(0 = No limitation)"
msgstr "(0 = Ingen begränsning)"

#: admin/views/options.php:697
msgid "Maximum files per post:"
msgstr "Max antal filer per inlägg:"

#: admin/admin.php:153
msgid "Signature"
msgstr "Signatur"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: asgaros-forum.php
msgid "https://asgaros.com"
msgstr "https://asgaros.com"

#: admin/views/options.php:624 includes/forum-memberslist.php:119
#: includes/forum-permissions.php:665 includes/forum-statistics.php:32
msgid "Users"
msgstr "Användare"

#: admin/admin.php:101 includes/forum-statistics.php:21
msgid "Statistics"
msgstr "Statistik"

#: admin/views/options.php:887
msgid "Show statistics"
msgstr "Visa statistik"

#: includes/forum-notifications.php:94
msgid "<b>Subscribe</b> to this forum."
msgstr "<b>Prenumerera</b> på detta forum."

#: includes/forum-notifications.php:88
msgid "<b>Unsubscribe</b> from this forum."
msgstr "<b>Avsluta prenumeration</b> från detta forum."

#. Description of the plugin
#: asgaros-forum.php
msgid "Asgaros Forum is the best forum solution for WordPress! It comes with dozens of features in a beautiful design and stays slight, simple and fast."
msgstr "Asgaros Forum är den bästa forumlösningen för Wordpress! Forumet innehåller massor av funktioner, har en tilltalande design, tar lite utrymme och är enkelt och snabbt."

#: includes/views/post-element.php:71
msgid "Topic Author"
msgstr "Ämnesförfattare"

#: includes/forum-search.php:80
msgid "No results found for:"
msgstr "Inga resultat hittade för:"

#: includes/forum-search.php:70
msgid "Search results:"
msgstr "Sökresultat:"

#: includes/forum-statistics.php:25 includes/views/forum.php:37
msgid "Topics"
msgstr "Ämnen"

#: includes/forum-search.php:71 includes/forum.php:1104
#: includes/views/forum.php:38 includes/views/overview.php:13
msgid "Last post"
msgstr "Sista inlägget"

#: includes/forum.php:995
msgid "%s Topic"
msgid_plural "%s Topics"
msgstr[0] "%s ämne"
msgstr[1] "%s ämnen"

#: includes/forum-search.php:57 widgets/widget-search.php:45
msgid "Search ..."
msgstr "Sök …"

#: includes/forum.php:1804
msgid "Open"
msgstr "Öppen"

#: includes/forum-approval.php:219 includes/forum.php:1056
msgid "By"
msgstr "Av"

#: includes/forum-search.php:29 includes/forum.php:767
msgid "Search"
msgstr "Sök"

#: includes/forum-breadcrumbs.php:89 includes/forum.php:765
msgid "Move Topic"
msgstr "Flytta ämne"

#: includes/forum-breadcrumbs.php:85 includes/forum-editor.php:213
#: includes/forum.php:763 includes/forum.php:1733
#: integrations/integration-mycred.php:342
msgid "New Topic"
msgstr "Nytt ämne"

#: admin/views/options.php:261
msgid "Enable search functionality"
msgstr "Aktivera sökfunktionalitet"

#: widgets/widget-recent-posts.php:25 widgets/widget-recent-topics.php:25
#: widgets/widget-search.php:51
msgid "The forum has not been configured correctly."
msgstr "Forumet har inte konfigurerats korrekt."

#: admin/views/options.php:75
msgid "Forum location:"
msgstr "Forumets plats:"

#: widgets/widget-recent-topics.php:157
msgid "Recent forum topics"
msgstr "Senaste forumämnena"

#: widgets/widget-recent-topics.php:17
msgid "Asgaros Forum: Recent Topics"
msgstr "Asgaros Forum: Senaste ämnena"

#: widgets/widget-recent-topics.php:15
msgid "Shows recent topics in Asgaros Forum."
msgstr "Visa senaste ämnena i Asgaros Forum."

#: includes/forum.php:1569 widgets/widget-recent-posts.php:34
#: widgets/widget-recent-posts.php:46 widgets/widget-recent-posts.php:59
#: widgets/widget-recent-posts.php:81 widgets/widget-recent-topics.php:34
#: widgets/widget-recent-topics.php:46 widgets/widget-recent-topics.php:59
#: widgets/widget-recent-topics.php:73
msgid "No topics yet!"
msgstr "Inga ämnen ännu!"

#: admin/views/options.php:151
msgid "Highlight topic authors"
msgstr "Markera ämnenas författare"

#: includes/forum-notifications.php:594
msgid "Notify about new topics in:"
msgstr "Meddela om nya ämnen i:"

#: includes/views/overview.php:18
msgid "In this category are no forums yet!"
msgstr "I denna kategori finns inga forum ännu!"

#: includes/forum.php:1441 includes/forum.php:1460
msgid "Deleted user"
msgstr "Borttagen användare"

#: includes/forum.php:1444 includes/forum.php:1466
msgid "Guest"
msgstr "Gäst"

#: includes/forum-uploads.php:191
msgid "You need to login to have access to uploads."
msgstr "Du måste logga in för att få åtkomst till uppladdningar."

#: admin/views/options.php:679
msgid "Show uploaded files to logged-in users only"
msgstr "Visa uppladdade filer endast till inloggade användare"

#: admin/views/options.php:269
msgid "Allow guest postings"
msgstr "Tillåt gästinlägg"

#: admin/views/options.php:136
msgid "Allow shortcodes in posts"
msgstr "Tillåt kortkoder i inlägg"

#: includes/forum.php:632
msgid "Sorry, only logged-in users can access this category."
msgstr "Endast inloggade användare kan komma åt denna kategori."

#: includes/forum-notifications.php:66 includes/forum-notifications.php:118
msgid "<b>Subscribe</b> to this topic."
msgstr "<b>Prenumerera</b> på detta ämne."

#: includes/forum-notifications.php:60
msgid "<b>Unsubscribe</b> from this topic."
msgstr "<b>Avsluta prenumereration</b> från detta ämne."

#: admin/admin.php:57
msgid "Notifications"
msgstr "Aviseringar"

#: admin/admin.php:132 admin/views/options.php:636
#: includes/forum-memberslist.php:147 includes/forum-permissions.php:113
#: includes/forum-permissions.php:683 includes/forum-permissions.php:718
#: includes/views/post-element.php:63
msgid "Banned"
msgstr "Avstängd"

#: includes/forum.php:1007 includes/forum.php:1103
msgid "Subforums"
msgstr "Underforum"

#: includes/forum-content.php:75
msgid "You are banned!"
msgstr "Du är avstängd!"

#: admin/views/appearance.php:41
msgid "Text color:"
msgstr "Textfärg:"

#: admin/views/appearance.php:33
msgid "Forum color:"
msgstr "Forumfärg:"

#: admin/admin.php:241 admin/views/appearance.php:10
#: admin/views/appearance.php:21
msgid "Appearance"
msgstr "Utseende"

#: admin/admin.php:73
msgid "Uploads"
msgstr "Uppladdningar"

#: admin/admin.php:37
msgid "General"
msgstr "Allmänt"

#: admin/views/structure.php:167
msgid "Deleting this forum will also permanently delete all sub-forums, topics and posts inside it. Are you sure you want to delete this forum?"
msgstr "Om du tar bort detta forum kommer också alla underforum, ämnen och inlägg i det att tas bort permanent. Är du säker på att du vill ta bort detta forum?"

#: admin/views/structure.php:77 admin/views/structure.php:144
#: admin/views/structure.php:157 admin/views/structure.php:171
#: admin/views/usergroups.php:55 admin/views/usergroups.php:68
#: admin/views/usergroups.php:87 admin/views/usergroups.php:100
#: includes/forum-editor.php:266 includes/forum-editor.php:271
msgid "Cancel"
msgstr "Avbryt"

#: admin/views/structure.php:76 admin/views/structure.php:143
#: admin/views/usergroups.php:54 admin/views/usergroups.php:86
msgid "Save"
msgstr "Spara"

#: admin/tables/admin-structure-table.php:91
#: admin/tables/admin-structure-table.php:92
msgid "Edit Forum"
msgstr "Redigera forum"

#: admin/tables/admin-structure-table.php:109 admin/views/structure.php:69
#: admin/views/structure.php:137 admin/views/structure.php:215
msgid "Order:"
msgstr "Sortering:"

#: admin/views/options.php:693
msgid "Allowed filetypes:"
msgstr "Tillåtna filtyper:"

#: includes/forum-memberslist.php:244 includes/forum.php:997
#: includes/views/post-element.php:50
msgid "%s Post"
msgid_plural "%s Posts"
msgstr[0] "%s inlägg"
msgstr[1] "%s inlägg"

#: includes/forum.php:1079
msgid "%s View"
msgid_plural "%s Views"
msgstr[0] "%s visning"
msgstr[1] "%s visningar"

#: admin/views/options.php:688 admin/views/options.php:752
#: admin/views/options.php:811 admin/views/structure.php:64
#: admin/views/structure.php:212
msgid "Moderators only"
msgstr "Endast moderatorer"

#: admin/views/options.php:687 admin/views/options.php:810
#: admin/views/structure.php:63 admin/views/structure.php:210
msgid "Logged in users only"
msgstr "Endast inloggade användare"

#: admin/views/options.php:686 admin/views/structure.php:62
#: admin/views/structure.php:208
msgid "Everyone"
msgstr "Vem som helst"

#: admin/views/structure.php:59 admin/views/structure.php:206
msgid "Access:"
msgstr "Åtkomst:"

#: admin/views/options.php:146
msgid "Highlight administrator/moderator names"
msgstr "Markera namn på administratör/moderator"

#: admin/views/options.php:196
msgid "Show edit date"
msgstr "Visa redigeringsdatum"

#: includes/views/post-element.php:171
msgid "Last edited on %s"
msgstr "Senast redigerad den %s"

#: includes/forum.php:651
msgid "Sorry, only logged-in users can access this topic."
msgstr "Endast inloggade användare kan komma åt detta ämne."

#: includes/forum.php:2025
msgid "Login"
msgstr "Logga in"

#: includes/forum.php:686
msgid "You need to log in to create posts and topics."
msgstr "Du måste logga in för att skapa inlägg och ämnen. "

#: widgets/widget-recent-posts.php:175 widgets/widget-recent-topics.php:169
msgid "Number of topics to show:"
msgstr "Antal ämnen att visa:"

#: widgets/widget-recent-posts.php:170 widgets/widget-recent-topics.php:164
#: widgets/widget-search.php:61
msgid "Title:"
msgstr "Rubrik:"

#: includes/forum.php:1660
msgid "%s ago"
msgstr "%s sedan"

#: widgets/widget-recent-posts.php:118 widgets/widget-recent-topics.php:113
msgid "by"
msgstr "av"

#: widgets/widget-recent-posts.php:162
msgid "Recent forum posts"
msgstr "Senaste foruminläggen"

#: widgets/widget-recent-posts.php:17
msgid "Asgaros Forum: Recent Posts"
msgstr "Asgaros Forum: Senaste inläggen"

#: widgets/widget-recent-posts.php:15
msgid "Shows recent posts in Asgaros Forum."
msgstr "Visa senaste inläggen i Asgaros Forum."

#: includes/forum.php:1163 includes/forum.php:1757 includes/forum.php:1862
msgid "Are you sure you want to remove this?"
msgstr "Är du säker på att du vill ta bort detta?"

#. Author of the plugin
#: asgaros-forum.php
msgid "Thomas Belser"
msgstr "Thomas Belser"

#. Plugin Name of the plugin
#: asgaros-forum.php integrations/integration-mycred.php:22
msgid "Asgaros Forum"
msgstr "Asgaros Forum"

#: includes/forum-unread.php:231 includes/forum-unread.php:247
msgid "Mark All Read"
msgstr "Markera alla som lästa"

#: includes/forum-unread.php:225
msgid "New posts"
msgstr "Nya inlägg"

#: includes/views/forum.php:63
msgid "There are no topics yet!"
msgstr "Det finns inga ämnen ännu!"

#: includes/forum-statistics.php:26
msgid "Posts"
msgstr "Inlägg"

#: includes/forum-statistics.php:29
msgid "Views"
msgstr "Visningar"

#: includes/forum-editor.php:274
msgid "Submit"
msgstr "Skicka"

#: includes/forum-uploads.php:281
msgid "Add another file ..."
msgstr "Lägg till en annan fil …"

#: includes/forum-uploads.php:261
msgid "Upload Files:"
msgstr "Ladda upp filer:"

#: includes/forum-editor.php:236
msgid "Subject:"
msgstr "Ämne:"

#: includes/forum-editor.php:215
msgid "Post Reply:"
msgstr "Publicera svar:"

#: includes/forum-editor.php:195 includes/views/post-element.php:123
msgid "Quote from"
msgstr "Citat från"

#: includes/forum-content.php:139
msgid "You must enter a message."
msgstr "Du måste ange ett meddelande."

#: includes/forum-content.php:128
msgid "You must enter a subject."
msgstr "﻿Du måste ange ett ämne."

#: includes/forum-content.php:69 includes/forum-content.php:81
#: includes/forum-content.php:87 includes/forum-content.php:93
#: includes/forum-content.php:105 includes/forum-content.php:113
#: includes/forum-content.php:121 includes/forum-editor.php:183
msgid "You are not allowed to do this."
msgstr "Du har inte behörighet att göra detta."

#: includes/forum.php:2218
msgid "Sorry, this post does not exist."
msgstr "Detta inlägg finns inte."

#: includes/forum-uploads.php:212 includes/forum-uploads.php:244
msgid "Uploaded files:"
msgstr "Uppladdade filer:"

#: admin/tables/admin-usergroups-table.php:79 admin/views/structure.php:156
#: admin/views/structure.php:170 admin/views/usergroups.php:67
#: admin/views/usergroups.php:99 includes/forum-uploads.php:235
#: includes/forum.php:1165 includes/forum.php:1759 includes/forum.php:1864
msgid "Delete"
msgstr "Ta bort"

#: includes/forum.php:1814
msgid "Close"
msgstr "Stäng"

#: includes/forum.php:1701 includes/forum.php:1790
msgid "Sticky"
msgstr "Klistrat"

#: includes/forum.php:1823
msgid "Reply"
msgstr "﻿Svara"

#: admin/tables/admin-usergroups-table.php:81 includes/forum.php:1872
msgid "Edit"
msgstr "Redigera"

#: includes/forum.php:1881
msgid "Quote"
msgstr "Citat"

#: includes/forum-editor.php:195 includes/views/post-element.php:123
msgid "on %s"
msgstr "den %s"

#: includes/forum.php:1218
msgid "You are not allowed to move topics."
msgstr "Du har inte behörighet att flytta ämnen."

#: includes/forum.php:1216 includes/forum.php:1770
msgid "Move"
msgstr "Flytta"

#: includes/forum.php:1190
msgid "Move \"<strong>%s</strong>\" to new forum:"
msgstr "Flytta ”<strong>%s</strong>” till nytt forum:"

#: includes/forum.php:2219
msgid "Sorry, this topic does not exist."
msgstr "Detta ämne finns inte."

#: includes/forum.php:1174
msgid "Sorry, but there are no posts."
msgstr "Det finns inga inlägg."

#: includes/forum.php:2220
msgid "Sorry, this forum does not exist."
msgstr "Detta forum finns inte."

#: includes/views/overview.php:37
msgid "There are no categories yet!"
msgstr "Det finns inga kategorier ännu!"

#: includes/forum-breadcrumbs.php:77 includes/forum.php:761
msgid "Post Reply"
msgstr "Publicera svar"

#: includes/forum-breadcrumbs.php:81 includes/forum-editor.php:217
#: includes/forum.php:759
msgid "Edit Post"
msgstr "Redigera inlägg"

#: admin/views/structure.php:224 admin/views/usergroups.php:129
msgid "Edit Category"
msgstr "Redigera kategori"

#: admin/views/options.php:96
msgid "Topics to show per page:"
msgstr "Antal ämnen att visa per sida:"

#: admin/views/options.php:91
msgid "Replies to show per page:"
msgstr "Antal svar att visa per sida:"

#: admin/views/structure.php:95
msgid "Description:"
msgstr "Beskrivning:"

#: admin/tables/admin-structure-table.php:107
#: admin/tables/admin-usergroups-table.php:88 admin/views/structure.php:55
#: admin/views/structure.php:91 admin/views/usergroups.php:24
#: admin/views/usergroups.php:80
msgid "Name:"
msgstr "Namn:"

#: admin/admin.php:115 admin/admin.php:239 includes/forum.php:268
#: includes/forum.php:1994 includes/forum.php:2374
msgid "Forum"
msgstr "Forum"