<?php
/**
 * DiscGolf Theme – ultra-minimal dark/light via a plain <a>
 * - Sets theme before paint (no flash)
 * - Works with <a class="dg-theme-link" href="?theme=toggle">☾</a>
 */

add_action('after_setup_theme', function () {
    add_theme_support('wp-block-styles');
    add_theme_support('responsive-embeds');
    add_theme_support('editor-styles');
});

/**
 * Pre-paint: read ?theme=, else localStorage, else OS.
 * Also cleans the URL if a query param was used.
 */
add_action('wp_head', function () {
    ?>
    <script>
    (function () {
      try {
        var KEY = 'dg-theme';
        var params = new URLSearchParams(location.search);
        var q = params.get('theme'); // "light" | "dark" | "toggle" | null

        // Determine current stored/OS theme
        var stored = localStorage.getItem(KEY);
        var prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        var current = stored || (prefersDark ? 'dark' : 'light');

        // If a query param is present, apply it
        if (q === 'light' || q === 'dark') {
          current = q;
          localStorage.setItem(KEY, current);
          // Clean URL (no reload)
          try { history.replaceState({}, '', location.pathname + location.hash); } catch(e){}
        } else if (q === 'toggle') {
          current = (current === 'dark') ? 'light' : 'dark';
          localStorage.setItem(KEY, current);
          try { history.replaceState({}, '', location.pathname + location.hash); } catch(e){}
        }

        document.documentElement.setAttribute('data-theme', current);
      } catch (e) {}
    })();
    </script>
    <?php
}, 0);

/** Styles only */
add_action('wp_enqueue_scripts', function () {
    wp_enqueue_style('discgolf-theme', get_stylesheet_uri(), [], wp_get_theme()->get('Version'));
    wp_enqueue_style('dg-theme-modes', get_stylesheet_directory_uri() . '/assets/css/theme-modes.css', [], wp_get_theme()->get('Version'));
});

/**
 * Footer: make the icon act like a link OR an instant toggle.
 * - If JS runs: we prevent navigation and toggle immediately.
 * - If JS didn’t run: the link still works via ?theme=toggle.
 */
add_action('wp_footer', function () {
    ?>
    <script>
    (function(){
      var KEY = 'dg-theme';
      function getTheme(){ return document.documentElement.getAttribute('data-theme') || 'light'; }
      function setTheme(t){
        document.documentElement.setAttribute('data-theme', t);
        try { localStorage.setItem(KEY, t); } catch(e){}
        // Update icon + make href reflect the next action
        document.querySelectorAll('a.dg-theme-link').forEach(function(a){
          a.textContent = (t === 'dark') ? '☀︎' : '☾';
          a.setAttribute('title', 'Växla tema');
          a.setAttribute('href', '?theme=toggle');
        });
      }

      document.addEventListener('DOMContentLoaded', function(){
        setTheme(getTheme());

        // One capture listener is enough; works even if the link is inside other blocks
        document.addEventListener('click', function(ev){
          var link = ev.target.closest('a.dg-theme-link');
          if (!link) return;
          ev.preventDefault(); // don’t navigate; we’ll toggle instantly
          var next = getTheme() === 'dark' ? 'light' : 'dark';
          setTheme(next);
        }, true);
      });
    })();
    </script>
    <?php
}, 100);

// --- Asgaros Forum skin -------------------------------------------------------
add_action('wp_enqueue_scripts', function () {
    $path = get_stylesheet_directory() . '/assets/css/asgaros-theme.css';
    $ver  = file_exists($path) ? filemtime($path) : wp_get_theme()->get('Version');

    // Beroenden läggs villkorligt (inget “missing dependencies”)
    $deps = [];
    if (wp_style_is('af-style', 'registered') || wp_style_is('af-style', 'enqueued')) {
        $deps[] = 'af-style';
    }
    if (wp_style_is('af-custom-color', 'registered') || wp_style_is('af-custom-color', 'enqueued')) {
        $deps[] = 'af-custom-color';
    }

    wp_enqueue_style(
        'dg-asgaros-theme',
        get_stylesheet_directory_uri() . '/assets/css/asgaros-theme.css',
        $deps,
        $ver
    );
}, 99);




