/* Enable UA adjustments for forms/scrollbars in both modes */
html { color-scheme: light dark; }

/* Base (Light) — matches your theme.json palette */
:root{
  --wp--preset--color--bg: #ffffff;
  --wp--preset--color--surface: #f5f5f7;
  --wp--preset--color--ink: #0a0a0a;
  --wp--preset--color--muted: #6e6e73;
  --wp--preset--color--accent: #0071e3; /* macOS/iOS blue */
  --dg-border: #e5e5ea;
}

/* Dark (Apple-ish) */
:root[data-theme="dark"]{
  --wp--preset--color--bg: #000000;        /* pure black for OLED look */
  --wp--preset--color--surface: #111113;   /* very dark gray for cards */
  --wp--preset--color--ink: #f5f5f7;       /* near-white text */
  --wp--preset--color--muted: #9a9aa0;     /* secondary text */
  --wp--preset--color--accent: #0a84ff;    /* iOS system blue */
  --dg-border: #2a2a2e;
}

/* Make sure the canvas follows the variables (your theme.json already maps background/text) */
body{
  background: var(--wp--preset--color--bg);
  color: var(--wp--preset--color--ink);
}

/* Subtle borders/separators */
hr, .wp-block-separator { color: var(--dg-border) !important; border-color: var(--dg-border) !important; }
.wp-block-group{ border-color: var(--dg-border); }

/* Links & buttons keep accent */
a { color: var(--wp--preset--color--accent); }
.wp-element-button { background: var(--wp--preset--color--accent); }

/* “Surface” blocks (cards/boxes) look good in both modes */
.dg-surface, .has-surface-background-color{
  background: var(--wp--preset--color--surface) !important;
}

/* Header adapts to theme */
.dg-header {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 999;
  height: 120px;
  width: 100%;
  background: var(--wp--preset--color--bg);
  border-bottom: 1px solid var(--dg-border);
  backdrop-filter: saturate(180%) blur(12px);
  
}

@media (max-width: 600px) {
  .dg-header {
    height: 80px;
  }

  .dgdb-wrap, .dgdb-profile-wrap, .dgdb-profile-compact, #af-wrapper, .dgdb-container, .dgdb-single-compact {
    padding-top: 80px;
  }
}


.dgdb-wrap, .dgdb-profile-wrap, .dgdb-profile-compact, #af-wrapper, .dgdb-container, .dgdb-single-compact {
  padding-top: 120px; /* Match the header height */
}



:root[data-theme="light"] .dg-header { background: rgba(255,255,255,0.8); }
:root[data-theme="dark"]  .dg-header { background: rgba(0,0,0,0.8); }

/* Header toggle chip */
.dg-header .dg-toggle {
  border: 1px solid var(--dg-border);
  background: var(--wp--preset--color--bg);
  color: var(--wp--preset--color--ink);
  padding: 6px 10px;
  border-radius: 12px;
  line-height: 1;
}

/* Fallback floating toggle (uppe till höger, tvinga bort ev. left) */
.dg-fab{
  position: fixed;
  top: 12px;
  right: 12px;
  left: auto !important;
  z-index: 9999;
  border: 1px solid var(--dg-border);
  background: var(--wp--preset--color--bg);
  color: var(--wp--preset--color--ink);
  padding: 6px 10px;
  border-radius: 12px;
  line-height: 1;
  cursor: pointer;
  backdrop-filter: saturate(180%) blur(12px);
}
:root[data-theme="light"] .dg-fab{ background: rgba(255,255,255,.9); }
:root[data-theme="dark"]  .dg-fab{ background: rgba(0,0,0,.9); }

.dg-header a.dg-theme-link{
  display:inline-block;
  border:1px solid var(--dg-border);
  background:var(--wp--preset--color--bg);
  color:var(--wp--preset--color--ink);
  padding:6px 10px;
  border-radius:12px;
  line-height:1;
  text-decoration:none;
}

.is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
    max-width: none !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

:where(.wp-site-blocks) > * {
    margin-block-start: 0!important;
    margin-block-end: 0;
}
