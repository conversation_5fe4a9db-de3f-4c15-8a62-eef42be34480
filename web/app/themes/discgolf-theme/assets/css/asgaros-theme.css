/* ==========================================================================
   DiscGolf Sweden — Asgaros “Apple clean” skin
   - <PERSON><PERSON><PERSON><PERSON> ditt data-theme (light/dark)
   - Lutar sig mot dina färgvariabler med vettiga fallbacks
   ========================================================================== */

/* ---------- Design tokens (fallbacks om inte redan satta av temat) -------- */
:root{
  --dg-bg: var(--wp--preset--color--bg, #ffffff);
  --dg-surface: var(--wp--preset--color--surface, #f5f5f7);
  --dg-ink: var(--wp--preset--color--ink, #111111);
  --dg-muted: var(--wp--preset--color--muted, #6e6e73);
  --dg-accent: var(--wp--preset--color--accent, #0a84ff);
  --dg-outline: color-mix(in oklab, var(--dg-ink) 10%, transparent);
  --dg-outline-strong: color-mix(in oklab, var(--dg-ink) 18%, transparent);
  --dg-shadow: 0 1px 0 rgba(0,0,0,.03), 0 6px 18px rgba(0,0,0,.06);
  color-scheme: light;
}
:root[data-theme="dark"]{
  --dg-bg: var(--wp--preset--color--bg, #0b0b0c);
  --dg-surface: var(--wp--preset--color--surface, #141416);
  --dg-ink: var(--wp--preset--color--ink, #f2f2f3);
  --dg-muted: var(--wp--preset--color--muted, #a1a1a6);
  --dg-accent: var(--wp--preset--color--accent, #0a84ff);
  --dg-outline: color-mix(in oklab, var(--dg-ink) 14%, transparent);
  --dg-outline-strong: color-mix(in oklab, var(--dg-ink) 22%, transparent);
  --dg-shadow: 0 1px 0 rgba(255,255,255,.03), 0 6px 18px rgba(0,0,0,.45);
  color-scheme: dark;
}

/* ---------- Bas: typografi, färger, distancing ---------------------------- */
#af-wrapper{
  font-family: ui-sans-serif, system-ui, -apple-system, "SF Pro Text", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 14px;
  line-height: 1.6;
  color: var(--dg-ink);
  text-shadow: none;
}
#af-wrapper a{
  color: var(--dg-accent);
  text-decoration: none;
}
#af-wrapper a:hover{ text-decoration: underline; }
#af-wrapper small{ color: var(--dg-muted); }
#af-wrapper .clear{ clear: both; }

/* ---------- Rubriker / breadcrumb / headerlinje --------------------------- */
#af-wrapper .main-title{
  font-size: 24px;
  font-weight: 600;
  color: var(--dg-ink);
  margin: 8px 0 16px;
  padding: 0;
  line-height: 1.25;
}
#af-wrapper #forum-breadcrumbs{
  color: var(--dg-muted);
  padding: 0 8px 8px;
  border-bottom: 1px solid var(--dg-outline);
}
#af-wrapper #forum-breadcrumbs a{ color: var(--dg-muted) !important; }
#af-wrapper #forum-breadcrumbs a:hover{ color: var(--dg-ink) !important; }

/* ---------- Översta forumbaren (navigation + sök) ------------------------- */
#af-wrapper #forum-header{
  background: var(--dg-surface);
  border: 1px solid var(--dg-outline);
  border-radius: 14px;
  min-height: 56px;
  padding: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: var(--dg-shadow);
}
#af-wrapper #forum-navigation,
#af-wrapper #forum-navigation-mobile{ display: inline-flex; gap: 2px; }
#af-wrapper #forum-navigation a,
#af-wrapper #forum-navigation-mobile a{
  color: var(--dg-ink) !important;
  border: 0;
  padding: 10px 14px;
  border-radius: 10px;
  line-height: 1;
}
#af-wrapper #forum-navigation a:hover{
  background: color-mix(in oklab, var(--dg-ink) 6%, transparent);
  text-decoration: none;
}
/* Sökfält */
#af-wrapper #forum-search{
  margin-left: auto;
  width: clamp(220px, 30vw, 340px);
  height: 40px;
  background: var(--dg-bg);
  color: var(--dg-ink);
  border: 1px solid var(--dg-outline);
  border-radius: 12px;
  display:flex; align-items:center; padding:0 10px;
}
#af-wrapper #forum-search input{
  width:100%; background:transparent; border:0; outline:0;
  color:var(--dg-ink); font-style: normal;
}
#af-wrapper #forum-search .search-icon{ color: var(--dg-muted); }

/* ---------- Sektionstitlar & tabellhuvuden -------------------------------- */
#af-wrapper .title-element,
#af-wrapper .forum-header,
#af-wrapper .topic-header,
#af-wrapper .section-header,
#af-wrapper .table thead th{
  background: var(--dg-surface) !important;
  color: var(--dg-ink) !important;
  border: 0 !important;
  border-bottom: 1px solid var(--dg-outline) !important;
  border-radius: 12px 12px 0 0;
  padding: 10px 14px;
}

/* ---------- “Kort” för listor (forumrader, ämnen, medlemmar) -------------- */
#af-wrapper .content-container,
#af-wrapper .editor-element{
  background: var(--dg-bg);
  border: 1px solid var(--dg-outline);
  border-radius: 14px;
  margin: 0 0 16px 0;
  box-shadow: var(--dg-shadow);
  overflow: hidden;
}
#af-wrapper .content-element{
  display: grid;
  grid-template-columns: 72px 1fr 320px;
  align-items: center;
  min-height: 84px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--dg-outline);
  background: transparent;
}
#af-wrapper .content-element:last-child{ border-bottom: 0; }
@media (max-width: 900px){
  #af-wrapper .content-element{ grid-template-columns: 56px 1fr; }
  #af-wrapper .forum-poster{ display:none !important; }
}
#af-wrapper .forum-status, #af-wrapper .topic-status{
  width:auto; text-align:center; color: var(--dg-muted);
}
#af-wrapper .forum-name, #af-wrapper .topic-name{
  padding-right: 8px;
}
#af-wrapper .forum-stats, #af-wrapper .topic-stats{
  color: var(--dg-muted);
}
#af-wrapper .forum-poster, #af-wrapper .topic-poster{
  border-left: 0;
  color: var(--dg-muted);
}

/* Små “chips” under titlar (subforums etc.) */
#af-wrapper .forum-subforums{
  border-top: 1px dashed var(--dg-outline);
  margin-top: 6px; padding-top: 6px;
}

/* ---------- Inlägg (post) -------------------------------------------------- */
#af-wrapper .post-element{
  background: var(--dg-bg);
  border: 1px solid var(--dg-outline);
  border-radius: 14px;
  box-shadow: var(--dg-shadow);
  overflow: hidden;
}
#af-wrapper .forum-post-header-container{
  background: var(--dg-surface);
  border-bottom: 1px solid var(--dg-outline);
  padding: 12px 16px;
}
#af-wrapper .post-author img.avatar{
  width:48px;height:48px;border-radius:50%; border:1px solid var(--dg-outline);
}
#af-wrapper .post-message{
  padding: 14px 16px 8px;
  color: var(--dg-ink);
}
#af-wrapper .post-message blockquote{
  background: var(--dg-surface);
  border: 1px solid var(--dg-outline);
  border-left: 4px solid var(--dg-accent);
  border-radius: 10px;
}

/* ---------- Inputs, selects, textarea ------------------------------------- */
#af-wrapper input[type="text"],
#af-wrapper input[type="email"],
#af-wrapper input[type="url"],
#af-wrapper input[type="search"],
#af-wrapper input[type="file"],
#af-wrapper select,
#af-wrapper textarea{
  background: var(--dg-bg);
  color: var(--dg-ink);
  border: 1px solid var(--dg-outline);
  border-radius: 12px;
  padding: 8px 10px;
  box-shadow: inset 0 1px 0 rgba(0,0,0,.02);
}
#af-wrapper input::placeholder{ color: var(--dg-muted); }

/* ---------- Knappar -------------------------------------------------------- */
#af-wrapper .button{
  appearance: none;
  border: 0;
  border-radius: 12px;
  padding: 8px 14px;
  font-weight: 600;
  line-height: 1;
  box-shadow: none;
}
#af-wrapper .button-normal{ background: var(--dg-accent); color: #fff; }
#af-wrapper .button-red{ background: #ff453a; color:#fff; }
#af-wrapper .button-green{ background: #34c759; color:#fff; }
#af-wrapper .button-neutral{
  background: color-mix(in oklab, var(--dg-accent) 15%, transparent);
  color: var(--dg-accent);
}
#af-wrapper .button:hover{ filter: brightness(.96); text-decoration:none; }

/* ---------- Pagination ----------------------------------------------------- */
#af-wrapper .pages{
  display: inline-flex;
  gap: 6px;
  padding: 4px;
  background: var(--dg-surface);
  border: 1px solid var(--dg-outline);
  border-radius: 12px;
}
#af-wrapper .pages a, #af-wrapper .pages strong{
  padding: 6px 10px;
  border: 0;
  border-radius: 8px;
  color: var(--dg-ink) !important;
}
#af-wrapper .pages strong,
#af-wrapper .pages a:hover{
  background: var(--dg-bg);
  box-shadow: inset 0 0 0 1px var(--dg-outline);
}

/* ---------- Notices / errors ---------------------------------------------- */
#af-wrapper .notices-panel{
  background: color-mix(in oklab, #ffd60a 14%, var(--dg-bg));
  border: 1px solid color-mix(in oklab, #ffd60a 40%, transparent);
  color: color-mix(in oklab, var(--dg-ink) 70%, #8a6d00);
  border-radius: 12px;
}
#af-wrapper .error{
  background: color-mix(in oklab, #ff453a 12%, var(--dg-bg));
  border: 1px solid color-mix(in oklab, #ff453a 38%, transparent);
  color: #ff453a;
  border-radius: 12px;
}

/* ---------- “Läs/oläst” indikator ---------------------------------------- */
#af-wrapper #read-unread{
  background: transparent;
  color: var(--dg-ink);
}
#af-wrapper #read-unread .indicator{ box-shadow: inset 0 0 0 1px var(--dg-outline); }
#af-wrapper #read-unread .unread{ background: var(--dg-accent); }
#af-wrapper #read-unread .read{ background: var(--dg-muted); }

/* ---------- Statistik / boxar --------------------------------------------- */
#af-wrapper #statistics,
#af-wrapper #statistics-body{
  background: var(--dg-bg);
  border: 1px solid var(--dg-outline);
  border-radius: 14px;
}
#af-wrapper #statistics-online-users{ background: var(--dg-surface); }

/* ---------- Profilsidor ---------------------------------------------------- */
#af-wrapper #profile-header{
  border: 1px solid var(--dg-outline);
}
#af-wrapper #profile-navigation{
  background: var(--dg-surface);
}
#af-wrapper #profile-navigation a{
  color: var(--dg-ink) !important;
  padding: 0 18px;
}
#af-wrapper #profile-navigation a.active{
  background: color-mix(in oklab, var(--dg-ink) 8%, transparent);
}
#af-wrapper #profile-layer,
#af-wrapper #profile-content{
  background: var(--dg-bg);
  border: 1px solid var(--dg-outline);
  border-radius: 12px;
}

/* ---------- Spoilers / modals --------------------------------------------- */
#af-wrapper .spoiler{
  border: 1px solid var(--dg-outline);
  border-radius: 12px;
}
#af-wrapper .spoiler .spoiler-head{ background: var(--dg-surface); }
#af-wrapper .modal-layer{ background: rgba(0,0,0,.55); }
#af-wrapper .modal-content{
  background: var(--dg-bg);
  color: var(--dg-ink);
  border-radius: 14px;
  border: 1px solid var(--dg-outline);
}

/* ---------- Ikoner, badge mm. --------------------------------------------- */
#af-wrapper .badge,
#af-wrapper .status,
#af-wrapper .sticky,
#af-wrapper .closed{
  border-radius: 999px;
  border: 1px solid var(--dg-accent);
  color: var(--dg-accent);
  background: color-mix(in oklab, var(--dg-accent) 12%, transparent);
}

/* ---------- Småjusteringar ------------------------------------------------- */
#af-wrapper .content-element:nth-child(even){ background: transparent; } /* stäng av randning */
#af-wrapper .forum .forum-title{ font-weight: 600; }
#af-wrapper .topic-sticky{ background: color-mix(in oklab, var(--dg-accent) 6%, transparent) !important; }
#af-wrapper #forum-breadcrumbs {
    display: none;
}
#af-wrapper .main-title {
    display: none;
}

/* ===== NAV: desktop vs mobil ===== */

/* default = DESKTOP */
#af-wrapper #forum-navigation{ display:inline-flex; gap:2px; }
#af-wrapper #forum-navigation-mobile{ display:none; }

/* MOBIL (<= 782px: Asgaros egen brytpunkt) */
@media (max-width: 782px){
  /* visa mobil-länkar, dölj desktop */
  #af-wrapper #forum-navigation{ display:none; }
  #af-wrapper #forum-navigation-mobile{
    display:block;
    width:100%;
  }

  /* gör headern kompakt & i kolumn */
  #af-wrapper #forum-header{
    flex-wrap:wrap;
    gap:10px;
    padding:8px;
  }
  #af-wrapper #forum-search{ width:100%; order:3; }
}

/* ===== TITEL + LIST-KORT: snygg kant och sömlös övergång ===== */

/* Gör titel till "kort-huvud" */
#af-wrapper .title-element,
#af-wrapper .forum-header,
#af-wrapper .topic-header,
#af-wrapper .section-header{
  background: var(--dg-surface) !important;
  border: 1px solid var(--dg-outline) !important;
  border-bottom: 0 !important;                 /* inga dubbla linjer */
  border-radius: 14px 14px 0 0 !important;      /* topprunda hörn */
  margin: 16px 0 0 0;
  padding: 10px 14px;
}

/* Själva listan fortsätter kortet utan avbrott */
#af-wrapper .title-element + .content-container,
#af-wrapper .forum-header + .content-container,
#af-wrapper .topic-header + .content-container,
#af-wrapper .section-header + .content-container{
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-top: 1px solid var(--dg-outline); /* säkerhetslinje om tema saknar egen */
}

/* Sista raden i listan får bottenhörn (container har overflow:hidden, men säkerställ) */
#af-wrapper .content-container{ overflow:hidden; border-radius: 0 0 14px 14px; }

/* ===== LISTRADER/KORT (forum, ämnen, medlemmar) ===== */

/* Desktop: tre kolumner */
#af-wrapper .content-element{
  display:grid;
  grid-template-columns: 72px 1fr 320px;
  gap: 0 8px;
}

/* Tablet/Mobil: två kolumner + lite mer padding */
@media (max-width: 1024px){
  #af-wrapper .content-element{ grid-template-columns: 56px 1fr; min-height:72px; }
  #af-wrapper .forum-poster, 
  #af-wrapper .topic-poster{ display:none !important; }
}

/* Låt första och sista rad få lite extra luft (ser mer “Apple” ut) */
#af-wrapper .content-element:first-child{ padding-top:14px; }
#af-wrapper .content-element:last-child{ padding-bottom:14px; }

/* ===== SÖK & KNAPPAR små justeringar ===== */
#af-wrapper #forum-search{
  height: 40px;
  border-radius: 12px;
  padding: 0 10px;
}

/* Primärknappar lite rundare på små skärmar */
@media (max-width: 782px){
  #af-wrapper .button{ border-radius: 14px; }
}

/* ===== Små beautifiers ===== */

/* När man inte har titel före lista: behåll full radie på list-kortet */
#af-wrapper .content-container:not(:has(+ .content-container)){
  border-radius: 14px;
}

/* Tunnare radseparator */
#af-wrapper .content-element{ border-bottom: 1px solid var(--dg-outline); }
#af-wrapper .content-element:last-child{ border-bottom: 0; }

/* ===== TITEL + LIST-KORT: snygg kant och sömlös övergång ===== */
#af-wrapper .title-element,
#af-wrapper .forum-header,
#af-wrapper .topic-header,
#af-wrapper .section-header {
  background: var(--dg-surface) !important;
  border: 1px solid var(--dg-outline) !important;
  border-bottom: 0 !important;               
  border-radius: 14px 14px 0 0 !important;    /* bara topp */
  margin: 16px 0 0 0;
  padding: 10px 14px;
}

/* Se till att första content-container “sitter ihop” */
#af-wrapper .title-element + .content-container,
#af-wrapper .forum-header + .content-container,
#af-wrapper .topic-header + .content-container,
#af-wrapper .section-header + .content-container {
  border-top: 0 !important;                   /* ta bort extra linje */
  border-radius: 0 0 14px 14px;               /* bottenradie */
  margin-top: 0;
}

/* === Statistics Card === */
#af-wrapper #statistics {
  margin: 20px 0;
  border: 1px solid var(--dg-outline);
  border-radius: 14px;
  background: var(--dg-bg);
  overflow: hidden; /* gör att innerdivar inte sticker ut */
}

/* Title (Statistics header) */
#af-wrapper #statistics .title-element {
  background: var(--dg-surface);
  border-bottom: 1px solid var(--dg-outline);
  border-radius: 14px 14px 0 0;
  font-weight: 600;
  padding: 10px 14px;
  margin: 0;
}

/* Body (numbers, users etc.) */
#af-wrapper #statistics-body {
  display: flex;
  justify-content: space-around;
  border: none;
  background: var(--dg-bg);
  padding: 12px;
  font-size: 14px;
  text-align: center;
}

/* Elements inside statistics */
#af-wrapper .statistics-element {
  flex: 1;
  border-right: 1px solid var(--dg-outline);
  padding: 8px;
}
#af-wrapper .statistics-element:last-child {
  border-right: none;
}

/* Online users row */
#af-wrapper #statistics-online-users {
  background: var(--dg-surface);
  border-top: 1px solid var(--dg-outline);
  padding: 10px 14px;
  font-size: 13px;
  color: var(--dg-muted);
  border-radius: 0 0 14px 14px;
}

#af-wrapper #statistics-online-users {
    display: none;
}