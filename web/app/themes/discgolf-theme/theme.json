{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 3, "settings": {"appearanceTools": true, "custom": {"radius": {"sm": "8px", "md": "12px", "lg": "20px", "xl": "28px"}, "shadow": {"sm": "0 1px 3px rgba(0,0,0,.04)", "md": "0 6px 18px rgba(0,0,0,.06)"}}, "spacing": {"units": ["px", "rem", "em", "%"], "blockGap": "1.2rem"}, "color": {"palette": [{"slug": "bg", "name": "Background", "color": "#ffffff"}, {"slug": "surface", "name": "Surface", "color": "#f5f5f7"}, {"slug": "ink", "name": "Ink", "color": "#0a0a0a"}, {"slug": "muted", "name": "Muted", "color": "#6e6e73"}, {"slug": "accent", "name": "Accent", "color": "#0071e3"}]}, "typography": {"fluid": true, "fontFamilies": [{"name": "System SF", "slug": "system-sf", "fontFamily": "-apple-system, BlinkMacSystemFont, 'SF Pro Text', 'SF Pro Display', 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif"}, {"name": "SF Mono (fallbacks)", "slug": "sf-mono", "fontFamily": "'SF Mono', ui-monospace, 'SFMono-Regular', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, 'Liberation Mono', monospace"}], "fontSizes": [{"slug": "xs", "size": "12px"}, {"slug": "sm", "size": "14px"}, {"slug": "base", "size": "16px"}, {"slug": "lg", "size": "18px"}, {"slug": "xl", "size": "22px"}, {"slug": "2xl", "size": "26px"}, {"slug": "3xl", "size": "34px"}, {"slug": "4xl", "size": "44px"}, {"slug": "5xl", "size": "64px"}], "lineHeight": "1.5"}}, "styles": {"color": {"text": "var(--wp--preset--color--ink)", "background": "var(--wp--preset--color--bg)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--system-sf)", "fontSize": "var(--wp--preset--font-size--base)"}, "elements": {"heading": {"typography": {"fontWeight": "600", "lineHeight": "1.2"}, "color": {"text": "var(--wp--preset--color--ink)"}}, "link": {"color": {"text": "var(--wp--preset--color--accent)"}}}, "blocks": {"core/group": {"spacing": {"padding": {"top": "1rem", "bottom": "1rem"}}}, "core/cover": {"border": {"radius": "var(--wp--custom--radius--lg)"}, "shadow": "var(--wp--custom--shadow--md)"}, "core/navigation": {"typography": {"fontSize": "var(--wp--preset--font-size--lg)"}}, "core/button": {"typography": {"fontWeight": "600"}, "border": {"radius": "12px"}, "spacing": {"padding": {"top": "10px", "right": "16px", "bottom": "10px", "left": "16px"}}}, "core/separator": {"color": {"text": "var(--wp--preset--color--surface)"}}}}, "templates": {"front-page": "templates/front-page", "home": "templates/index", "index": "templates/index"}, "templateParts": {"header": {"area": "header", "title": "Header", "slug": "header"}, "footer": {"area": "footer", "title": "Footer", "slug": "footer"}}}